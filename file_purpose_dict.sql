
alter table doc_modify_apply
  add file_purpose varchar(50) null comment '文件用途',
  add regulation_standard_status varchar(50) null comment '法规标准状态',
  add regulation_publish_date date null comment '法规/标准发布日期',
  add regulation_implement_date date null comment '法规/标准实施日期';

alter table doc_version
  add file_purpose varchar(50) null comment '文件用途',
  add regulation_standard_status varchar(50) null comment '法规标准状态',
  add regulation_publish_date date null comment '法规/标准发布日期',
  add regulation_implement_date date null comment '法规/标准实施日期',
    add review_record_file_ids varchar(500) null comment '评审记录文件id';

alter table doc_review_apply
  add review_record_file_ids varchar(500) null comment '评审记录文件id';


-- 1. 创建父级字典类型：文件用途
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark, tenant_id)
VALUES ('文件用途', 'file_purpose_type', '0', 'admin', NOW(), '', null, '文件用途列表', 'dc41618350206272c0b3271ccb9c3c76');

-- 2. 创建子级字典数据：文件用途选项
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark, customer_id, tenant_id)
VALUES
-- 产品量产测试（默认选项）
(1, '产品量产测试', 'mass_production_test', 'file_purpose_type', '', '', 'Y', '0', 'admin', NOW(), '', null, '产品量产测试用途', null, 'dc41618350206272c0b3271ccb9c3c76'),
-- 产品试产测试
(2, '产品试产测试', 'trial_production_test', 'file_purpose_type', '', '', 'N', '0', 'admin', NOW(), '', null, '产品试产测试用途', null, 'dc41618350206272c0b3271ccb9c3c76'),
-- 产品试模
(3, '产品试模', 'product_trial_mold', 'file_purpose_type', '', '', 'N', '0', 'admin', NOW(), '', null, '产品试模用途', null, 'dc41618350206272c0b3271ccb9c3c76'),
-- 产品打样
(4, '产品打样', 'product_sampling', 'file_purpose_type', '', '', 'N', '0', 'admin', NOW(), '', null, '产品打样用途', null, 'dc41618350206272c0b3271ccb9c3c76');

-- 1. 创建父级字典类型：法规/标准状态
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark, tenant_id)
VALUES ('法规/标准状态', 'regulation_standard_status', '0', 'admin', NOW(), '', null, '法规/标准状态列表', 'dc41618350206272c0b3271ccb9c3c76');

-- 2. 创建子级字典数据：法规/标准状态选项
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark, customer_id, tenant_id)
VALUES
-- 现行（默认选项）
(1, '现行', 'current', 'regulation_standard_status', '', '', 'Y', '0', 'admin', NOW(), '', null, '现行状态', null, 'dc41618350206272c0b3271ccb9c3c76'),
-- 即将实施
(2, '即将实施', 'upcoming', 'regulation_standard_status', '', '', 'N', '0', 'admin', NOW(), '', null, '即将实施状态', null, 'dc41618350206272c0b3271ccb9c3c76');


INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark, customer_id, tenant_id) VALUES (0, '相关部门会签人员', 'relevant_user', 'flow_node_fun_list', null, 'default', 'N', '0', 'adminmh', '2025-07-21 17:34:43', 'adminmh', '2025-07-21 17:36:33', '相关部门会签人员', null, 'dc41618350206272c0b3271ccb9c3c76');



INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark, tenant_id) VALUES ('评审记录', 'review_record', '0', 'adminmh', '2025-07-22 14:29:28', 'adminmh', '2025-07-22 14:29:28', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark, customer_id, tenant_id) VALUES (0, '质量管理手册', '1791269862321500162', 'review_record', null, 'default', 'N', '0', 'adminmh', '2025-07-22 14:34:40', 'adminmh', '2025-07-22 14:59:29', null, null, 'dc41618350206272c0b3271ccb9c3c76');


INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark, customer_id, tenant_id) VALUES (0, '下载履历', 'download_resume', 'flow_node_fun_list', null, 'default', 'N', '0', 'adminmh', '2025-07-23 14:22:55', 'adminmh', '2025-07-23 14:22:55', '下载履历', null, 'dc41618350206272c0b3271ccb9c3c76');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark, customer_id, tenant_id) VALUES (0, '源文件下载', 'download_source_file', 'flow_node_fun_list', null, 'default', 'N', '0', 'adminmh', '2025-07-23 15:04:17', 'adminmh', '2025-07-23 15:04:17', null, null, 'dc41618350206272c0b3271ccb9c3c76');


INSERT INTO  sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark, tenant_id) VALUES ('过期状态', 'expired_status', '0', 'adminmh', '2025-07-25 09:15:05', 'adminmh', '2025-07-25 09:15:05', null, null);
INSERT INTO  sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark, customer_id, tenant_id) VALUES (0, '正常', 'normal', 'expired_status', null, 'primary', 'N', '0', 'adminmh', '2025-07-25 09:15:36', 'adminmh', '2025-07-25 09:16:06', null, null, 'dc41618350206272c0b3271ccb9c3c76');
INSERT INTO  sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark, customer_id, tenant_id) VALUES (0, '失效', 'failure', 'expired_status', null, 'danger', 'N', '0', 'adminmh', '2025-07-25 09:15:58', 'adminmh', '2025-07-25 09:15:58', null, null, 'dc41618350206272c0b3271ccb9c3c76');



-- 创建字典类型：文件类型
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark, tenant_id)
VALUES ('文件类型', 'file_type', '0', 'adminmh', '2025-07-25 09:15:05', 'adminmh', '2025-07-25 09:15:05', null, null);

-- 创建字典数据：文件类型选项
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark, customer_id, tenant_id)
VALUES (0, '工程样板', 'G', 'file_type', null, 'success', 'N', '0', 'adminmh', '2025-07-25 09:15:36', 'adminmh', '2025-07-25 09:16:06', null, null, 'dc41618350206272c0b3271ccb9c3c76');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark, customer_id, tenant_id)
VALUES (1, '临时文件', 'Y', 'file_type', null, 'warning', 'N', '0', 'adminmh', '2025-07-25 09:15:58', 'adminmh', '2025-07-25 09:15:58', null, null, 'dc41618350206272c0b3271ccb9c3c76');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark, customer_id, tenant_id)
VALUES (2, '无', 'N', 'file_type', null, 'info', 'N', '0', 'adminmh', '2025-07-25 09:15:58', 'adminmh', '2025-07-25 09:15:58', null, null, 'dc41618350206272c0b3271ccb9c3c76');


INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark, customer_id, tenant_id) VALUES (0, '过期日期', 'shelf_life', 'watermark_type', null, 'default', 'N', '0', 'adminmh', '2025-07-29 09:06:14', 'adminmh', '2025-07-29 09:06:14', null, null, 'dc41618350206272c0b3271ccb9c3c76');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, tenant_id) VALUES ('过期文件', 2168, 3, 'expired/index', 'document_account/expired/index', '{"dataType":"stdd","status":"1"}', 1, 0, 'C', '0', '0', 'process:gs:expired:list', '#', 'admin', '2021-12-22 15:00:26', 'admin', '2023-07-05 10:36:31', '', 'dc41618350206272c0b3271ccb9c3c76');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, tenant_id) VALUES ('项目人员设置', 1, 24, 'projectPerson', 'system/projectPerson/index', null, 1, 0, 'C', '0', '0', 'system:notifyTemplate:list', 'guide', 'adminsl', '2024-04-18 11:07:37', 'adminsl', '2024-04-18 11:16:08', '', 'dc41618350206272c0b3271ccb9c3c76');



create table basic_project_person
(
  id           varchar(50)          not  null
    primary key,
  project_code varchar(50)            not null comment '项目编码',
  users        text                   not null comment '人员配置',
  create_by    varchar(64) default '' null comment '创建者',
  create_time  datetime               null comment '创建时间',
  update_by    varchar(64) default '' null comment '更新者',
  update_time  datetime               null comment '更新时间'
)
  comment '项目人员配置';

