import request from '@/utils/request'

// 查询项目文件基线申请列表
export function listBaseApply(query) {
  return request({
    url: '/process/baseApply/list',
    method: 'get',
    params: query
  })
}

// 查询项目文件基线申请详细
export function getBaseApply(id) {
  return request({
    url: '/process/baseApply/' + id,
    method: 'get'
  })
}

export function getInfoByBpmnId(bpmnId) {
  return request({
    url: '/process/baseApply/bpmnId/' + bpmnId,
    method: 'get'
  })
}

// 新增项目文件基线申请
export function addBaseApply(data) {
  return request({
    url: '/process/baseApply',
    method: 'post',
    data: data
  })
}

// 修改项目文件基线申请
export function updateBaseApply(data) {
  return request({
    url: '/process/baseApply',
    method: 'put',
    data: data
  })
}

// 删除项目文件基线申请
export function delBaseApply(id) {
  return request({
    url: '/process/baseApply/' + id,
    method: 'delete'
  })
}
