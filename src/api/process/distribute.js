import request from '@/utils/request'

export function pageDistribute(query) {
  return request({
    url: '/process/distribute/page',
    method: 'get',
    params: query
  })
}

/** 查询阅读同意数据列表 **/
export function readTrainPage(query) {
  return request({
    url: '/process/distribute/read/train/page',
    method: 'post',
    data: query
  })
}

export function pageDoc(query) {
  return request({
    url: '/process/distribute/page/doc',
    method: 'get',
    params: query
  })
}

//权限过滤分页查询
export function authFilterPageDoc(query) {
  return request({
    url: '/process/distribute/auth/filter/page/doc',
    method: 'get',
    params: query
  })
}

// 查询文件分发明细列表
export function listDistribute(query) {
  return request({
    url: '/process/distribute/list',
    method: 'get',
    params: query
  })
}


// 权限过滤查询文件分发明细列表
export function authFilterListDistribute(query) {
  return request({
    url: '/process/distribute/auth/filter/list',
    method: 'get',
    params: query
  })
}

// 查询文件分发明细列表
export function postListDistribute(query) {
  return request({
    url: '/process/distribute/postList',
    method: 'post',
    data: query
  })
}

export function listPrintGroup(query) {
  return request({
    url: '/process/distribute/list/print/group',
    method: 'get',
    params: query
  })
}


export function updateList(data) {
  return request({
    url: '/process/distribute/update/list',
    method: 'post',
    data: data
  })
}

export function updateListCode(data) {
  return request({
    url: '/process/distribute/update/code',
    method: 'post',
    data: data
  })
}

export function receiveByIds(ids) {
  return request({
    url: '/process/distribute/receive/'+ids,
    method: 'get',
  })
}

export function recoveryByIds(ids) {
  return request({
    url: '/process/distribute/recovery/'+ids,
    method: 'get',
  })
}

export function lostByIds(ids) {
  return request({
    url: '/process/distribute/lost/'+ids,
    method: 'get',
  })
}

export function sendRecoveryEMail(ids) {
  return request({
    url: '/process/distribute/sendRecoveryEMail/'+ids,
    method: 'get',
  })
}


// 查询文件分发明细详细
export function getDistribute(id) {
  return request({
    url: '/process/distribute/' + id,
    method: 'get'
  })
}

// 新增文件分发明细
export function addDistribute(data) {
  return request({
    url: '/process/distribute',
    method: 'post',
    data: data
  })
}

// 修改文件分发明细
export function updateDistribute(data) {
  return request({
    url: '/process/distribute',
    method: 'put',
    data: data
  })
}

// 删除文件分发明细
export function delDistribute(id) {
  return request({
    url: '/process/distribute/' + id,
    method: 'delete'
  })
}


//详情集合
export function trainDetailList(query) {
  return request({
    url: '/process/distribute/trainDetailList',
    method: 'get',
    params: query
  })
}

export function trainDetailStatusList(query) {
  return request({
    url: '/process/distribute/trainDetailStatusList',
    method: 'get',
    params: query
  })
}
