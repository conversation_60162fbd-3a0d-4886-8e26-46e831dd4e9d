import request from '@/utils/request'

// 获取业务类型流程模板
export function queryFlowList(params) {
  return request({
    url: `/setting/docClassFlow/queryFlowList`,
    method: 'get',
    params:params
  })
}

// 根据文件分类和业务类型，向上级联获取文件分类流程配置
export function getByUpDocClassAndBizType(docClass,bizType) {
  return request({
    url: `/setting/docClassFlow/getByUpDocClassAndBizType/${docClass}/${bizType}`,
    method: 'get'
  })
}

// 根据文件分类获取分类的所有流程设置信息
export function getByDocClass(docClass) {
  return request({
    url: '/setting/docClassFlow/docClass/'+docClass,
    method: 'get'
  })
}

// 查询文件分类设置-流程设置列表
export function listDocClassFlow(query) {
  return request({
    url: '/setting/docClassFlow/list',
    method: 'get',
    params: query
  })
}

// 查询文件分类设置-流程设置详细
export function getDocClassFlow(id) {
  return request({
    url: '/setting/docClassFlow/' + id,
    method: 'get'
  })
}

// 新增文件分类设置-流程设置
export function saveDocClassFlow(data) {
  return request({
    url: '/setting/docClassFlow/save',
    method: 'post',
    data: data
  })
}

// 新增文件分类设置-流程设置
export function addDocClassFlow(data) {
  return request({
    url: '/setting/docClassFlow',
    method: 'post',
    data: data
  })
}

// 修改文件分类设置-流程设置
export function updateDocClassFlow(data) {
  return request({
    url: '/setting/docClassFlow',
    method: 'put',
    data: data
  })
}

// 删除文件分类设置-流程设置
export function delDocClassFlow(id) {
  return request({
    url: '/setting/docClassFlow/' + id,
    method: 'delete'
  })
}
