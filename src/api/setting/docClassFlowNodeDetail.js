import request from '@/utils/request'

// 查询文件分类设置-节点明细设置列表
export function getInfo(docClass,bizType,nodeCode) {
  return request({
    url: `/setting/docClassFlowNodeDetail/getAuth/${docClass}/${bizType}/${nodeCode}`,
    method: 'get',
  })
}

export function getInfoByType(bizType,nodeCode) {
  return request({
    url: `/setting/docClassFlowNodeDetail/getInfoByType/${bizType}/${nodeCode}`,
    method: 'get',
  })
}

export function getInfoByTypeDetail(bizType) {
  return request({
    url: `/setting/docClassFlowNodeDetail/getInfoByTypeDetail/${bizType}`,
    method: 'get',
  })
}

// 查询文件分类设置-节点明细设置列表
export function listDocClassFlowNodeDetail(query) {
  return request({
    url: '/setting/docClassFlowNodeDetail/list',
    method: 'get',
    params: query
  })
}

// 查询文件分类设置-节点明细设置详细
export function getDocClassFlowNodeDetail(id) {
  return request({
    url: '/setting/docClassFlowNodeDetail/' + id,
    method: 'get'
  })
}

// 新增文件分类设置-节点明细设置
export function addDocClassFlowNodeDetail(data) {
  return request({
    url: '/setting/docClassFlowNodeDetail',
    method: 'post',
    data: data
  })
}

// 修改文件分类设置-节点明细设置
export function updateDocClassFlowNodeDetail(data) {
  return request({
    url: '/setting/docClassFlowNodeDetail',
    method: 'put',
    data: data
  })
}

// 删除文件分类设置-节点明细设置
export function delDocClassFlowNodeDetail(id) {
  return request({
    url: '/setting/docClassFlowNodeDetail/' + id,
    method: 'delete'
  })
}
