import request from '@/utils/request'

// 查询文件类型列表
export function listDocClass(query) {
  return request({
    url: '/setting/docClass/list',
    method: 'get',
    params: query
  })
}

// 查询文件类型详细
export function getDocClass(id) {
  return request({
    url: '/setting/docClass/' + id,
    method: 'get'
  })
}

// 新增文件类型
export function addDocClass(data) {
  return request({
    url: '/setting/docClass',
    method: 'post',
    data: data
  })
}

// 修改文件类型
export function updateDocClass(data) {
  return request({
    url: '/setting/docClass',
    method: 'put',
    data: data
  })
}

// 删除文件类型
export function delDocClass(id) {
  return request({
    url: '/setting/docClass/' + id,
    method: 'delete'
  })
}

export function initAncestors() {
  return request({
    url: '/setting/docClass/init/ancestors',
    method: 'get'
  })
}
