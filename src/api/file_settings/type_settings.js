import request from '@/utils/request'

// 查询列表
export function settingDocClassList(query) {
    return request({
        url: '/setting/docClass/list',
        method: 'get',
        params: query
    })
}
// 查询列表
export function settingDocClassSort(query) {
    return request({
        url: '/setting/docClass/sort/' + query,
        method: 'get',
    })
}
export function settingDocClassId(query) {
    return request({
        url: '/setting/docClass/' + query,
        method: 'get',

    })
}
export function settingdocClassMergedelete(id) {
    return request({
        url: '/setting/docClassMerge/delete/' + id,
        method: 'get',

    })
}
export function settingdocClassSignaturedelete(id) {
    return request({
        url: '/setting/docClassSignature/delete/' + id,
        method: 'get',

    })
}
export function settingDocClassIsExistByCode(query) {
    return request({
        url: '/setting/docClass/isExistByCode/' + query,
        method: 'get',
    })
}

export function settingDocClassIsExistByName(query) {
    return request({
        url: '/setting/docClass/isExistByName/' + query,
        method: 'get',
    })
}

export function settingDocClassIds(postId) {
    return request({
        url: '/setting/docClass/' + postId,
        method: 'delete'
    })
}


// 新增
export function settingDocClass(data) {
    return request({
        url: '/setting/docClass',
        method: 'post',
        data: data
    })
}

// 修改
export function settingDocClassUpdata(data) {
    return request({
        url: '/setting/docClass',
        method: 'put',
        data: data
    })
}

export function settingDocClassTreeseList(data) {
    return request({
        url: '/setting/docClass/treeseList',
        method: 'post',
        data: data
    })
}

export function settingDocClassValidateZf(data) {
    return request({
        url: '/setting/docClass/validateZf',
        method: 'post',
        data: data
    })
}
export function settingDocClassValidateSubmit(data) {
    return request({
        url: '/setting/docClass/validateSubmit',
        method: 'post',
        data: data
    })
}
