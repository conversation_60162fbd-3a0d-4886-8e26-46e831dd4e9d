import request from '@/utils/request'

// 查询标准文件列表
export function queryPageFavorites(query) {
  return request({
    url: '/process/versionFavorites/page',
    method: 'get',
    params: query
  })
}

export function queryPageFavoritesRecordFile(query) {
  return request({
    url: '/process/versionFavorites/page/record',
    method: 'get',
    params: query
  })
}

// 新增我的收藏
export function saveOrRemoveFavorites(id) {
  return request({
    url: '/process/versionFavorites/saveOrRemove/'+id,
    method: 'get',
  })
}

// 删除我的收藏
export function delVersionFavorites(id) {
  return request({
    url: '/process/versionFavorites/remove/' + id,
    method: 'get'
  })
}
