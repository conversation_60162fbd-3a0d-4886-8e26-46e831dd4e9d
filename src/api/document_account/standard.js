import request from '@/utils/request'

// 查询标准文件列表
export function listStandard(query) {
  return request({
    url: '/process/standard/list',
    method: 'get',
    params: query
  })
}
// 查询标准文件列表
export function queryPageByThisDept(query) {
  return request({
    url: '/process/distributeLog/queryPageByThisDept',
    method: 'get',
    params: query
  })
}
// 查询标准文件列表
export function queryPageByOtherDept(query) {
  return request({
    url: '/process/distributeLog/queryPageByOtherDept',
    method: 'get',
    params: query
  })
}

export function modifyApplyLinklist(query) {
  return request({
    url: '/process/modifyApplyLink/list',
    method: 'get',
    params: query
  })
}
export function versionpagerecord(query) {
  return request({
    url: '/process/version/page/record',
    method: 'get',
    params: query
  })
}

// 获取标准文件全部的详细信息
export function standardGetDetail(query) {
  return request({
    url: '/process/standard/getDetail',
    method: 'get',
    params: query
  })
}

// 查询标准文件详细
export function getStandard(id) {
  return request({
    url: '/process/standard/' + id,
    method: 'get'
  })
}

// 新增标准文件
export function addStandard(data) {
  return request({
    url: '/process/standard',
    method: 'post',
    data: data
  })
}

// 修改标准文件
export function updateStandard(data) {
  return request({
    url: '/process/standard',
    method: 'put',
    data: data
  })
}

// 删除标准文件
export function delStandard(id) {
  return request({
    url: '/process/standard/' + id,
    method: 'delete'
  })
}

// 判断文件名称是否存在 true 存在 false 不存在
export function isExistByName(query) {
  return request({
    url: '/process/standard/isExistByName',
    method: 'get',
    params: query,
  })
}
