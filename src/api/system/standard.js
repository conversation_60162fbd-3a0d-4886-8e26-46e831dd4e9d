import request from '@/utils/request'

// 查询初始化插入的数据
export function standardList(query) {
    return request({
        url: '/process/standard/list',
        method: 'get',
        params: query
    })
}

// 查询文件台账列表 doc_standard
export function accountList(query) {
    return request({
        url: '/process/standard/listAccount',
        method: 'get',
        params: query
    })
}

// 判断编号是否存在
export function checkNoIsExist(data) {
    return request({
        url: '/process/modifyApply/check/docId',
        method: 'post',
        data: data
    })
}

export function standardUpload(data) {
    return request({
        url: '/process/standard/upload',
        method: 'post',
        data: data
    })
}


// 查询单条数据详情
export function processstandard(id) {
    return request({
        url: '/process/standard/' + id,
        method: 'get'
    })
}

// 修改单条数据
export function standardUpdate(data) {
    return request({
        url: '/process/standard/update',
        method: 'post',
        data: data
    })
}

// 删除单条数据
export function standarddelete(data) {
    return request({
        url: '/process/standard/delete',
        method: 'post',
        data: data
    })
}

//替换文件
export function standardReplaceFile(data) {
    return request({
        url: '/process/standard/replace/file',
        method: 'post',
        data: data,
        timeout: 10*60*1000
    })
}
/**
 * 替换文件
 */
export function standardReplaceFile2(data) {
    return request({
        url: '/process/standard/replace-file',
        method: 'POST',
        data,
        timeout: 10 * 60 * 1000
    })
}
//关联记录文件
export function standardRecordFile(data) {
    return request({
        url: '/process/standard/record/file',
        method: 'post',
        data: data,
        timeout: 10*60*1000
    })
}
// 批量删除数据
export function standardDeleteBatch(ids) {
    return request({
        url: '/process/standard/delete/' + ids,
        method: 'get'
    })
}
// 批量生效
export function standardUpdateStatus(status,ids) {
    return request({
        url: '/process/standard/update/status/'+ status +'/' + ids,
        method: 'get',
        timeout: 30*60*1000
    })
}

export function standardUpdateByParam(status,ids) {
  return request({
    url: '/process/standard/update/status',
    method: 'post',
    data:{"status":status,"ids":ids},
    timeout: 30*60*1000
  })
}
//查询关联记录
export function linkLogList(query) {
    return request({
        url: '/process/linkLog/list',
        method: 'get',
        params: query
    })
}

export function selectLinkList(query) {
  return request({
    url: '/process/linkLog/link/list',
    method: 'get',
    params: query
  })
}


//加入关联记录
export function standardJoin(data) {
    return request({
        url: '/process/standard/join',
        method: 'post',
        data: data
    })
}

export function standardimport(data) {
    return request({
        url: '/process/standard/import',
        method: 'post',
        data: data,
        timeout: 30*60*1000
    })
}
// 删除关联记录
export function linkLogDelete(id) {
    return request({
        url: '/process/linkLog/delete/' + id,
        method: 'get'
    })
}
// 修改关联记录
export function linkLogUpdate(data) {
    return request({
        url: '/process/linkLog',
        method: 'put',
        data: data
    })
}
// 刷新文件台账数据
export function refreshDocAccount(stddardId) {
    return request({
        url: '/process/standard/refreshDocAccount?stddardId='+stddardId,
        method: 'get',
        data: {}
    })
}

export function testLocalUpload(data) {
  return request({
    url: '/file/encryt/local_upload',
    method: 'post',
    data: data
  })
}

export function testLocalDown(data) {
  return request({
    url: '/file/encryt/downfile',
    method: 'post',
    data: data
  })
}

export function searchLinkLogList(query) {
  return request({
    url: '/process/linkLog/search/list',
    method: 'get',
    params: query
  })
}

export function searchLinkLogDetail(ids) {
  return request({
    url: '/process/linkLog/search/detail/' + ids,
    method: 'get'
  })
}

export function searchLinkLogDownload(ids) {
  return request({
    url: '/process/linkLog/search/download/' + ids,
    method: 'get',
    timeout: 60*60*1000,
    responseType: 'blob'
  })
}
