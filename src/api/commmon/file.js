import request from '@/utils/request'

// 查询附件列表
export function listFile(query) {
  return request({
    url: '/process/file/list',
    method: 'get',
    params: query
  })
}

// 查询附件详细
export function getFile(id) {
  return request({
    url: '/process/file/' + id,
    method: 'get',
    timeout: 60000
  })
}
// 下载本地存储
export function fileLocalDownload(fileId) {
  return request({
    url: '/process/file/local_download/' + fileId,
    method: 'get',
    timeout: 10*60*1000,
    responseType: 'blob'
  })
}

// 新增附件
export function addFile(data) {
  return request({
    url: '/process/file',
    method: 'post',
    data: data
  })
}
// 上传本地存储
export function processFileLocalUpload(data) {
  return request({
    url: '/process/file/local_upload',
    method: 'post',
    data: data,
    timeout: 10*60*1000,
  })
}

// 修改附件
export function updateFile(data) {
  return request({
    url: '/process/file',
    method: 'put',
    data: data
  })
}

// 删除附件
export function delFile(id) {
  return request({
    url: '/process/file/' + id,
    method: 'delete'
  })
}

/**
 * 获取爱数token
 * @returns {*}
 */
export function getAsAccessToken() {
  return request({
    url: '/process/file/getAsAccessToken',
    method: 'get'
  })
}

/**
 * 获取语言包数据
 * @returns {*}
 */
export function getLanguageData() {
  return request({
    url: '/process/file/getLanguageData',
    method: 'get'
  })
}
