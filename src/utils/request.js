import axios from 'axios'
import { Notification, MessageBox, Message, Loading } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import { tansParams, blobValidate } from "@/utils/ruoyi";
import { saveAs } from 'file-saver'
import i18nUtil from '../i18n/index.js'

let downloadLoadingInstance;

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
axios.defaults.headers['Accept-Language'] = 'zh'
axios.defaults.headers['tenantid'] = 'CAM'
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: process.env.VUE_APP_BASE_API,
  // 超时
  timeout: 1000*60*60*10
})

// request拦截器
service.interceptors.request.use(config => {
  // 是否需要设置 token
  const isToken = (config.headers || {}).isToken === false
  if (getToken() && !isToken) {
    config.headers['Authorization'] = 'Bearer ' + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
  }
  // 国际化语言设置
  if(sessionStorage.getItem('language')){
    config.headers['Accept-Language'] = sessionStorage.getItem('language')
  }
  // get请求映射params参数
  if (config.method === 'get' && config.params) {
    let url = config.url + '?' + tansParams(config.params);
    url = url.slice(0, -1);
    config.params = {};
    config.url = url;
  }
  return config
}, error => {
  console.log(error)
  Promise.reject(error)
})

// 响应拦截器
service.interceptors.response.use(res => {
    // 未设置状态码则默认成功状态
    const code = res.data.code || 200;
    // 获取错误信息
    const msg = errorCode[code] || res.data.msg || errorCode['default']
    // 二进制数据则直接返回
    if(res.request.responseType ===  'blob' || res.request.responseType ===  'arraybuffer'){
      return res.data
    }
    if (code === 401) {
      MessageBox.confirm(i18nUtil.t('login.status_expired'), i18nUtil.t('doc.this_dept_tip'), {
          confirmButtonText: i18nUtil.t('login.again'),
          cancelButtonText: i18nUtil.t('doc.this_dept_abolish'),
          type: 'warning'
        }
      ).then(() => {
        store.dispatch('LogOut').then(() => {
          location.href = process.env.VUE_APP_CONTEXT_PATH + "/#/index";
        })
      }).catch(() => {});
      return Promise.reject(i18nUtil.t('doc.this_download_expire'))
    } /*
    else if (code === 500) {
      Message({
        message: msg,
        type: 'error'
      })
      return Promise.reject(new Error(msg))
    } */
    else if (code !== 200) {
      // Notification.error({
      //   title: msg
      // })
      Message({
        message: msg,
        type: 'error',
        duration: 5 * 1000
      })
      // return Promise.reject('error')
      console.log('request.js拦截到非200状态的异常接口消息='+msg)
      return Promise.reject(msg)
    } else {
      return res.data
    }
  },
  error => {
    console.log('err' + error)
    let { message } = error;
    if (message == "Network Error") {
      message = i18nUtil.t('service.back_interface_error');
    }
    else if (message.includes("timeout")) {
      message = i18nUtil.t('service.sys_interface_error');
    }
    else if (message.includes("Request failed with status code")) {
      message = i18nUtil.t('service.sys_interface_name') + message.substr(message.length - 3) + i18nUtil.t('service.common_error');
    }
    Message({
      message: message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

// 通用下载方法
export function download(url, params, filename) {
  downloadLoadingInstance = Loading.service({ text: i18nUtil.t('doc.this_download_loading'), spinner: "el-icon-loading", background: "rgba(0, 0, 0, 0.7)", })
  return service.post(url, params, {
    transformRequest: [(params) => { return tansParams(params) }],
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    responseType: 'blob'
  }).then(async (data) => {
    const isLogin = await blobValidate(data);
    if (isLogin) {
      const blob = new Blob([data])
      saveAs(blob, filename)
    } else {
      Message.error(i18nUtil.t('doc.this_download_expire'));
    }
    downloadLoadingInstance.close();
  }).catch((r) => {
    console.error(r)
    Message.error(i18nUtil.t('doc.this_download_tip'))
    downloadLoadingInstance.close();
  })
}

export default service
