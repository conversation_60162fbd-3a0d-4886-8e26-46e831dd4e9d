<template>
  <div id="app" :class="language">
    <router-view />
  </div>
</template>

<script>
import enLocale from 'element-ui/lib/locale/lang/en'
import zhLocale from 'element-ui/lib/locale/lang/zh-CN'
import axios from "axios";

export default  {
  name:  'App',
  data() {
    return {
      language : sessionStorage.getItem('language') == 'en' ? "english" : null,
    };
  },
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: title => {
        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
      }
    }
  },
  created() {
    //初始化语言
    this.initLanguage()
  },
  methods: {
    async initLanguage() {
      let language = sessionStorage.getItem('language') || process.env.VUE_APP_LANGUAGE
      // 设置语言类型
      this.$i18n.locale = language
      // 请求对应语言
      let message = {}
      //if (!sessionStorage.getItem('languageData')) {
         await axios({
          url: `${process.env.VUE_APP_INET}/international/findLangPackage`,
          method: 'get',
          headers: {
            "Accept-Language": language,
          },
          params: { type: 'front' }
        }).then((res) => {
          if (res.data.resultCode != 200) {
            return
          }
          sessionStorage.setItem('languageData', JSON.stringify(res.data.result))
          sessionStorage.setItem('language', language)
          message = res.data.result
        })
      /* } else {
        message = JSON.parse(sessionStorage.getItem('languageData'))
      } */
      // 设置语言并合并element-ui的国际化
      if (language === 'zh') {
        message = { ...zhLocale, ...message }
      } else {
        message = { ...enLocale, ...message }
      }
      this.$i18n.setLocaleMessage(language, message)
    }
  }
}
</script>
