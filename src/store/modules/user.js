import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken, getAsToken, getTenantId, setTenantId } from '@/utils/auth'
import { settingDocClassList } from "@/api/file_settings/type_settings";
import cache from '@/plugins/cache'
import { getConfigKey } from '@/api/system/config'
const user = {
  state: {
    token: getToken(),
    asToken: getAsToken(),
    user: {},
    leader: {},
    name: '',
    nickName: '',
    avatar: '',
    number: '',
    thingNumber: '',
    roles: [],
    permissions: [],
    searchQuery: '',
    tenantId:getTenantId(),
  },

  mutations: {
    SET_TENANTID:(state,tenantId) =>{
      state.tenantId=tenantId
      setTenantId(tenantId)
    },
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NUMBER: (state, token) => {
      state.number = token
    },
    SET_THINGNUMBER: (state, token) => {
      state.thingNumber = token
    },
    SET_USER: (state, user) => {
      state.user = user
    },
    SET_LEADER: (state, leader)=>{
      state.leader = leader
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_NICK_NAME: (state, nickName) => {
      state.nickName = nickName
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_SEARCH_QUERY(state, query) {
      state.searchQuery = query;
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      const code = userInfo.code
      const uuid = userInfo.uuid
      const type = 'PC'
      const TenantId = userInfo.TenantId
      const longinUrl = userInfo.longinUrl
      console.log(userInfo, 'userInfo========>');
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid, type, TenantId,longinUrl).then(res => {
          console.log('login', res);
          setToken(res.data.token)
          commit('SET_TOKEN', res.data.token)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo().then(res => {
          const user = res.data.user
          const avatar = user.avatar == "" ? require("@/assets/images/profile.jpg") : user.avatar;
          if (res.data.roles && res.data.roles.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', res.data.roles)
            commit('SET_PERMISSIONS', res.data.permissions)
            let tenantId = cache.session.get('tenantId')
            if(tenantId == null || tenantId == undefined || tenantId == ''){
              cache.session.set('tenantId',user.tenantId)
            }


            // 用户信息存入缓存
            sessionStorage.setItem('USER_ROLES', res.data.roles);
            sessionStorage.setItem('USER_INFO', JSON.stringify(user));
            sessionStorage.setItem('SYS_CONFIG', JSON.stringify(res.data.config));
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_USER', user)
          commit('SET_LEADER', res.data.leader)
          commit('SET_NAME', user.userName)
          commit('SET_NICK_NAME', user.nickName)
          commit('SET_AVATAR', avatar)
          getConfigKey("doc_preview_method").then(res=>{
            sessionStorage.setItem('winOpen', res.msg === 'winOpen')
          })
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          sessionStorage.removeItem('USER_ROLES');
          sessionStorage.removeItem('USER_INFO');
          sessionStorage.removeItem('SYS_CONFIG');
          removeToken()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    },

    setSearchQuery({ commit }, query) {
      commit('SET_SEARCH_QUERY', query);
    }
  }
}

export default user
