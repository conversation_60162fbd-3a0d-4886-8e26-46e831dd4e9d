<template>
  <!-- 富文本 -->
  <div>
    <VueUeditorWrap
      v-model="content"
      :config="editConfig"
      :editor-id="id"
      :editorDependencies="['ueditor.config.js','ueditor.all.js']"
      @change="changeContent()"
      :ref="'myEditor'+id"
    ></VueUeditorWrap>
  </div>
</template>

<script>
import VueUeditorWrap from 'vue-ueditor-wrap'

export default {
  components: {
    VueUeditorWrap
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: 'editor'
    }
  },
  watch: {
    value (newValue) {
      this.content = newValue
    },
    content (newValue) {
      this.$emit('input', newValue)
    }
  },
  data () {
    return {
      editConfig: null,
      loading: false,
      content: this.value
    }
  },
  created () {
    this.loading = true
    this.editConfig = {
      // 初始容器高度// 初始容器宽度
      initialFrameHeight: 400,
      initialFrameWidth: '100%',
      zIndex: 1500,
      serverUrl: process.env.VUE_APP_BASE_API + '/process/file/local_upload',
      initChunkUrl: process.env.VUE_APP_BASE_API + '/process/file/local_upload',
      presignUploadUrl: process.env.VUE_APP_BASE_API + '/process/file/local_upload',
      chunked: true,
      chunkSize: 5 * 1024 * 1024,
      imageMaxSize: 1000 * 1024 * 1024,
      maxInputCount: 50000,
      allHtmlEnabled: true,
      // UEditor 是文件的存放路径，
      UEDITOR_HOME_URL: process.env.VUE_APP_CONTEXT_PATH + '/UEditorPlus/',
      UEDITOR_CORS_URL: process.env.VUE_APP_CONTEXT_PATH + '/UEditorPlus/'
    }
    this.loading = false
  },
  mounted () {},
  methods: {
    destroyEditor () {
      this.$refs['myEditor' + this.id].$destroy()
    },
    changeContent () {
      console.log(this.internalValue)
      console.log(this.value)
    }
  }
}
</script>
