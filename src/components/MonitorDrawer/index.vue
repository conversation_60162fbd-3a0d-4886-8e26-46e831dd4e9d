<template>
  <el-drawer
    :wrapperClosable='false'
    title="流程监控"
    size="65%"
    :visible.sync="visible"
    :append-to-body="true"
  >
    <div style="width:100%;height:100%">
      <iframe :src="src" width="100%" height="100%" title="myFrame" style="border: 0;"></iframe>
    </div>
  </el-drawer>
</template>

<script>
    import { getHistAskLogUrl } from "@/api/my_business/workflow";
    export default {
        data() {
            return {
                visible: false,
                src: ''
            };
        },
        methods: {
            init(procInstId) {
                let _this = this
                _this.visible = true
                getHistAskLogUrl(procInstId).then(res => {
                    _this.src=res.data
                })
            }
        }
    }
</script>
