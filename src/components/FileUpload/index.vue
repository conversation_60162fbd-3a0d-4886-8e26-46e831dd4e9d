<template>
  <div class="upload-file rzfujian">
    <el-upload
      multiple
      :action="uploadFileUrl"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="false"
      :headers="headers"
      class="upload-file-uploader"
      ref="upload"
      v-if="editStatus"
    >
      <!-- 上传按钮 -->
      <div class="upload-trigger" >
        <div class="upload-button">
          <i class="el-icon-upload2"></i>
          <span>{{ title }}</span>
        </div>
        <!-- 上传提示 -->
        <div class="upload-tip" v-if="showTip">
          请上传
          <template v-if="fileSize">
            大小不超过 <b>{{ fileSize }}MB</b>
          </template>
          <template v-if="fileType">
            格式为 <b>{{ fileType.join("/") }}</b>
          </template>
          的文件
        </div>
      </div>
    </el-upload>

    <!-- 文件列表 -->
    <div class="file-list" v-if="showTransition && fileList.length > 0" :style="{marginTop: editStatus?'12px':'0px'}">
      <transition-group name="file-fade" tag="div">
        <div
          :key="file.url"
          class="file-item"
          v-for="(file, index) in fileList"
        >
          <div class="file-info" @click="isPreview(file.url, file.name)">
            <div class="file-icon">
              <i :class="getFileIcon(file.name)"></i>
            </div>
            <div class="file-details">
              <div class="file-name" :title="file.name">{{ file.name }}</div>
              <div class="file-size">{{ getFileSize(file) }}</div>
            </div>
          </div>
          <div class="file-actions" v-if="editStatus || showDownload">
            <!-- 预览按钮：只有在不显示下载按钮时才显示 -->
            <el-button
              v-if="!showDownload"
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="isPreview(file.url, file.name)"
              title="预览"
            ></el-button>
            <!-- 下载按钮：只有在showDownload为true时才显示 -->
            <span
              v-if="showDownload"
              @click="handleDownload(file.url, file.name)"
              style="color: #385bb4; cursor: pointer; "
            >{{$t('home.search_download_btn')}}</span>
            <el-button
              v-if="isMode&&!showDownload"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handlePreview(file.url, '', '', 'edit')"
              title="在线编辑"
            ></el-button>
            <el-button
              v-if="!showDownload"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(index)"
              title="删除"
              style="color: #f56c6c;"
            ></el-button>
          </div>
        </div>
      </transition-group>
    </div>
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="close">
    </as-pre-view>
    <el-image-viewer
      v-if="showViewer"
      :on-close="closeViewer"
      :url-list="srcList"
    ></el-image-viewer>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { fileLocalDownload, getFile } from "@/api/commmon/file";
// 导入组件
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import mixin from "@/layout/mixin/Commmon.js";
export default {
  name: "FileUpload",
  mixins: [mixin],
  components: {
    ElImageViewer,
  },
  props: {
    // 值
    value: [String, Object, Array],
    // 上传
    title: {
      type: String,
      // 或者使用函数
      default: function () {
        return this.$t('doc.this_dept_upload');
      }
    },
    // 数量限制
    limit: {
      type: Number,
      default: 5,
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 1024,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ["docx", "doc", "xls", "ppt", "txt", "pdf", "jpg", "png"],
    },
    // 是否限制文件类型
    isfileType: {
      type: Boolean,
      default: true,
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: false,
    },
    // 是否显示文件列表
    showTransition: {
      type: Boolean,
      default: true,
    },
    // 是否可以在线编辑
    isMode: {
      type: Boolean,
      default: false,
    },
    editStatus:{
      type: Boolean,
      default: true,
    },
    // 是否显示下载按钮
    showDownload: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      number: 0,
      fileSizeMin: 0,
      uploadList: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      uploadFileUrl:
        process.env.VUE_APP_BASE_API + "/process/file/local_upload", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      imgUrl: "",
      srcList: [
        // "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
      ],
      showViewer: false, // 显示查看器]
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          let temp = 1;
          // 首先将值转为数组
          const list = Array.isArray(val) ? val : this.value.split(",");
          //console.log("list", list);
          // 然后将数组转为对象数组
          this.fileList = list.map((item) => {
            if (typeof item === "string") {
              item = { name: item, url: item };
            }
            item.uid = item.uid || new Date().getTime() + temp++;
            return item;
          });
        } else {
          this.fileList = [];
          return [];
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    },
  },
  methods: {
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.isfileType) {
        if (this.fileType) {
          let fileExtension = "";
          if (file.name.lastIndexOf(".") > -1) {
            fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
          }
          const isTypeOk = this.fileType.some((type) => {
            //if (file.type.indexOf(type) > -1) return true;
            if (fileExtension && fileExtension.indexOf(type) > -1) return true;
            return false;
          });
          if (this.fileType.filter(x => x == fileExtension) <= 0) {
            this.$modal.msgError(
              `文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`
            );
            return false;
          }
          console.log(isTypeOk);
          if (!isTypeOk) {
            this.$modal.msgError(
              `文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`
            );
            return false;
          }
        }
      }

      //校验文件重复
      for (let index = 0; index < this.fileList.length; index++) {
        const element = this.fileList[index];
        if (element.name == file.name) {
          this.$modal.msgError(`文件重复!`);
          return false;
        }
      }

      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }
      // debugger;

      //校验总共加起来小于100M
      for (let index = 0; index < this.fileList.length; index++) {
        const element = this.fileList[index];
        getFile(element.url).then((res) => {
          this.fileSizeMin = this.fileSizeMin + res.data.fileSize;
        });
      }
      console.log("fileSizeMin", this.fileSizeMin);
      console.log(
        "fileSizeMinsize",
        (this.fileSizeMin + file.size) / 1024 / 1024
      );
      /* if ((this.fileSizeMin + file.size) / 1024 / 1024 > 1000) {
        this.$modal.msgError(`上传文件总共大小不能超过1024MB!`);
        return false;
      } */
      this.$modal.loading("正在上传文件，请稍候...");
      this.number++;
      return true;
    },
    // 文件个数超出
    handleExceed(files, fileList) {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);
    },
    // 上传失败
    handleUploadError(err) {
      this.$modal.msgError("上传失败，请重试");
      this.$modal.closeLoading();
    },
    // 上传成功回调
    handleUploadSuccess(res,uploadFile,uploadFiles) {
      if (res.code != 200) {
        // 去除文件列表失败文件
        let uid = uploadFile.uid
        let idx = uploadFiles.findIndex(item => item.uid === uid)
        uploadFiles.splice(idx, 1)
        this.uploadList = [];
        this.number = 0;
        this.$modal.msgError(res.msg);
        this.$modal.closeLoading();
        return;
      }
      this.$modal.closeLoading();
      let file={ name: res.data.fileName, url: res.data.fileId }
      this.uploadList.push(file);
      if (this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList);
        this.uploadList = [];
        this.number = 0;
        //console.log("this.fileList1", this.fileList);
        this.$emit("input", this.listToString(this.fileList));
        //console.log("this.fileList2", this.fileList);
      }
    },
    // 删除文件
    handleDelete(index) {
      this.fileList.splice(index, 1);
      this.$emit("input", this.listToString(this.fileList));
    },
    // 获取文件名称
    getFileName(name) {
      if (name.lastIndexOf("/") > -1) {
        return name.slice(name.lastIndexOf("/") + 1);
      } else {
        return "";
      }
    },
    // 对象转成指定字符串分隔
    listToString(list, separator) {
      // let strs = "";
      // separator = separator || ",";
      // for (let i in list) {
      //   strs += list[i].url + separator;
      // }
      // return strs != "" ? strs.substr(0, strs.length - 1) : "";
      return list;
    },
    //isPreview
    isPreview(url, name) {
      let fileExtension = "";
      let fileType = ["jpg", "png", "docx", "doc", "xls", "ppt", "pdf"];
      if (name.lastIndexOf(".") > -1) {
        fileExtension = name.slice(name.lastIndexOf(".") + 1);
      }
      const isTypeOk = fileType.some((type) => {
        if (fileExtension && fileExtension.indexOf(type) > -1) return true;
        return false;
      });
      if (!isTypeOk) {
        //this.$modal.msg(`文件格式不支持预览!`);
        this.handelefileLocalDownload(url,name)
        return false;
      }
      const isTupian = this.isTupian(name);
      if (isTupian) {
        this.handeleyulan(url);
      } else {
        this.handlePreview(url);
      }
    },
    //校检是否是图片
    isTupian(name) {
      let fileExtension = "";
      let fileType = ["jpg", "png"];
      if (name.lastIndexOf(".") > -1) {
        fileExtension = name.slice(name.lastIndexOf(".") + 1);
      }
      console.log("fileExtension", fileExtension);
      const isTypeOk = fileType.some((type) => {
        console.log("type", type);
        console.log("fileExtension.indexOf(type)", fileExtension.indexOf(type));
        //if (file.type.indexOf(type) > -1) return true;
        if (fileExtension && fileExtension.indexOf(type) > -1) return true;
        return false;
      });
      console.log("isTypeOk", isTypeOk);
      return isTypeOk;
    },
    //预览图片文件
    handeleyulan(id) {
      fileLocalDownload(id)
        .then((res) => {
          this.srcList = [];
          // return this.blobUrl(res);
          let blob = new Blob([res], { type: "image/jpeg" });
          const imageUrl = URL.createObjectURL(blob);
          this.imgUrl = imageUrl;
          this.srcList.push(imageUrl);
          //this.$refs.elimage.$el.click()
          //document.getElementById("img").click();
          this.showViewer = true;
          console.log("this.showViewer", this.showViewer);
        })
        .catch((err) => {
          console.log("导出失败");
        });
    },

    closeViewer() {
      //关闭
      this.showViewer = false;
    },

    // 获取文件图标
    getFileIcon(fileName) {
      const ext = fileName.split('.').pop().toLowerCase()
      const iconMap = {
        'pdf': 'el-icon-document',
        'doc': 'el-icon-document',
        'docx': 'el-icon-document',
        'xls': 'el-icon-s-grid',
        'xlsx': 'el-icon-s-grid',
        'ppt': 'el-icon-present',
        'pptx': 'el-icon-present',
        'jpg': 'el-icon-picture',
        'jpeg': 'el-icon-picture',
        'png': 'el-icon-picture',
        'gif': 'el-icon-picture',
        'bmp': 'el-icon-picture',
        'svg': 'el-icon-picture',
        'tif': 'el-icon-picture'
      }
      return iconMap[ext] || 'el-icon-document'
    },

    // 获取文件大小显示
    getFileSize(file) {
      // 这里可以根据实际需要显示文件大小
      // 由于当前数据结构中没有size信息，暂时返回空
      return ''
    },

    // 下载文件
    handleDownload(url, name) {
      this.handelefileLocalDownload(url, name)
    },
  },
};
</script>

<style lang="scss">
.el-image-viewer__wrapper {
  z-index: 200000 !important;
}

.rzfujian {
  .upload-file-uploader {
    //margin-bottom: 12px;
  }

  // 上传触发区域
  .upload-trigger {
    .upload-button {
      display: inline-flex;
      align-items: center;
      padding: 2px 8px;
      background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
      color: white;
      border-radius: 3px;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 11px;
      box-shadow: 0 1px 2px rgba(64, 158, 255, 0.15);
      height: 24px;

      i {
        margin-right: 3px;
        font-size: 12px;
      }

      &:hover {
        background: linear-gradient(135deg, #66b3ff 0%, #409eff 100%);
        box-shadow: 0 2px 4px rgba(64, 158, 255, 0.25);
        transform: translateY(-0.5px);
      }
    }

    .upload-tip {
      margin-top: 8px;
      font-size: 12px;
      color: #909399;
      line-height: 1.4;

      b {
        color: #f56c6c;
        font-weight: 500;
      }
    }
  }

  // 文件列表
  .file-list {
    //margin-top: 12px;

    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 4px 8px;
      //margin-bottom: 4px;
      background: #fafafa;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      transition: all 0.2s ease;
      min-height: 32px;

      &:hover {
        background: #f0f9ff;
        border-color: #409eff;
        box-shadow: 0 1px 4px rgba(64, 158, 255, 0.1);
      }

      .file-info {
        display: flex;
        align-items: center;
        flex: 1;
        cursor: pointer;

        .file-icon {
          margin-right: 6px;

          i {
            font-size: 14px;
            color: #409eff;
          }
        }

        .file-details {
          flex: 1;

          .file-name {
            font-size: 12px;
            color: #303133;
            font-weight: 400;
            line-height: 1.2;
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .file-size {
            font-size: 10px;
            color: #909399;
            line-height: 1;
          }
        }
      }

      .file-actions {
        display: flex;
        align-items: center;
        gap: 2px;

        .el-button {
          padding: 2px;
          min-width: auto;
          font-size: 12px;

          &:hover {
            background-color: rgba(64, 158, 255, 0.1);
          }
        }
      }
    }
  }

  // 动画效果
  .file-fade-enter-active, .file-fade-leave-active {
    transition: all 0.3s ease;
  }

  .file-fade-enter, .file-fade-leave-to {
    opacity: 0;
    transform: translateY(-10px);
  }

  // Element UI 覆盖
  .el-upload {
    border: none !important;
    background: transparent !important;

    &:hover {
      background: transparent !important;
    }
  }
}
</style>
