<template>
  <div
    :class="{ 'has-logo': showLogo }"
    :style="{
      backgroundColor:
        settings.sideTheme === 'theme-dark'
          ? variables.menuBackground
          : variables.menuLightBackground,
    }"
  >
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar :class="settings.sideTheme" wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="
          settings.sideTheme === 'theme-dark'
            ? variables.menuBackground
            : variables.menuLightBackground
        "
        :text-color="
          settings.sideTheme === 'theme-dark'
            ? variables.menuColor
            : variables.menuLightColor
        "
        :unique-opened="true"
        :active-text-color="settings.theme"
        :collapse-transition="false"
        mode="vertical"
      >
      <!--  -->
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :number="[
            $store.getters.Number.number,
            $store.getters.Number.thingNumber
          ]"
          :base-path="route.path"
        />

      </el-menu>
    </el-scrollbar>
    <div class="foldMenu">
      <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container"
                 @toggleClick="toggleSideBar" />
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import { workflowToDoList } from "@/api/my_business/workflow";
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/assets/styles/variables.scss";
import { getNewsNum } from "@/api/my_business/news";
import Hamburger from "@/components/Hamburger";
export default {
  components: { SidebarItem, Logo, Hamburger },
  data() {
    return {
      number: "",
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: 1,
      },
      queryParamsApply: {
        pageNum: 1,
        pageSize: 10,
        actDefName: 1,
      },
    };
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
  },
  created() {
    getNewsNum({}).then((response) => {
      if (response.data != 0) {
        this.$store.commit("SET_NUMBER", response.data);
      }
    });
    workflowToDoList(this.queryParams).then((response) => {
      this.$store.commit("SET_THINGNUMBER", response.total || 0);
    });
  },
  computed: {
    ...mapState(["settings"]),
    ...mapGetters(["sidebarRouters", "sidebar"]),
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    showLogo() {
      //return this.$store.state.settings.sidebarLogo;
      return sessionStorage.getItem('client') === null
    },
    variables() {
      return variables;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
  },
};
</script>
