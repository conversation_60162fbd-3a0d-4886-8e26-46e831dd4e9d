<template>
  <main-component ref="mainComponent" :code="code" :data="data"  @close="handleCloseChange"></main-component>
</template>

<script>
import MainComponent from "@/components/mainComponent/index.vue";
export default {
  name: "workflowRouter",
  dicts: [],
  components: {
    MainComponent,
  },
  data() {
    return {
      path: 'views/workflowList/addWorkflow/',
      code: '',
      data: undefined,
      status: true,
    }
  },
  created() {
    this.$nextTick(()=>{
      this.handleDetails(this.$route.query);
    })
  },
  methods: {
    /** 详情按钮操作 */
    handleDetails(row) {
      let _this = this
      if (row.type&&_this.status) {
        _this.code = _this.path + row.type
        _this.data = row
      }
    },
    init(row){
      let _this = this
      _this.code = _this.path + row.type
      _this.data = row
      _this.status = false
    },
    handleCloseChange() {
      let _this = this
      if (_this.status) {
        window.parent.postMessage({type:"close"})
        window.parent.close()
      }else{
        _this.$emit("closeDrawer")
      }
    }
  },
};
</script>
