<template>
  <div class="document_change_add"  v-loading="loading||detailLoading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{ $t(`doc.this_dept_file`) }}{{$t(`doc.this_dept_cancel`)}}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button v-if="procInstId" @click="handleMonitor">{{ $t(`doc.this_dept_process_monitor`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_preset_user')&&workflowStatus&&!batchStatus" @click="selectPresetUser">{{ $t(`file_handle.change_select_people`) }}</el-button>
        <el-button v-if="editStatus&&procInstId&&!batchStatus" type="danger" @click="deleteForm" v-dbClick>{{ $t(`file_handle.change_revoke`) }}</el-button>
        <el-button v-if="procInstId&&workflowStatus&&!batchStatus" @click="transferForm" v-dbClick>{{ transferStatus?$t(`file_handle.transfer_return`):$t(`file_handle.transfer`) }}</el-button>
        <el-button v-if="procInstId&&!workflowStatus&&'doing'===formData.processStatus&&formData.createBy===userInfo.userName&&backFlowToOneStatus&&!batchStatus" type="danger" @click="handleBackFlowToOne" v-dbClick>{{$t(`file_handle.change_withdraw`)}}</el-button>
        <el-button v-if="nodeShow('top_btn_reject_previous_step')&&workflowStatus&&!batchStatus" @click="rejectPreviousStep()" type="danger">{{ $t(`dicts.flow_node_fun_list_top_btn_reject_previous_step`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_reject_drafter')&&workflowStatus&&!batchStatus" @click="handelpbohuiqicaoren()" type="danger">{{ $t(`file_handle.change_reject_to_preparer`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_generate_code')&&workflowStatus&&!batchStatus" @click="shengchengbianhao()" type="primary">{{ $t(`file_handle.change_generate_num`) }}</el-button>
        <!-- 【签章生效】和【执行发布】一般出现在发布环节 -->
        <!--        下载-->
        <el-button v-if="nodeShow('download_resume')&&workflowStatus" @click="downloadResume()"  v-dbClick
                   type="primary">{{ $t(`doc.download_resume`) }}
        </el-button>
        <el-button v-if="nodeShow('top_btn_setup_time')&&workflowStatus&&!batchStatus" @click="handleSignEffective()"  type="primary">{{ $t(`file_handle.change_signature_effc`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_publish_file')&&workflowStatus&&!batchStatus" @click="handlePublish()"  type="primary">{{ $t(`file_handle.change_execute_release`) }}</el-button>
        <!-- 提交按钮在 非【执行发布】环节出现 -->
        <el-button v-if="!nodeShow('top_btn_publish_file') && workflowStatus&&!batchStatus" type="primary" @click="submitForm" v-dbClick>
          {{ $t(`doc.this_dept_annex`) }}</el-button>
        <el-button v-if="editStatus" type="primary" @click="saveForm" v-dbClick>{{ $t(`doc.this_dept_save`) }}</el-button>
        <el-button @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <el-tabs v-if="procInstId" v-model="activeName">
      <el-tab-pane :label="$t(`doc.this_dept_info_content`)" name="info"></el-tab-pane>
      <el-tab-pane :label="$t(`doc.this_dept_approval_records`)" name="log"></el-tab-pane>
      <el-tab-pane :label="$t(`doc.dept_relevant_user`)" name="relevant_user" v-if="nodeShow('relevant_user')"></el-tab-pane>
    </el-tabs>
    <div class="dialog-body" v-show="activeName==='info'">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_base_msg`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row gutter="15" v-if="isProject">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_project`)+`:`" prop="projectId">
                  <span>{{formData.projectName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label=" " prop="">
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_types`)" prop="docClass">
                  <span>{{docClassData.className}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_change_type`) + `:`" prop="changeType">
                  <span>{{ $t(`doc.this_dept_cancel`) }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15"  v-if="formData.classType===classTypeRecord">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_by_superior`)+`:`" prop="upVersionId">
                  <span>{{formData.upDocName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_superior_file_name`)" prop="parentDocId">
                  <span>{{formData.parentDocId}}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_file_names`)" prop="docName">
                  <span>{{formData.docName}}</span>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_file_codes`)" prop="docId">
                  <span>{{ formData.docId }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <template v-if="internalDocIdShow">
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_internal_file_number`)" prop="internalDocId">
                    <span>{{formData.internalDocId}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <template v-if="ecnCodeShow">
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_ecn_number`)" prop="ecnCode">
                    <span>{{formData.ecnCode}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_file_versions`)" prop="versionValue">
                  <span>{{formData.versionValue}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_staffing_depts`)" prop="deptName">
                  <span>{{ formData.deptName }}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_staff`)" prop="userName">
                  <span>{{ formData.nickName }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_preparation_time`)+`:`" prop="applyTime">
                  <span>{{ parseTime(formData.applyTime) }}</span>
                </el-form-item>
              </el-col>
            </el-row>

<!--            <el-row gutter="15" v-if="formData.classType===classTypeForeign">-->
<!--              <el-col :span="24">-->
<!--                <el-form-item :label="$t(`doc.this_dept_file_effective_date`)+`:`" prop="fileEffectiveDate">-->
<!--                  <span>{{ parseTime(formData.fileEffectiveDate) }}</span>-->
<!--                </el-form-item>-->
<!--              </el-col>-->

<!--              <el-col :span="24">-->
<!--                <el-form-item :label="$t(`doc.this_dept_revise_date`)+`:`" prop="revisionDate">-->
<!--                  <span>{{ parseTime(formData.revisionDate) }}</span>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--            </el-row>-->
            <template v-if="isSystemClauseShow">
              <el-row gutter="15">
                <el-col :span="12">
                  <el-form-item :label="$t(`doc.system_clause`)" prop="systemClause">
                    <span>{{formData.systemClause}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_file_temp`)">
                  <div class="link-box bzlink-box">
                    <span
                      v-if="mobanwenjian != ''"
                      style="color: #385bb4; cursor: pointer"
                      @click="handlePreview(mobanwenjian[0].id)"
                    >{{ mobanwenjian[0].fileName }}</span
                    >
                    <span
                      v-if="mobanwenjian != ''"
                      style="color: #385bb4; cursor: pointer; margin-left: 10px"
                      @click="
                        handelefileLocalDownload(
                          mobanwenjian[0].id,
                          mobanwenjian[0].fileName
                        )
                      "
                    >下载</span
                    >
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_change_source`)+`:`" prop="invokeId">
                  <span style="color: #385bb4; cursor: pointer" @click="handleDeal(formData)">{{formData.invokeId}}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15"  v-if="programVersionId">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_by_superior`) + `:`" prop="programDocId">
                  <span>{{formData.programDocName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_superior_file_name`)" prop="programDocId">
                  <span>{{formData.programDocId}}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 法规/标准相关字段 -->
            <el-row gutter="15" v-if="regulationStatusShow || regulationPublishDateShow">
              <el-col :span="12" v-if="regulationStatusShow">
                <el-form-item :label="$t('dict.regulation_standard_status') + ':'" prop="regulationStandardStatus">
                  <div class="project-code-tags">
                    <dict-tag :options="dict.type.regulation_standard_status" :value="formData.regulationStandardStatus"/>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="regulationPublishDateShow">
                <el-form-item :label="$t('field.regulation_publish_date') + ':'" prop="regulationPublishDate">
                  <span>{{formData.regulationPublishDate}}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15" v-if="regulationImplementDateShow">
              <el-col :span="12">
                <el-form-item :label="$t('field.regulation_implement_date') + ':'" prop="regulationImplementDate">
                  <span>{{formData.regulationImplementDate}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <!-- 空列，保持布局对齐 -->
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_current_change_ver`)+`:`">
                  <fileUpload
                    :showDownload="nodeShow('download_source_file')&&workflowStatus&&!batchStatus"
                    :editStatus="false"
                    v-model.trim="standardDocfileList"
                    limit="1"
                    :fileType="['docx','doc','xls','xlsx','pdf','ppt','pptx']"
                    :isShowTip="false"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_change_ver_annex`)+`:`">
                  <fileUpload
                    :editStatus="false"
                    v-model.trim="appendixesfileList"
                    :fileType="[]"
                    :isfileType="false"
                    :limit="100"
                    :isShowTip="false"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <template v-if="projectCodeShow">
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_project_code`)" prop="projectCode">
                    <el-select
                      v-if="editStatus"
                      filterable
                      multiple
                      :placeholder="$t(`doc.this_dept_project_code`)"
                      v-model.trim="formData.projectCode"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="dict in dict.type.project_code_list"
                        :key="dict.value"
                        :label="dictLanguage(dict)"
                        :value="dict.value"
                      >
                      </el-option>
                    </el-select>
                    <div v-else class="project-code-tags">
                      <dict-tag :options="dict.type.project_code_list" :value="formData.projectCode"/>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>

            <template v-if="projectNameSecurityKeywordByteShow">
              <el-row gutter="15">
                <el-col :span="12">
                  <el-form-item :label="$t(`doc.this_dept_project_name`)" prop="projectName">
                    <span>{{formData.projectName}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t(`doc.this_dept_security_class`)" prop="securityClass">
                    <span> <dict-tag :options="dict.type.security_class_list" :value="formData.securityClass"/></span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row gutter="15">
                <el-col :span="12">
                  <el-form-item :label="$t(`doc.this_dept_keyword`)" prop="keyword">
                    <span>{{formData.keyword}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t(`doc.this_dept_doc_bytes`)" prop="docBytes">
                    <span>{{formData.docBytes}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>

            <template v-if="isCustomerShow">
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_client_codes`)" prop="customerCode">
                    <span >{{formData.customerCode}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>

            <template v-if="isShowPart">
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_product_codes`)" prop="productTableData">
                    <template >
                      <el-table
                        :data="productTableData"
                        border
                        style="width: 100%"
                        :show-header="true"
                      >
                        <el-table-column
                          :label="$t(`doc.this_dept_product_codes`)"
                          prop="materialCode"
                        >
                          <template slot-scope="scope">
                            <span>{{ scope.row.materialCode || '-' }}</span>
                          </template>
                        </el-table-column>

                        <el-table-column
                          v-if="isShowProductVersion"
                          :label="$t(`doc.this_dept_product_version`)"
                          prop="productVersion"
                        >
                          <template slot-scope="scope">
                            <span>{{ scope.row.productVersion || '-' }}</span>
                          </template>
                        </el-table-column>

                        <el-table-column
                          :label="$t(`doc.this_dept_product_summarys`)"
                          prop="materialDescription"
                        >
                          <template slot-scope="scope">
                            <span>{{ scope.row.materialDescription || '-' }}</span>
                          </template>
                        </el-table-column>
                      </el-table>
                    </template>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_factorys`)" prop="factorys">
                    <span>{{ getDictLabel(formData.factorys) }}</span>
<!--                    <dict-tag :options="dict.type.tenant_list" :value="formData.factorys"/>-->
                  </el-form-item>
                </el-col>
              </el-row>
            </template>

            <template v-if="isDeviceShow">
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_unit_codes`)" prop="deviceCode">
                    <span >{{formData.deviceCode}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_unit_name`)+`:`" prop="deviceName">
                    <span>{{formData.deviceName}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>

            <el-row gutter="15" v-if="isShelfLifeShow">
<!--              <el-col :span="24">-->
<!--                <el-form-item :label="$t(`doc.this_dept_custody_dept`)+`:`">-->
<!--                  <span>{{formData.custodyDeptName}}</span>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_shelf_life`)+`:`">
                  <span>{{parseTime(formData.shelfLife, "{y}-{m}-{d}")}}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15" v-if="formData.classType===classTypeForeign&&complianceShow">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_compliancy`) + `:`" prop="compliance">
                  <span>{{formData.compliance}}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15" v-if="filePurposeShow">
              <el-col :span="12">
                <el-form-item :label="$t('dict.file_purpose_type') + ':'" prop="filePurpose">
                  <div class="project-code-tags">
                    <dict-tag :options="dict.type.file_purpose_type" :value="formData.filePurpose"/>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_change_reason`) + `:`" prop="changeReason">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    resize="none"
                    v-model="formData.changeReason"
                    type="textarea"
                    :placeholder="$t(`doc.this_dept_insert_change_reason`)"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                    maxlength="1000"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_changes`) +`:`" prop="content">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    resize="none"
                    v-model="formData.content"
                    type="textarea"
                    :placeholder="$t(`doc.this_dept_insert_change_content`)"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                    maxlength="1000"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`sys_mgr.user_remark`)+`:`" prop="remark">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    resize="none"
                    v-model="formData.remark"
                    type="textarea"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15" v-if="(trains&&trains.length>0)||(workflowStatus&&nodeShow('page_oper_add_train_record')&&formData.yNTrain!==no)">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_train_record`)+`:`" prop="remark">
                  <fileUpload
                    v-model.trim="trains"
                    :editStatus="workflowStatus&&nodeShow('page_oper_add_train_record')"
                    limit="100"
                    :fileType="['docx', 'doc','xls','xlsx','pdf','ppt','pptx','ppts','bmp','jpg','png','svg','tif','gif']"
                    :isShowTip="false"
                    @input="(list)=>handelConfirm(list,'train')"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15" v-if="nodeShow('recovery_confirm')">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.recovery_confirm`)+`:`" prop="recoveryConfirm">
                  <el-radio-group  v-if="nodeShow('recovery_confirm')" @input="handelRecoveryConfirm" v-model.trim="formData.recoveryConfirm">
                    <el-radio
                      v-for="(item, index) in dict.type.sys_yes_no"
                      :key="index"
                      :label="item.value"
                    >{{ dictLanguage(item) }}</el-radio>
                  </el-radio-group>
                  <dict-tag v-else :options="dict.type.sys_yes_no" :value="formData.recoveryConfirm"/>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>

      <div class="news-card" v-if="!!formData.yNTrain">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_train`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elFormTrain"
            :model="formData"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_train_or_not`)+`:`" prop="yNTrain">
                  <el-radio-group  v-if="editStatus&&trainStatus&&!batchStatus" v-model.trim="formData.yNTrain">
                    <el-radio
                      v-for="(item, index) in dict.type.sys_yes_no"
                      :key="index"
                      :label="item.value"
                    >{{ dictLanguage(item) }}</el-radio>
                  </el-radio-group>
                  <dict-tag v-else :options="dict.type.sys_yes_no" :value="formData.yNTrain"/>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>

      <div class="el-card news-card is-always-shadow">
        <div class="el-card__body">
          <el-tabs
            v-model.trim="activeIndex"
            class="news-tabs"
          >
            <el-tab-pane name="1" v-if="formData.classType===classTypeNote&&classTypeRecordMN">
              <span slot="label">{{ $t(`doc.this_dept_master_file`) }}</span>
              <link-doc
                :editStatus="editStatus"
                v-show="activeIndex  == '1'"
                :dataList="formData.noteDocLinks"
                ref="linkDoc"
                :dataType="formData.dataType"
              ></link-doc>
            </el-tab-pane>
            <el-tab-pane name="1" v-if="formData.classType===classTypeDoc&&classTypeRecordMN">
              <span slot="label">{{ $t(`doc.this_dept_related_record`) }}</span>
              <link-note
                :editStatus="false"
                :status="false"
                v-show="activeIndex  == '1'"
                :dataList="formData.noteLinks"
                ref="linkNote"
                :dataType="formData.dataType"
              ></link-note>
            </el-tab-pane>
            <el-tab-pane name="1" v-if="formData.classType===classTypeDoc&&!classTypeRecordMN">
              <span slot="label">{{ $t(`doc.this_dept_related_record`) }}</span>
              <link-record
                ref="linkRecord"
                :disuseStatus="true"
                :editStatus="editStatus"
                :dataList="formData.recordLinks"
                v-show="activeIndex  == '1'"
                :dataType="formData.dataType"
              ></link-record>
            </el-tab-pane>
            <el-tab-pane name="2" v-if="formData.classType===classTypeDoc">
              <span slot="label">{{ $t(`doc.this_dept_related_file`) }}</span>
              <link-file
                :editStatus="false"
                v-show="activeIndex  == '2'"
                :dataList="formData.docLinks"
                ref="linkFile"
                :dataType="formData.dataType"
              ></link-file>
            </el-tab-pane>
            <el-tab-pane name="3">
              <span slot="label">{{ $t(`doc.this_dept_file_history`) }}</span>
              <el-card class="gray-card">
                <historicalVersion
                  @handleClick="handleDeal"
                  :detatailsData="formData"
                  v-show="activeIndex  == '3'"
                ></historicalVersion
                ></el-card>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="news-card" v-if="(nodeShow('shenhe')||nodeShow('pizhun'))&&workflowStatus&&!approvalStatus&&!batchStatus">
        <div class="card-head">
          <div class="cell-title">{{submitLabel}}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="validateForm"
            :model="formSubmit"
            :rules="rules"
            size="medium"
            label-position="right"
            label-width="200px"
          >
            <el-row gutter="15" >
              <el-col :span="24">
                <el-form-item :label="submitLabel+'结论:'" prop="pass">
                  <el-radio-group v-model.trim="formSubmit.pass" @input="commentItemSelect">
                    <el-radio
                      v-for="dict in passoptions"
                      :key="dict.value"
                      :label="dict.value"
                    >{{ dict.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="submitLabel+'意见:'">
                  <el-input
                    v-model="formSubmit.summary"
                    type="textarea"
                    :placeholder="'请输入'+submitLabel+'意见'"
                    maxlength="200"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
      <approval-box
        v-if="!nodeShow('top_btn_publish_file')&&workflowStatus&&approvalStatus&&!batchStatus"
        ref="approvalBox" :submitLabel="submitLabel"
        :selected="nodeShow('default_selected')"
        :userListStatus="nodeShow('user_list')"
        :order = "order"
        :searchQuery="searchQuery"
        :hideNodeCode = "hideNodeCode"
        :defaultStaff="defaultStaff"
        :pListData="pListData"
        :status="(nodeShow('shenhe')||nodeShow('pizhun'))"
      ></approval-box>
    </div>
    <div v-show="activeName==='log'">
      <workflow-logs :procInstId = "procInstId"></workflow-logs>
    </div>
    <div v-show="activeName==='relevant_user'">
      <pre-select-users-table
        v-if="formData.id"
        :apply-id="formData.id"
      />
    </div>
    <!-- 流程选择下一环节及人员 -->
    <el-dialog
      title="选择下一环节及人员"
      v-if="dialogVisible" :visible.sync="dialogVisible"
      width="60%"
      append-to-body
      v-loading = flowStepLoading
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <processcode
        ref="prochild"
        :selected="nodeShow('default_selected')"
        :order = "order"
        :userListStatus="nodeShow('user_list')"
        :defaultStaff="defaultStaff"
        :hideNodeCode = "hideNodeCode"
        :searchQuery="searchQuery"
        :pListData="pListData"
        :isSummary="isSummary"
      ></processcode>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
        <el-button type="primary" v-dbClick @click="handleWorkflowSubmit"
        >{{ $t(`doc.this_dept_annex`) }}</el-button
        >
      </span>
    </el-dialog>
    <monitor-drawer
      v-if="monitorDrawerVisible"
      ref="monitorDrawer"
    ></monitor-drawer>
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="closeAS">
    </as-pre-view>
    <!-- PDF文件预览组件 -->
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="close"></as-pre-view>
    <el-drawer
      :wrapperClosable='false'
      :visible.sync="dealDrawerShow"
      :append-to-body="true"
      direction="rtl"
      size="90%"
      :with-header="false"
      :show-close="false"
      modal-append-to-body
      :destroy-on-close="true"
    >
      <div style="width:100%; height:100%;overflow: hidden">
        <workflow-router ref="dealDrawer" @closeDrawer="handleCloseChange"></workflow-router>
      </div>
    </el-drawer>
    <preset-user ref="presetUser" @selectHandle="selectHandlePresetUser"></preset-user>
    <transfer-flow ref="transferFlow" @close="close"></transfer-flow>
  </div>
</template>
<script>
import historicalVersion from "./add_import/historicalVersion";
import { processFileLocalUpload } from "@/api/commmon/file";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import processcode from "@/views/workflowList/processcode/index.vue";
import WorkflowLogs from "@views/workflowList/workflowLogs/index.vue";
import { getLeaderByUserName } from '../../../api/system/user'
import { listDept } from '@/api/system/dept'
import {
  addModifyApply,
  getModifyApply,
  updateModifyApply,
  linkLoglistlink,
  getInfoByBpmnId, getDocNoByApplyId, getRecordDocNoByLinkId, updateById,queryModifyApplyTrain
} from '@/api/file_processing/modifiyApply'
import {signEffective, downloadResume as downloadResumeApi} from "@/api/file_processing/fileSignature";
import { settingDocClassId } from "@/api/file_settings/type_settings";
import { getByUpDocClassAndBizType } from "@/api/setting/docClassFlow";
import { getInfo } from "@/api/setting/docClassFlowNodeDetail";
import {modifyApplyLinklist, standardGetDetail} from "@/api/document_account/standard";
import {
  workflowSubmit,
  workflowprocesskey,
  getStartActdef,
  getExtAttributeModel,
  procInstInfoAndStatus, workflowbacktostart, getRecordbyPorcInstId, getRedirectDefId, backFlowToOne
} from '@/api/my_business/workflow'
import mixin from "@/layout/mixin/Commmon.js";
import { getModifyApplyTrainList, updateModifyApplyTrainList } from '@/api/my_business/modifyApplyTrain'
import LinkRecord from '@views/workflowList/addWorkflow/add_import/linkRecord.vue'
import LinkFile from '@views/workflowList/addWorkflow/add_import/linkFile.vue'
import WorkflowRouter from '@views/workflowList/workflowRouter.vue'
import { selectStatusByDocId, selectStatusRecord } from '@/api/my_business/workflowApplyLog'
import { listPresetUser } from '@/api/setting/presetUser'
import PresetUser from '@views/workflowList/addWorkflow/add_import/presetUser.vue'
import { checkPermi } from '@/utils/permission'
import { listWorkflowLog } from '@/api/my_business/workflowLog'
import { getInfoBy } from '@/api/setting/docClassSetting'
import LinkNote from '@views/workflowList/addWorkflow/add_import/linkNote.vue'
import LinkDoc from '@views/workflowList/addWorkflow/add_import/linkDoc.vue'
import ApprovalBox from './add_import/approvalBox.vue'
import TransferFlow from './add_import/transferFlow.vue'
import { listDistributeGroupDetail } from '../../../api/setting/distributeGroupDetail'
import PreSelectUsersTable from './add_import/preSelectUsersTable.vue'
import { backPreviousStep } from '../../../api/my_business/workflow'

export default {
  dicts: ["business_status",'sys_yes_no','tenant_list','security_class_list','project_code_list','file_purpose_type','regulation_standard_status'],
  components: {
    TransferFlow,
    ApprovalBox,
    LinkDoc,
    LinkNote,
    PresetUser,
    WorkflowRouter,
    LinkFile,
    LinkRecord,
    historicalVersion,
    Treeselect,
    processcode,
    WorkflowLogs,
    PreSelectUsersTable
  },
  name: "Add_doc",
  props: ["dataType",'data'],
  mixins: [mixin],
  data() {
    return {
      mark:undefined,
      type: 'disuse_doc',
      approvalStatus: true,
      rains:[],
      order: 0,
      yes: 'Y',
      no: 'N',
      hideNodeCode: [],
      defaultStaff:undefined,
      classTypeRecordMN: true,
      backFlowToOneStatus: true,
      classTypeRecord: 'RECORD',
      classTypeDoc: 'DOC',
      classTypeForeign: 'FOREIGN',
      classTypeNote: 'NOTE',
      searchQuery: {},
      shlkPath:process.env.VUE_APP_SHLK_PATH,
      procInstId: undefined,
      dealDrawerShow: false,
      submitLabel:this.$t('doc.this_dept_annex'),
      isProject: false,
      docIdData: {},
      jiluliData: [],
      shenchenbianhao: false,
      passoptions: [
        { value: true, label: this.$t(`doc.this_dept_pass`) },
        { value: false, label: this.$t(`doc.this_dept_not_pass`) },
      ],
      formSubmit: { summary: "", actionType: "", pass: undefined },
      pButton: 'DISUSE',
      isSummary: false,
      title: this.$t(`doc.this_dept_add_file`),
      activeName: "info",
      nodeDetail: {},
      nodeDetailList: [],
      procDefKey: undefined,
      processData: {},
      viewId: "",
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      viewShow: false,
      active: 4,
      activeIndex: "1",
      uploadType: ["doc", "docx", "ppt", "xlsx", "pdf", "jpg", "png"],
      monitorDrawerVisible:false,
      redirectDefId: undefined,
      redirectReceivers: undefined,
      redirectOrder: undefined,
      formData: {
        whetherCustomer: undefined,
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "01",
        docId: undefined,
        deptId: undefined,
        deptName: undefined,
        userName: undefined,
        nickName: undefined,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: undefined,
        appendixes: undefined, //附件
        standardDoc: undefined, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        noteLinks: undefined,
        noteDocLinks: undefined,
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined,
        step: undefined,
        presetUserList:[],
        batch: undefined,
        productLine: undefined,
        process: undefined,
        productType: undefined,
        securityLevel: undefined,
        haveLinkFile: undefined,
        partNumber: undefined,
        custodyDeptId: undefined,
        shelfLife: undefined,
        fileEffectiveDate: undefined,
        revisionDate: undefined,
        compliance: undefined,
        filePurpose: undefined,
        partRemark: null,
        factorys: null,
        customerCode: null,
        deviceCode: null,
        deviceName: null,
        recoveryConfirm: undefined,
        productVersion: null
      },
      partNumberArr: [""],
      // 产品表格数据
      productTableData: [
        {
          materialCode: '',
          productVersion: '',
          materialDescription: '',
        }
      ],

      // 各字段的校验规则
      materialCodeRules: [
        {required: true, message: this.$t(`doc.this_dept_product_code_no_null`), trigger: ['blur', 'change']}
      ],
      productVersionRules: [
        {required: true, message: this.$t(`doc.this_dept_product_version_no_null`), trigger: ['blur', 'change']}
      ],
      materialDescriptionRules: [
        {required: true, message: this.$t(`doc.this_dept_product_summary_no_null`), trigger: ['blur', 'change']}
      ],
      // 产品表格验证规则
      productTableRules: {},
      rules: {
        whetherCustomer: [
          { required: true, message: this.$t(`doc.this_dept_pls_select`)+'!', trigger: "blur,change" },
        ],
        pass:[
          { required: true, message: this.$t(`doc.this_dept_pls_select`), trigger: "blur" },
        ],
        docClass: [
          { required: true, message: this.$t(`doc.this_dept_preparer`), trigger: "blur" },
        ],
        docName: [
          {
            required: true,
            message: this.$t(`doc.this_dept_file_name_not_null`),
            trigger: "blur,change",
          },
          {
            max: 1000,
            message: this.$t(`doc.this_dept_file_name_more_long`),
          }
        ],
        versionValue: [
          { required: true, message: this.$t(`doc.this_dept_insert_ver`), trigger: "blur" },
        ],
        // custodyDeptId :[
        //   { required: true, message: "请选择保管部门", trigger: "blur,change" },
        // ],
        // shelfLife :[
        //   { required: true, message: "请选择保存期限", trigger: "blur,change" },
        // ],
        changeReason: [
          { required: true, message: this.$t(`doc.this_dept_insert_change_reason`), trigger: "blur,change" },
          {
            max: 1000,
            message: this.$t(`doc.this_dept_change_reason_more_long`),
          },
        ],
        content: [
          { required: true, message: this.$t(`doc.this_dept_insert_change_content`), trigger: "blur,change" },
          {
            max: 1000,
            message: this.$t(`doc.this_dept_change_content_more_long`),
          },
        ],
        projectCode: [
          {
            required: true,
            message: this.$t(`doc.this_dept_pls_select`)+this.$t(`doc.this_dept_project_code`),
            trigger: "blur,change",
          }
        ]
      },
      kuozhanshuju:{},
      kuozhanshujuBool: {},
      field117Action: "",
      action: "/dms-admin/process/file/local_upload",
      trains:[],
      // 文件上传相关数据
      standardDocfileList: [],
      appendixesfileList: [],
      summary: "",
      pListData: {},
      editStatus:false,
      trainStatus:true,
      transferStatus: false,
      projectList: [],
      project:{id:'',name:''},
      workflowStatus: false,
      dialogVisible: false,
      processcodeData: {},
      processInstanceModel: {},
      disabled: false,
      mobanwenjian: [],
      loading: false,
      detailLoading: false,
      flowStepLoading : false,
      userinfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      isShowPart: false,
      isSystemClauseShow: false,
      isCustomerShow: false,
      isDeviceShow: false,
      isShowProductVersion: false,
      projectNameSecurityKeywordByteShow: false,
      complianceShow: false,
      filePurposeShow: false,
      regulationStatusShow: false,
      programVersionId:false,
      regulationPublishDateShow: false,
      regulationImplementDateShow: false,
      internalDocIdShow: false,
      ecnCodeShow: false,
      projectCodeShow:  false,
      isShelfLifeShow: false,
      docClassData: {},
    };
  },
  computed: {
    batchStatus(){
      return !!this.formData.batchId
    }
  },
  watch: {
    "formData.docClass"(val) {
      let _this = this
      if (val) {
        if (!_this.procInstId) {
          _this.getByUpDocClassAndBizType(val)
        }else {
          _this.getNodeDetailInfo()
        }
      }
    },
    "formData.dataType"(val) {
      let _this = this
      _this.isProject = val==='project'
    },
    data (val) {
      if (val) {
        this.init(val)
      }
    },
  },
  async created() {
    let response = await this.getConfigKey("record.doc.type")
    this.classTypeRecordMN = response.msg === undefined ? true : response.msg === 'true';
    let response1 = await this.getConfigKey("back_flow_to_one")
    this.backFlowToOneStatus = response1.msg === undefined ? true : response1.msg === 'true';
  },
  mounted() {
    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    checkPermi,
    // 处理提交数据，将数组字段转换为字符串
    processFormDataForSubmit(formData) {
      // 将 projectCode 数组转换为字符串
      if (formData.projectCode && Array.isArray(formData.projectCode)) {
        formData.projectCode = formData.projectCode.join(',')
      }
      return formData
    },
    init(row){
      let _this = this
      _this.rest()
      _this.loading = true
      if (row.order) {
        _this.order = row.order
      }
      if (row.mark) {
        _this.mark = row.mark
      }
      //是否编辑模式
      this.$nextTick(() => {
        if (row&&(!!row.procInstId||!isNaN(row.dataListIndex))) {
          _this.procInstId = row.procInstId
          _this.workflowStatus = row.status == '1'
          _this.procInstInfoAndStatus(_this.procInstId)
          if (row.batchId) {
            _this.formData = row
            if (row.id) {
              _this.getModifyApplyTrain(row.id)
            }
            _this.dataListIndex = row.dataListIndex
            if(_this.formData.classType!==_this.classTypeDoc&&_this.formData.classType!==_this.classTypeNote){
              _this.activeIndex = '3'
            }
            _this.settingDocClassId(_this.formData.docClass);
            _this.getByDocClass(_this.formData.docClass);
            _this.restoreProductTableData()
          }else {
            _this.getDetail(_this.procInstId)
          }
        }else {
          _this.workflowStatus = true
          _this.loading = false
          _this.editStatus = true
          _this.getStandardDetail(row)
          // this.getWorkflowprocesskey();
        }
      });
    },
    getStandardDetail(query) {
      let _this = this
      _this.detailLoading = true
      standardGetDetail(query).then(async (res) => {
        let formData = res.data;
        //关联文件
        let res1 = await linkLoglistlink({linkType: "REF_DOC", versionId: formData.versionId})
        formData.docLinks = res1.data
        //关联记录
        let res2 = await linkLoglistlink({linkType: "RECORD", versionId: formData.versionId})
        formData.recordLinks = res2.data
        //关联记录
        let res3 = await linkLoglistlink({linkType: "NOTE", versionId: formData.versionId,status:'1'})
        formData.noteLinks = res3.data

        let res4 = await linkLoglistlink({linkType: "NOTE_DOC", versionId: formData.versionId,status:'1'})
        formData.noteDocLinks = res4.data

        formData.id = undefined
        formData.type = _this.type
        formData.changeType = _this.pButton;
        formData.presetUserList = []
        if (formData.classType !== _this.classTypeDoc && formData.classType !== _this.classTypeNote) {
          _this.activeIndex = '3'
        }
        formData.whetherCustomer = undefined
        formData.projectCode = formData.projectCode ? formData.projectCode.split(',') : []
        _this.formData = formData
        if(!_this.formData.userName){
          this.$set(this.formData,'nickName',_this.userinfo.nickName)
          this.$set(this.formData,'userName',_this.userInfo.userName)
        }
        _this.settingDocClassId(_this.formData.docClass);

        //查询物料是否展示
        this.getByDocClass(formData.docClass);

        this.restoreProductTableData()
      }).finally(()=>{
        _this.detailLoading = false
      });
    },
    async settingDocClassId(val) {
      let _this = this
      await settingDocClassId(val).then((response) => {
        _this.docClassData=response.data
        if (response.data&&response.data.fileList != null) {
          _this.mobanwenjian = response.data.fileList;
        }else {
          _this.mobanwenjian = []
        }
      });
    },
    getDetail(procInstId) {
      let _this = this
      _this.detailLoading = true
      getInfoByBpmnId(procInstId).then(async(res) => {
        let formData = res.data;
        //console.log("查询文件变更操作申请详细", res);
        //关联文件
        let res1 = await linkLoglistlink({ linkType: "REF_DOC", versionId: formData.versionId })
        formData.docLinks = res1.data
        //关联记录
        // let res2 = await linkLoglistlink({ linkType: "RECORD", versionId: formData.versionId })
        // formData.recordLinks = res2.data
        let res2 = await modifyApplyLinklist({ applyId: formData.id, linkType: "RECORD" })
        formData.recordLinks = res2.rows;
        let res3 = await listPresetUser({bizId: formData.id})
        formData.presetUserList = res3.data
        let res4 = await modifyApplyLinklist({ applyId: formData.id, linkType: "NOTE" })
        formData.noteLinks = res4.rows;

        let res5 = await modifyApplyLinklist({ applyId: formData.id, linkType: "NOTE_DOC" })
        formData.noteDocLinks = res5.rows;

        //培训记录
        _this.getModifyApplyTrain(formData.id)

        formData.type = _this.type
        formData.changeType = this.pButton;
        if(formData.classType!==_this.classTypeDoc){
          _this.activeIndex = '3'
        }
        formData.projectCode = formData.projectCode ? formData.projectCode.split(',') : []
        _this.formData = formData
        _this.settingDocClassId(_this.formData.docClass);

        //查询物料是否展示
        this.getByDocClass(formData.docClass);

        this.restoreProductTableData()
        this.setFileList(formData)
      }).finally(()=>{
        _this.detailLoading = false
      });
    },
    getModifyApplyTrain(applyId){
      let _this = this
      if (applyId) {
        let trains = []
        queryModifyApplyTrain({ type: 'train', applyId: applyId }).then(res => {
          if (res.data) {
            res.data.forEach(item => {
              trains.push({
                url: item.fileIds,
                name: item.files[0].fileName
              })
            })
          }
          _this.trains = trains
        })
      }
    },
    rest(){
      let _this = this
      _this.activeName = "info"
      _this.formData= {
        whetherCustomer: undefined,
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "01",
        docId: undefined,
        deptId: _this.userInfo.dept.deptId,
        deptName: _this.userInfo.dept.deptName,
        userName: _this.userInfo.userName,
        nickName: _this.userInfo.nickName,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: new Date().getTime(),
        appendixes: undefined, //附件
        standardDoc: undefined, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        noteLinks: undefined, // 关联记录
        noteDocLinks: undefined,
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined,
        step: undefined,
        presetUserList: [],
        batch: undefined,
        productLine: undefined,
        process: undefined,
        productType: undefined,
        securityLevel: undefined,
        haveLinkFile: undefined,
        partNumber: undefined,
        custodyDeptId: undefined,
        shelfLife: undefined,
        fileEffectiveDate: undefined,
        revisionDate: undefined,
        compliance: undefined,
        recoveryConfirm: undefined,
        productVersion: undefined,
      }
      // 初始化产品表格数据
      _this.productTableData = [{
        materialCode: '',
        productVersion: '',
        materialDescription: ''
      }]
      // 初始化文件列表数据
      _this.standardDocfileList = []
      _this.appendixesfileList = []
    },
    procInstInfoAndStatus(procInstId){
      let _this = this
      procInstInfoAndStatus(procInstId).then((res) => {
        if (res) {
          _this.procDefKey = res.procDefKey
          _this.pListData = res
        }else {
          _this.pListData = {procInstId:procInstId}
        }
        _this.getExtAttributeModel()
      });
    },
    handelConfirm(list,type) {
      let trains = []
      list.forEach(item=>{
        trains.push({
          fileIds: item.url,
          fileName: item.name,
          userName: this.userInfo.userName,
          deptId: this.userInfo.deptId,
          docId: this.formData.docId,
          applyId: this.formData.id,
          type:type
        })
      })
      let data = {
        applyId:this.formData.id,
        type: type,
        trains: trains
      }
      updateModifyApplyTrainList(data);
    },
    nodeShow(code){
      let _this = this
      if (_this.nodeDetail) {
        return !!_this.nodeDetail[code]
      }else  {
        return  false
      }
    },
    nodeFunCondition(code){
      let _this = this
      let nodeDetail = _this.nodeDetailList.find(item=>item.code===code)
      if (nodeDetail&&nodeDetail.funCondition) {
        return JSON.parse(nodeDetail.funCondition)
      }else {
        return undefined
      }
    },
    // 获取文件类型的流程配置
    async getByUpDocClassAndBizType(docClass) {
      let _this = this
      let { data } = await getByUpDocClassAndBizType(docClass, _this.pButton)
      _this.procDefKey = data && data.flowKey ? data.flowKey : "";
      _this.getWorkflowprocesskey()
    },
    getWorkflowprocesskey() {
      let _this = this
      _this.loading = true
      _this.pListData = {}
      if (_this.procDefKey) {
        workflowprocesskey(_this.procDefKey).then((data) => {
          getStartActdef(data.data.procDefId).then((res) => {
            _this.pListData = res.data;
            this.getExtAttributeModel()
          });
        });
      }else {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
      }
    },
    getExtAttributeModel(){
      let _this = this
      let procDefId = _this.pListData.procDefId
      let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
      if (procDefId&&curActDefId) {
        _this.getNodeDetailInfo()
        getExtAttributeModel(
          procDefId,
          curActDefId
        ).then((res) => {
          console.log("扩展属性====>", res);
          let kuozhanshujuBool = {}
          let kuozhanshuju = {}
          res.data.forEach(item=>{
            if (item.objType==='Boolean') {
              kuozhanshujuBool[item.objKey] = item.objValue
            } else {
              kuozhanshuju[item.objKey] = item.objValue
            }
          })
          _this.kuozhanshujuBool = kuozhanshujuBool;
          _this.kuozhanshuju = kuozhanshuju;
        }).finally(()=>{
          _this.loading = false
        });
      }else {
        _this.kuozhanshujuBool = {}
        _this.kuozhanshuju = {}
        _this.loading = false
      }
    },
    attributeModelBool(val){
      if (this.kuozhanshujuBool&&this.kuozhanshujuBool!=={}) {
        let obj = this.kuozhanshujuBool[val]
        return !!obj&&obj==='true'
      }else {
        return false
      }
    },
    attributeModel(val){
      return this.kuozhanshuju[val]
    },
    getNodeDetailInfo(){
      let _this = this
      let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
      if (_this.pListData&&curActDefId&&_this.formData.docClass) {
        getInfo(_this.formData.docClass,_this.pButton,curActDefId).then(res=>{
          let nodeDetail = {}
          res.data.forEach(item=>{
            nodeDetail[item.code] = true
          })
          _this.nodeDetail = nodeDetail
          _this.nodeDetailList = res.data
          _this.initStatus()
        })
      }
    },
    async initStatus() {
      let _this = this
      _this.editStatus = _this.nodeShow('bianji') && _this.workflowStatus
      _this.submitLabel = _this.nodeShow('pizhun') ? _this.$t(`file_handle.change_approve`) : _this.nodeShow('shenhe')?_this.$t(`file_handle.change_auditing`):_this.$t('doc.this_dept_annex')
      if (!_this.batchStatus) {
        if (_this.nodeShow('whether_train')) {
          let funCondition = _this.nodeFunCondition('whether_train')
          if (funCondition) {
            if (funCondition.limitValue && !_this.formData.yNTrain) {
              _this.formData.yNTrain = funCondition.limitValue
            }
            _this.trainStatus = funCondition.validate
          }
        }
        _this.transferStatus = await _this.$refs.transferFlow.getTransferStatus(_this.pListData)
        if (_this.approvalStatus) {
          _this.jointReviewRedirect()
          await _this.setPresetUserList()
          if (_this.$refs.approvalBox) {
            _this.$refs.approvalBox.init()
          }
        }
      }
    },
    beforeUpload(file) {
      let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
      const isLt1M = file.size / 1024 / 1024 < 10;
      if (!isLt1M) {
        this.$message.error(this.$t(`file_handle.change_upload_limit`));
        return false;
      }
      if (this.uploadType.includes(fileType)) {
        return true;
      } else {
        this.$message({
          message: this.$t(`file_handle.change_upload_only`) + this.uploadType.toString() + this.$t(`file_handle.change_format`),
          type: "warning",
        });
        return false;
      }
    },
    closeAS() {
      this.viewShow = false;
    },
    close() {
      this.$emit("close")
    },
    handlePreview(id) {
      this.viewId = id;
      this.$refs.viewRef.handleOpenView(id);
      this.viewShow = true;
    },
    //不需要验证必填的保存
    saveForm() {
      let _this = this
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
        return;
      }

      _this.loading = true
      let formData = JSON.parse(JSON.stringify(_this.formData))
      formData=this.processFormDataForSubmit(formData)
      formData.recordStatus = "draft";
      //变更操作（新增、修订、作废）的提交和保存的类型区分统一使用recordStatus字段（draft：草稿, doing:进行中，done:已完成，deleted:作废）
      formData.docLinks = [];
      if (_this.$refs.linkFile&&_this.$refs.linkFile.dataList) {
        _this.$refs.linkFile.dataList.forEach((element) => {
          formData.docLinks.push({
            // linkId: element.linkId,
            fileId: element.fileId,
            docId: element.docId,
            versionId: element.versionId,
            versionValue: element.versionValue,
            docName: element.docName,
            docClass: element.docClass,
            status: 2,
          });
        });
      }
      formData.noteLinks = [];
      if (_this.$refs.linkNote&&_this.$refs.linkNote.dataList) {
        let noteLinks = _this.$refs.linkNote.dataList;
        noteLinks.forEach(item=>{
          item.status = 2
        })
        formData.noteLinks = noteLinks;
      }
      formData.noteDocLinks = [];
      if (_this.$refs.linkDoc&&_this.$refs.linkDoc.dataList) {
        let noteDocLinks = _this.$refs.linkDoc.dataList;
        noteDocLinks.forEach(item=>{
          item.status = 2
        })
        formData.noteDocLinks = noteDocLinks;
      }

      if(_this.$refs.linkRecord&&_this.$refs.linkRecord.dataList) {
        formData.recordLinks = _this.$refs.linkRecord.dataList;
      }
      formData.appendixes = [];
      formData.remarkDoc = [];
      if (_this.batchStatus) {
        _this.loading = false
        this.$emit("close",formData,_this.dataListIndex)
        return
      }
      if (formData.id) {
        updateModifyApply(formData).then((res) => {
          if (res.code===200) {
            _this.$modal.msgSuccess(_this.$t(`file_handle.change_save_succ`));
            _this.loading = false
          }
        });
      } else {
        let wf_receivers = [];
        wf_receivers.push({
          receiveUserId: _this.userInfo.userName,
          receiveUserOrgId: _this.userInfo.deptId
        });
        formData.bpmClientInputModel = {
          model: {
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_procTitle: formData.docName,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_curActDefName: _this.pListData.actDefName,
            wf_curActDefId: _this.pListData.actDefId,
            wf_nextActDefName: _this.pListData.actDefName,
            wf_nextActDefId: _this.pListData.actDefId,
          },
          order: _this.order,
          mark: _this.mark,
          type: _this.type
        };
        formData.editStatus = _this.editStatus
        addModifyApply(formData).then((res) => {
          if (res.code===200) {
            _this.$modal.msgSuccess(_this.$t(`file_handle.change_succ`));
            _this.formData.id = res.data.businessKey;
            _this.procInstId = res.data.procInstId
            _this.procInstInfoAndStatus(res.data.procInstId)
            _this.loading = false
          }
        });
      }
    },
    handleBackFlowToOne(){
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_withdraw_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value)=>{
          if(!value) {
            return _this.$t(`file_handle.change_fill_withdrawal_text`);
          }
        },
      }).then(({ value })=> {
        _this.loading = true
        getRecordbyPorcInstId(_this.procInstId).then(async res => {
          for (const item of res.data) {
            let revocationMsg = _this.$t(`file_handle.revocation.mgs`) == 'file_handle.revocation.mgs'?'':_this.$t(`file_handle.revocation.mgs`)
            let bpmClientInputModel = {
              model: {
                wf_procInstId: _this.procInstId,
                wf_procDefKey: item.procDefKey,
                wf_procDefId: item.procDefId,
                wf_procTitle: _this.formData.docName.trim(),
                wf_curActInstId: item.curActInstId,
                wf_sendUserId: item.recUserId,
                wf_sendUserOrgId: item.recOrgId,
                wf_curComment: revocationMsg+value,
                wf_curActDefId: item.curActDefId,
                wf_curActDefName: item.curActDefName,
              },
              review: true,
              applyStatus: false,
              bizType: _this.pButton,
              status: 'draft',
              type: _this.type,
              mark: _this.mark,
              order: 0,
            };
            //抢单模式多个待办只执行一个 第二个执行返回null跳出循环
            let res1 = await backFlowToOne(bpmClientInputModel)
            if (!res1.data) {
              break;
            }
          }
          _this.close(true);
        })
      })
    },
    deleteForm(){
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_revoke_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value)=>{
          if(!value) {
            return _this.$t(`file_handle.change_fill_revocation_text`);
          }
        },
      }).then(({ value })=> {
          _this.loading = true
          let formData = {
            id: _this.formData.id,
            bpmClientInputModel: {
              model: {
                wf_procDefKey: _this.procDefKey,
                wf_procDefId: _this.pListData.procDefId,
                wf_procInstId: _this.pListData.procInstId,
                wf_sendUserId: _this.userInfo.userName,
                wf_sendUserOrgId: _this.userInfo.deptId,
                wf_curActDefName: _this.pListData.curActDefName,
                wf_curActDefId: _this.pListData.curActDefId,
                wf_curActInstId: _this.pListData.curActInstId,
                wf_curComment: value,
              },
              order: _this.order,
              type: _this.type,
              mark: _this.mark,
              review: true,
              applyStatus: false,
            },
            recordStatus: 'cancel',
            editStatus: false
          }
          addModifyApply(formData).then((res) => {
            if (res.code === 200) {
              _this.$modal.msgSuccess(_this.$t(`file_handle.change_revoke_success`));
              _this.close(true);
            }
          });
        })
    },
    transferForm(){
      let _this = this
      _this.$refs.transferFlow.init(_this.pListData,_this.formData.id,_this.type,_this.order,_this.pButton)
    },
    rejectPreviousStep(){
      let _this = this
      _this.$prompt(_this.$t(`file_handle.reject_previous_step_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value)=>{
          if(!value) {
            return _this.$t(`doc.this_dept_ins_summary_remark`);
          }
        },
      }).then(async({ value }) => {
        _this.loading = true
        let bpmClientInputModel = {
          model: {
            wf_procInstId: _this.procInstId,
            wf_procDefKey: _this.procDefKey,
            wf_procDefId: _this.pListData.procDefId,
            wf_procTitle: _this.formData.docName.trim(),
            wf_curActInstId: _this.pListData.curActInstId,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_curComment: value,
            wf_curActDefId: _this.pListData.curActDefId,
            wf_curActDefName: _this.pListData.curActDefName,
          },
          bizType: _this.pButton,
          review: true,
          applyStatus: false,
          type: _this.type,
          mark: _this.mark,
        };
        backPreviousStep(bpmClientInputModel).then(res=>{
          _this.close(true);
        })
      })
    },
    // 提交
    async submitForm() {
      let _this = this
      // 首先页签调整为 信息内容
      _this.activeName = 'info'
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
        return;
      }

      // 构建文件数据
      _this.buildFileData()
      //审核
      if (_this.nodeShow('shenhe')||_this.nodeShow('pizhun')) {
        if (_this.approvalStatus) {
          _this.formSubmit = _this.$refs.approvalBox.formSubmit
        }
        if (_this.formSubmit.pass===undefined) {
          _this.$modal.msgError(_this.submitLabel+_this.$t(`file_handle.change_result_not_null`));
          return;
        }
        // 验证是否填写了审核意见
        if(!_this.formSubmit.pass&&_this.formSubmit.summary.trim() == '') {
          _this.$modal.msgError(_this.$t(`doc.this_dept_pls_fill`)+_this.submitLabel+_this.$t(`doc.this_dept_comments`));
          return;
        }
      }
      let direction = _this.pListData.actDefOrder>_this.order
      //培训记录
      if (direction&&_this.nodeShow('page_oper_add_train_record')&&_this.formData.yNTrain!==_this.no) {
        let funCondition = _this.nodeFunCondition('page_oper_add_train_record')
        if (!funCondition||(funCondition&&funCondition.validate)) {
          if (!(_this.trains&&_this.trains.length>0)) {
            _this.$modal.msgError(_this.$t(`doc.this_dept_pls_upload_train_file`));
            return true;
          }
        }
      }
      let dialogVisible = true
      if (!!_this.$refs["elForm"]) {
        let valid = await _this.$refs["elForm"].validate()
        if (!valid) {
          dialogVisible = false
        }
      }
      if (await _this.validate()){
        return
      }
      this.loading = true;
      this.formData.docLinks = [];
      if (_this.$refs.linkFile&&_this.$refs.linkFile.dataList) {
        let docLinks = _this.$refs.linkFile.dataList;
        docLinks.forEach(item=>{
          item.status = 2
        })
        _this.formData.docLinks = docLinks
      }
      _this.formData.noteLinks = [];
      if (_this.$refs.linkNote&&_this.$refs.linkNote.dataList) {
        let noteLinks = _this.$refs.linkNote.dataList;
        noteLinks.forEach(item=>{
          item.status = 2
        })
        _this.formData.noteLinks = noteLinks
      }

      _this.formData.noteDocLinks = [];
      if (_this.$refs.linkDoc&&_this.$refs.linkDoc.dataList) {
        let noteDocLinks = _this.$refs.linkDoc.dataList;
        noteDocLinks.forEach(item=>{
          item.status = 2
        })
        _this.formData.noteDocLinks = noteDocLinks
      }
      if(_this.$refs.linkRecord&&_this.$refs.linkRecord.dataList) {
        this.formData.recordLinks = this.$refs.linkRecord.dataList;
      }
      this.formData.appendixes = [];
      this.formData.remarkDoc = [];
      this.loading = false;
      if (!_this.approvalStatus) {
        _this.jointReviewRedirect()
        await _this.setPresetUserList()
        this.dialogVisible = true;
      }else {
        _this.$confirm(_this.$t(`file_handle.submit_text`), _this.$t(`file_handle.change_tip`), {
          confirmButtonText: _this.$t(`file_handle.change_confirm`),
          cancelButtonText: _this.$t(`doc.this_dept_abolish`),
          type: "warning",
        })
          .then(() => {
            _this.handleWorkflowSubmit({})
          })
      }
    },
    async setPresetUserList(){
      let _this = this
      if(_this.nodeShow('preset_countersign')){
        let funCondition = _this.nodeFunCondition('preset_countersign')
        if (funCondition&&funCondition.nodeCode&& funCondition.nodeCode.length > 0&&funCondition.groupId) {
          let users = [];
          let res = await listDistributeGroupDetail({ groupId: funCondition.groupId })
          res.rows.forEach(item=>{
            users.push({
              userName: item.receiveUserName,
              nickName: item.receiveNickName,
              deptId: item.receiveUserDeptId,
              deptName: item.receiveUserDept,
            })
          })
          funCondition.nodeCode.forEach(nodeCode => {
            let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
            if (preset) {
              preset.users = JSON.stringify(users)
            } else {
              _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
            }
          })
        }
      }
      // 下个环节预选直属部门领导
      if(_this.nodeShow('next_set_leader')){
        let funCondition = _this.nodeFunCondition('next_set_leader')
        if (funCondition&&funCondition.nodeCode&& funCondition.nodeCode.length > 0) {
          let users = undefined;
          if (funCondition.validate) {
            let res = await getLeaderByUserName(_this.formData.userName)
            if (res.data) {
              users = [{userName:res.data.userName, nickName:res.data.nickName, deptId:res.data.deptId, deptName:res.data.dept.deptName}]
            }
          } else {
            let leader = _this.$store.getters.leader
            if (leader) {
              users = [{ userName: leader.userName,nickName: leader.nickName,deptId: leader.deptId,deptName: leader.dept.deptName}]
            }
          }
          if (users) {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          }
        }
      }
      //设定流程默认执行人
      let defaultStaff = []
      if (this.formData.presetUserList.length>0) {
        defaultStaff.push(...JSON.parse(JSON.stringify(this.formData.presetUserList)))
      }
      if(_this.nodeShow('cdxmd')&&_this.formData.batch){
        // 文件类型设置中是否设置了 需要谁驳回就再只发送给驳回的人
        let funCondition = _this.nodeFunCondition('cdxmd')
        if (funCondition&&funCondition.validate) {
          //查询本次驳回有哪些人员
          let res = await listWorkflowLog({ batch: _this.formData.batch, nextDefId: _this.pListData.curActDefId ,havaDetail:true})
          let nodeCode = ""
          let users = []
          res.rows.forEach(item => {
            nodeCode = item.actDefId
            users.push({userName:item.sender, nickName:item.nickName, deptId:item.senderDeptId, deptName:item.deptName})
          })
          if (defaultStaff.length > 0) {
            let staff = defaultStaff.find(item => item.nodeCode = nodeCode)
            if (staff) {
              staff.users = JSON.stringify(users)
            }
          } else {
            defaultStaff.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
          }
        }
      }
      _this.defaultStaff = defaultStaff
      _this.searchQuery.pass = _this.formSubmit.pass
      let hideNodeCode = []
      //下一环节未预选人员隐藏
      if (_this.nodeShow('xyhjwyxryyc')){
        let funCondition = _this.nodeFunCondition('xyhjwyxryyc')
        if (funCondition&&funCondition.nodeCode) {
          let length = funCondition.nodeCode.length
          //下一环节隐藏范围 都隐藏
          hideNodeCode = funCondition.nodeCode
          //过滤有预选人员的环节
          defaultStaff.forEach(item=>{
            if (item.users) {
              let users = JSON.parse(item.users)
              if (hideNodeCode.includes(item.nodeCode)&&users&&users.length>0){
                hideNodeCode = hideNodeCode.filter(code=>code!==item.nodeCode)
              }
            }
          })
          //配置了反向节点 隐藏范围环节内都没预选人员 过滤掉反向节点
          if (funCondition.neNodeCode&&hideNodeCode.length===length) {
            hideNodeCode = hideNodeCode.filter(code=>!funCondition.neNodeCode.includes(code))
          }
          //填写了限定值 只能显示最多限定的数量
          if (funCondition.limitValue) {
            let limitValue = Number(funCondition.limitValue)
            //总数-隐藏数=显示数 显示数>限定数量
            if (!isNaN(limitValue)&&(length-hideNodeCode.length)>limitValue){
              //倒叙再插回去
              let reverse = funCondition.nodeCode.reverse()
              for (let item of reverse) {
                if (!hideNodeCode.includes(item)) {
                  hideNodeCode.push(item)
                }
                if ((length-hideNodeCode.length)<=limitValue) {
                  break
                }
              }
            }
          }
          // if (funCondition.validate) {
            //验证开启 配置了反向节点 隐藏范围环节内都有预选人员 增加反向节点
            if (funCondition.neNodeCode&&hideNodeCode.length!==length&&hideNodeCode.length===0) {
              defaultStaff.forEach(item=>{
                if (funCondition.neNodeCode.includes(item.nodeCode)) {
                  hideNodeCode.push(item.nodeCode)
                }
              })
            }
          // }
        }
      }
      //隐藏环节列表
      _this.hideNodeCode = hideNodeCode
    },
    jointReviewRedirect(){
      let _this = this
      if(_this.nodeShow('hscdx')){
        getRecordbyPorcInstId(_this.procInstId).then(res=>{
          if (res.data.length===1){
            let query={
              docClass: _this.formData.docClass,
              bizType: _this.pButton,
              code: "cdxmd",
              batch: _this.formData.batch,
            }
            getRedirectDefId(query).then(res1=>{
              if (res1.data) {
                let funCondition = _this.nodeFunCondition('hscdx')
                if (funCondition&&funCondition.nodeCode&&funCondition.nodeCode.length===1) {
                  let next = res1.data.find(item=>item.nextDefId===funCondition.nodeCode[0])
                  if (next) {
                    _this.redirectDefId = next.nextDefId
                    _this.redirectReceivers = JSON.parse(next.receiver)
                    _this.redirectOrder = next.actDefOrder
                  }
                }
              }
            })
          }
        })
      }
    },
    // 审批结论选择
    commentItemSelect(val) {
      let lang = this.$t(`doc.this_dept_pass`)
      if(val) {
        lang = this.$t(`doc.this_dept_pass`)
      } else {
        lang = this.$t(`doc.this_dept_not_pass`)
      }
      let summary = this.formSubmit.summary
      // 赋值审核意见 this.formSubmit.summary = lang +' '+summary
      this.formSubmit.summary = lang
    },
    async validate() {
      // 验证是否填写了审核意见
      let _this = this
      let validate = false
      // 验证环节参与人员是否选择
      if (_this.nodeShow('top_btn_preset_user')){
        let nodeCode = this.pListData.actDefId || this.pListData.curActDefId
        let bool = await _this.$refs.presetUser.validate(_this.formData.docClass,_this.pButton,_this.formData.presetUserList,nodeCode)
        if (bool) {
          _this.$modal.msgWarning("请完善环节参与人员");
          return true;
        }
      }
      if (!_this.formData.id) {
        let res1 = await selectStatusByDocId({ versionId: _this.formData.versionId, notInDraft: false })
        if (res1.data != 0) {
          _this.$modal.msgWarning(res1.msg);
          validate = true
        }
      }
      return false;
    },
    // 签章生效
    handleSignEffective() {
      let self = this
      if(self.formData.docId == null) {
        self.$modal.alert(self.$t(`doc.this_dept_pls_gen_code_gen_cover_relase`));
        return false;
      }
      if(self.formData.isSignature == 'E' || self.formData.isSignature == 'Y') {
        self.$modal.alert(self.$t(`file_handle.change_signature_text1`));
        return false;
      }
      self.$modal.msgSuccess(self.$t(`file_handle.change_signature_text2`));
      self.loading = true
        signEffective(self.formData.id,'effective').then((res) => {
          if (res.code===200) {
            self.$modal.alert(self.$t(`file_handle.change_signature_text3`));
            self.formData.encryptFileId = res.data
            self.formData.isSignature = 'E'
          } else {
            self.$modal.alert(res.msg);
          }
          self.loading = false
        });
    },
    // 执行发布（推送文件到文件台账）
    handlePublish() {
      let self = this
      if (self.nodeShow('recovery_confirm')&&self.formData.recoveryConfirm!==self.yes) {
        self.$modal.alert(self.$t(`doc.recovery_confirm_validate`));
        return false;
      }
      /*
      if(self.formData.docId == null) {
        self.$modal.alert("温馨提示：请先执行【生成编号】，然后执行【签章生效】，最后再【执行发布】。");
        return false;
      } else if(self.formData.isSignature == 'N') {
        self.$modal.alert("温馨提示：请先执行【签章生效】，最后再【执行发布】。");
        return false;
      }
      */
      self.$modal.confirm(self.$t(`file_handle.change_signature_text4`)+self.formData.docName+'》，' + self.$t(`file_handle.change_signature_text5`)).then(function() {
        // 执行发布的签章
        self.loading = true
        signEffective(self.formData.id,'publish').then((res) => {
          if (res.code===200) {
            self.$modal.msgSuccess(self.$t(`file_handle.change_signature_text6`));
            self.formData.encryptFileId = res.data
            self.formData.isSignature = 'Y'
            self.handleWorkflowSubmit('publish');
          } else {
            self.$modal.alert(res.msg);
          }
          self.loading = false
        });
      }).then(() => {
        // 取消操作
      }).catch(() => {});
    },
    // 下载履历
    async downloadResume() {
      let self = this
      if (!self.formData.id) {
        self.$modal.msgError(self.$t(`doc.this_dept_no_apply_id`));
        return;
      }

      try {
        self.loading = true
        const response = await downloadResumeApi(self.formData.id,'DISUSE')

        // 创建下载链接
        const blob = new Blob([response], { type: 'application/octet-stream' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        // 设置下载文件名，可以根据需要调整
        const fileName = `download_resume_${self.formData.id}.pdf`
        link.download = fileName

        // 触发下载
        document.body.appendChild(link)
        link.click()

        // 清理
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        self.$modal.msgSuccess(self.$t(`doc.download_resume`) + self.$t(`doc.this_dept_operation_succ`))
      } catch (error) {
        console.error('下载履历失败:', error)
        self.$modal.msgError(self.$t(`doc.download_resume`) + self.$t(`doc.this_dept_operation_fail`))
      } finally {
        self.loading = false
      }
    },
    //提交流程
    handleWorkflowSubmit(invokeFrom) {
      let _this = this
      let formData = JSON.parse(JSON.stringify(_this.formData))
      formData=this.processFormDataForSubmit(formData)
      let wf_receivers = [];
      let wf_nextActDefId = null
      let wf_nextActDefName = null
      let direction = null
      let prochild = _this.approvalStatus? _this.$refs.approvalBox:_this.$refs.prochild
      if(typeof(invokeFrom) == 'object'  ) {
        if (prochild.receiveUserList.length < 1 &&prochild.nextData.actDefType!=='endEvent') {
          if (_this.approvalStatus) {
            _this.$message.warning(_this.$t(`doc.this_dept_directory_select_user_alert`,[prochild.curOptionLabel]));
          }else {
            _this.$message.warning(_this.$t(`doc.this_dept_select_user_alert`));
          }
          return;
        }
        prochild.receiveUserList.forEach((element) => {
          wf_receivers.push({
          receiveUserId: element.id,
          receiveUserOrgId: element.parentId,
          });
        });
        wf_nextActDefId = prochild.nextData.actDefId;
        wf_nextActDefName = prochild.nextData.actDefName;
        direction = _this.pListData.actDefOrder>_this.order
      } else if (typeof(invokeFrom) == 'string' && invokeFrom == 'publish') {
        // 来源于按钮【执行发布】
        wf_nextActDefId = 'end'
        wf_nextActDefName = '结束'
        direction = true
      }
        // 显示加载中
        _this.detailLoading = true
        _this.flowStepLoading = true
        if (_this.pListData && _this.pListData.procInstId) {
          //流程执行参数
          formData.bpmClientInputModel = {
            model: {
              wf_procDefKey: _this.procDefKey,
              wf_procDefId: _this.pListData.procDefId,
              wf_procTitle: _this.formData.docName,
              wf_curActInstId: _this.pListData.curActInstId,
              wf_sendUserId: _this.userInfo.userName,
              wf_sendUserOrgId: _this.userInfo.deptId,
              wf_receivers: wf_receivers,
              wf_nextActDefId: wf_nextActDefId,
              wf_curComment: _this.formSubmit.summary,
              wf_curActDefId: _this.pListData.curActDefId,
              wf_curActDefName: _this.pListData.curActDefName,
              wf_nextActDefName: wf_nextActDefName,
            },
            order: _this.pListData.actDefOrder,
            review: _this.nodeShow('shenhe')||_this.nodeShow('pizhun'),
            applyStatus: _this.formSubmit.pass,
            type: _this.type,
            mark: _this.mark,
            direction: direction,
            step: _this.attributeModel("step")
          };
        }else{
          //创建流程参数
          formData.bpmClientInputModel = {
            type: _this.type,
            model: {
              wf_procTitle:  _this.formData.docName,
              wf_nextActDefId: wf_nextActDefId,
              wf_procDefId: _this.pListData.procDefId,
              wf_procDefKey: _this.procDefKey,
              wf_sendUserId: _this.userInfo.userName,
              wf_sendUserOrgId: _this.userInfo.deptId,
              wf_receivers: wf_receivers,
              wf_curActDefName: _this.pListData.actDefName,
              wf_curActDefId: _this.pListData.actDefId,
              wf_nextActDefName: wf_nextActDefName,
            },
            order: _this.pListData.actDefOrder,
            review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
            applyStatus: _this.formSubmit.pass,
            mark: _this.mark,
            direction: direction,
            step: _this.attributeModel("step")
          };
        }
        if (_this.nodeShow('top_btn_publish_file')&&wf_nextActDefId==='end') {
          //办结
          formData.recordStatus = 'done'
          formData.bpmClientInputModel.jointReview = false
        } else {
          //进行中
          formData.recordStatus = 'doing'
          formData.bpmClientInputModel.jointReview = prochild.nextData.multi
        }
        if(_this.nodeShow('hscdx')){
          formData.bpmClientInputModel.batch = formData.batch
          formData.bpmClientInputModel.redirectDefId=_this.redirectDefId
          formData.bpmClientInputModel.redirectReceivers = _this.redirectReceivers
          if (_this.redirectOrder) {
            formData.bpmClientInputModel.order = _this.redirectOrder
          }
        }
        formData.editStatus = _this.editStatus
        formData.presetUserEdit = _this.nodeShow('top_btn_preset_user') || _this.nodeShow('next_set_leader')
        formData.customerEdit = _this.nodeShow('whether_customer_record')
        addModifyApply(formData).then((res) => {
          if (res.code===200) {
            //  _this.$modal.msgSuccess("提交成功");
            _this.$message({
                message: this.$t(`doc.this_dept_process_sub_succ`),//提示的信息
                type:'success',　　//类型是成功
                duration:1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
                onClose:()=>{
                  _this.detailLoading = false
                  _this.flowStepLoading = false
                  _this.dialogVisible = false;
                  _this.close();
                }
            });
          }
        });
    },
    resetForm() {
      this.$refs["elForm"].resetFields();
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    normalizerDept(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    // 树形结构数据转化成列表结构数据
    deepTraversal(data) {
      const result = [];
      data.forEach((item) => {
        const loop = (data) => {
          result.push({
            deptId: data.deptId,
            deptName: data.deptName,
            notPrint: data.notPrint,
            nums: data.nums,
          });
          let child = data.children;
          if (child) {
            for (let i = 0; i < child.length; i++) {
              loop(child[i]);
            }
          }
        };
        loop(item);
      });
      return result;
    },
    handleMonitor() {
      this.monitorDrawerVisible = true;

      this.$nextTick(() => {
        this.$refs.monitorDrawer.init(this.pListData.procInstId);
      });
    },
    handelpbohuiqicaoren(){
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_reject_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        inputValue: _this.formSubmit.summary,
        confirmButtonText: _this.$t(`file_handle.change_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value)=>{
          if(!value) {
            return this.$t(`file_handle.change_fill_reject_text`);
          }
        },
      })
        .then(({ value }) => {
          // _this.$modal.confirm('是否确认将流程驳回至编制人？').then(res => {
          let backarr = {
            model: {
              wf_procDefKey: _this.procDefKey,
              wf_businessKey: _this.formData.id,
              wf_curActInstId: _this.pListData.curActInstId,
              wf_sendUserId: _this.userInfo.userName,
              wf_sendUserOrgId: _this.userInfo.deptId,
              wf_procTitle: _this.formData.docName,
              wf_procDefId: _this.pListData.procDefId,
              wf_curComment: value,
              wf_curActDefName: _this.pListData.curActDefName,
              wf_curActDefId: _this.pListData.curActDefId,
              wf_receivers: [
                {
                  receiveUserId: _this.formData.userName,
                  receiveUserOrgId: _this.formData.deptId
                }
              ],
            },
            review: true,
            applyStatus: false,
            type: _this.type,
            step: _this.attributeModel("step"),
            bizType: _this.pButton
          };
          workflowbacktostart(backarr).then((response) => {
            this.$modal.msgSuccess("file_handle.change_reject_succ");
            _this.close();
          });
        })
    },
    shengchengbianhao() {
      let _this =this
      _this.jiluliushuihao = [];
      getDocNoByApplyId(_this.formData.id).then((res) => {
        _this.formData.docId = res.data.docId;
        _this.docIdData = res.data;
        if (_this.formData.recordLinks != null) {
          var linids = [];
          _this.formData.recordLinks.forEach((element, i) => {
            linids.push(element.fileId);
          });
          if (linids != "") {
            getRecordDocNoByLinkId({
              fileIds: linids.join(","),
              applyId: _this.formData.id,
            }).then((res) => {
              _this.jiluliData = res.data;
              res.data.forEach((element, i) => {
                _this.formData.recordLinks.forEach((val, ix) => {
                  if (val.linkId == element.linkId) {
                    _this.formData.recordLinks[ix].docId = element.docId;
                  }
                });
                _this.jiluliushuihao.push(element);
              });
            });
          }
        }
      });

      _this.shenchenbianhao = true;
    },
    onProjectChange(val){
      let _this = this
      _this.formData.projectId = val.id
      _this.formData.projectName = val.name
    },
    handleCloseChange(){
      this.dealDrawerShow = false
    },
    handleDeal(formData) {
      let _this = this
      _this.dealDrawerShow = true;
      _this.$nextTick(() => {
        console.log(_this.formData)
        _this.$refs.dealDrawer.init({type:formData.invokeType,preChangeCode:formData.invokeId});
      });
    },
    selectPresetUser(){
      let _this = this
      _this.$nextTick(()=>{
        let nodeCode = this.pListData.actDefId || this.pListData.curActDefId
        _this.$refs.presetUser.init(null,null,_this.formData.docClass,_this.pButton,_this.formData.presetUserList,nodeCode)
      })
    },
    async selectHandlePresetUser(source, index, data) {
      let _this = this
      _this.$set(_this.formData, "presetUserList", data)
      if (_this.approvalStatus) {
        await _this.setPresetUserList()
        if (_this.$refs.approvalBox) {
          _this.$refs.approvalBox.init()
        }
      }
    },
    getByDocClass(docClass) {
      let _this = this
      _this.loading = true
      getInfoBy({type:'formShow',docClass:docClass}).then(async res => {
        this.isShowPart = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.isShowPart = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'formCustomerShow',docClass:docClass}).then(async res => {
        this.isCustomerShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.isCustomerShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'projectNameSecurityKeywordByteShow',docClass:docClass}).then(async res => {
        this.projectNameSecurityKeywordByteShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.projectNameSecurityKeywordByteShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'shelfLifeShow',docClass:docClass}).then(async res => {
        this.isShelfLifeShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.isShelfLifeShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })


      getInfoBy({type:'formDeviceShow',docClass:docClass}).then(async res => {
        this.isDeviceShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.isDeviceShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'formProductVersionShow',docClass:docClass}).then(async res => {
        this.isShowProductVersion = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.isShowProductVersion = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'systemClauseShow',docClass:docClass}).then(async res => {
        this.isSystemClauseShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.isSystemClauseShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'internalDocIdShow',docClass:docClass}).then(async res => {
        this.internalDocIdShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.internalDocIdShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'ecnCodeShow',docClass:docClass}).then(async res => {
        this.ecnCodeShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.ecnCodeShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'projectCodeShow',docClass:docClass}).then(async res => {
        this.projectCodeShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.projectCodeShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'complianceShow',docClass:docClass}).then(async res => {
        this.complianceShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.complianceShow = true
          }
        }
        _this.detailLoading = false
      }).catch(err => {
        console.log(err)
        _this.detailLoading = false
      })

      // 获取文件用途字段显示控制
      getInfoBy({type:'filePurposeShow',docClass:docClass}).then(async res => {
        this.filePurposeShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.filePurposeShow = true
          }
        }
      }).catch(err => {
        console.log(err)
        // 默认显示文件用途字段
        this.filePurposeShow = true
      })

      // 获取法规标准状态字段显示控制
      getInfoBy({type:'regulationStatusShow',docClass:docClass}).then(async res => {
        this.regulationStatusShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.regulationStatusShow = true
          }
        }
      }).catch(err => {
        console.log(err)
        this.regulationStatusShow = false
      })

      // 获取法规/标准发布日期字段显示控制
      getInfoBy({type:'regulationPublishDateShow',docClass:docClass}).then(async res => {
        this.regulationPublishDateShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.regulationPublishDateShow = true
          }
        }
      }).catch(err => {
        console.log(err)
        this.regulationPublishDateShow = false
      })

      // 获取法规/标准实施日期字段显示控制
      getInfoBy({type:'regulationImplementDateShow',docClass:docClass}).then(async res => {
        this.regulationImplementDateShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.regulationImplementDateShow = true
          }
        }
      }).catch(err => {
        console.log(err)
        this.regulationImplementDateShow = false
      })

      // 获取归属上级文件字段显示控制
      getInfoBy({type:'programVersionId',docClass:docClass}).then(async res => {
        this.programVersionId = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.programVersionId = true
          }
        }
      }).catch(err => {
        console.log(err)
        this.programVersionId = false
      })
    },
    handelRecoveryConfirm(){
      let _this = this
      updateById({id:_this.formData.id,recoveryConfirm:_this.formData.recoveryConfirm})
    },
    getDictLabel(dictValue) {
      const dictItem = this.dict.type.tenant_list.find(item => item.value === dictValue);
      return dictItem ? this.dictLanguage(dictItem) : dictValue;
    },
    // 从保存的数据恢复表格数据
    restoreProductTableData() {
      this.restoreFromLegacyData();
    },
    // 从原有数据格式恢复
    restoreFromLegacyData() {
      if (this.formData.partNumber) {
        const partNumbers = this.formData.partNumber.split(';');
        const descriptions = this.formData.partRemark ? this.formData.partRemark.split(';') : [];
        const productVersions = this.formData.productVersion ? this.formData.productVersion.split(';') : [];

        this.productTableData = partNumbers.map((code, index) => ({
          materialCode: code || '',
          productVersion: productVersions[index] || '',
          materialDescription: descriptions[index] || ''
        }));

      } else {
        this.productTableData = [{
          materialCode: '',
          productVersion: '',
          materialDescription: ''
        }];
      }
    },
    // 设置文件列表数据
    setFileList(formData) {
      let _this = this
      // 处理当前版本文档（用于作废展示）
      if (formData.preStandardDoc && formData.preStandardDoc.fileId) {
        _this.standardDocfileList = [
          {
            name: formData.preStandardDoc.fileName,
            url: formData.preStandardDoc.fileId
          },
        ];
      } else if (formData.standardDoc && formData.standardDoc.fileId) {
        // 兼容其他情况
        _this.standardDocfileList = [
          {
            name: formData.standardDoc.docName,
            url: formData.standardDoc.fileId
          },
        ];
      }

      // 处理附件
      _this.appendixesfileList = []
      if (formData.preAppendixes && formData.preAppendixes.length > 0) {
        formData.preAppendixes.forEach((element) => {
          _this.appendixesfileList.push({
            name: element.fileName,
            url: element.fileId,
          });
        });
      } else if (formData.appendixes && formData.appendixes.length > 0) {
        // 兼容其他情况
        formData.appendixes.forEach((element) => {
          _this.appendixesfileList.push({
            name: element.docName,
            url: element.fileId,
          });
        });
      }
    },
    // 构建文件数据用于提交
    buildFileData() {
      let _this = this
      // 构建标准文档数据
      if (_this.standardDocfileList.length > 0) {
        _this.formData.standardDoc = {
          docName: _this.standardDocfileList[0].name,
          fileId: _this.standardDocfileList[0].url
        }
      } else {
        _this.formData.standardDoc = null
      }

      // 构建附件数据
      _this.formData.appendixes = []
      if (_this.appendixesfileList.length > 0) {
        _this.appendixesfileList.forEach((file) => {
          _this.formData.appendixes.push({
            docName: file.name,
            fileId: file.url
          })
        })
      }
    }
  },
};
</script>
<style scoped>
.document_change_add{
  .fujian .el-textarea__inner {
    border: 0 solid #dcdfe6;
    padding: 0;
  }
}

/* 项目代码标签样式优化 */
.project-code-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
}

.project-code-tags .el-tag {
  margin: 0;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.project-code-tags span {
  display: inline-block;
  margin: 0 8px 8px 0;
  padding: 4px 8px;
  background-color: #f0f2f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
