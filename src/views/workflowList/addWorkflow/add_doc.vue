<template>
  <div class="document_change_add"  v-loading="loading||detailLoading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{$t(`doc.this_dept_add_file`)}}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button v-if="procInstId" @click="handleMonitor">{{ $t(`doc.this_dept_process_monitor`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_preset_user')&&workflowStatus&&!batchStatus" @click="selectPresetUser">{{ $t(`file_handle.change_select_people`) }}</el-button>
        <el-button v-if="editStatus&&procInstId&&!batchStatus" type="danger" @click="deleteForm" v-dbClick>{{ $t(`file_handle.change_revoke`) }}</el-button>
        <el-button v-if="procInstId&&workflowStatus&&!batchStatus" @click="transferForm" v-dbClick>{{ transferStatus?$t(`file_handle.transfer_return`):$t(`file_handle.transfer`) }}</el-button>
        <el-button v-if="procInstId&&!workflowStatus&&'doing'===formData.processStatus&&formData.createBy===userInfo.userName&&backFlowToOneStatus&&!batchStatus" type="danger" @click="handleBackFlowToOne" v-dbClick>{{$t(`file_handle.change_withdraw`)}}</el-button>
        <el-button v-if="nodeShow('top_btn_reject_previous_step')&&workflowStatus&&!batchStatus" @click="rejectPreviousStep()" type="danger">{{ $t(`dicts.flow_node_fun_list_top_btn_reject_previous_step`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_reject_drafter')&&workflowStatus&&!batchStatus" @click="handelpbohuiqicaoren()" type="danger">{{ $t(`file_handle.change_reject_to_preparer`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_generate_code')&&workflowStatus&&!batchStatus" @click="shengchengbianhao()" type="primary">{{ $t(`file_handle.change_generate_num`) }}</el-button>
        <!-- 【生成封面】、【签章生效】和【执行发布】一般出现在发布环节 -->
        <el-button v-if="nodeShow('publish_setup_time')&&workflowStatus&&!batchStatus" @click="setupStartTime()"  type="primary">{{ $t(`doc.this_dept_select_effective_date`) }}</el-button>
        <!--        下载-->
        <el-button  v-if="nodeShow('download_resume')&&workflowStatus"  @click="downloadResume()"
                    v-dbClick     type="primary">{{ $t(`doc.download_resume`) }}
        </el-button>
        <el-button v-if="nodeShow('top_btn_file_cover')&&workflowStatus&&!batchStatus" @click="handleCoverEffective()"  type="primary">{{ $t(`file_handle.change_generate_cover`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_setup_time')&&workflowStatus&&!batchStatus" @click="handleSignEffective()"  type="primary">{{ $t(`file_handle.change_signature_effc`) }}</el-button>
        <el-button v-if="nodeShow('top_btn_publish_file')&&workflowStatus&&!batchStatus" @click="handlePublish()"  type="primary">{{ $t(`file_handle.change_execute_release`) }}</el-button>
        <!-- 提交按钮在 非【执行发布】环节出现 -->
        <el-button v-if="!nodeShow('top_btn_publish_file')&&workflowStatus&&!batchStatus" type="primary" @click="submitFormConfirm" v-dbClick>{{ $t('doc.this_dept_annex') }}</el-button>
        <el-button v-if="editStatus||nodeShow('top_btn_save')||nodeShow('doc_recheck_act')" type="primary" @click="saveFormConfirm" v-dbClick>{{ $t(`doc.this_dept_save`) }}</el-button>
        <el-button @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <el-tabs v-if="procInstId" v-model="activeName">
      <el-tab-pane :label="$t(`doc.this_dept_info_content`)" name="info"></el-tab-pane>
      <el-tab-pane :label="$t(`doc.this_dept_approval_records`)" name="log"></el-tab-pane>
      <el-tab-pane :label="$t(`doc.dept_relevant_user`)" name="relevant_user" v-if="nodeShow('relevant_user')"></el-tab-pane>
    </el-tabs>
    <div class="dialog-body" v-show="activeName==='info'">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_base_msg`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row gutter="15" v-if="isProject">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_project`)" prop="projectId">
                  <el-select value-key="id" v-if="editStatus" clearable v-model.trim="project" @change="onProjectChange"  style="width:100%">
                    <el-option
                      v-for="item in projectList"
                      :key="item.id"
                      :label="item.name"
                      :value="{id:item.id,name:item.name}"
                    />
                  </el-select>
                  <span v-else>{{project.name}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label=" " prop="">
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_types`)" prop="docClass">
                  <span v-if="(procInstId||formData.batchId)&&docClassData">{{docClassData.className}}</span>
                  <treeselect
                    v-else
                    v-model.trim="formData.docClass"
                    :options="classLevelOptions"
                    :normalizer="normalizer"
                    :disable-branch-nodes="true"
                    :searchable="false"
                    :show-count="true"
                    :clearable="false"
                    :noOptionsText="$t(`doc.this_dept_no_data`)"
                    @select="selectDocClass"
                    :placeholder="$t(`doc.this_dept_select_type`)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_change_type`)" prop="changeType">
                  <span>{{ $t(`doc.this_dept_new_add`)}}</span
                  >
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15"  v-if="formData.classType===classTypeRecord">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_by_superior`) + `:`" prop="upVersionId">
                  <el-input
                    v-if="editStatus"
                    v-model="formData.upDocName"
                    :placeholder="$t(`doc.this_dept_pls_select`)"
                    readonly
                  >
                    <el-button
                      slot="append"
                      type="primary"
                      icon="el-icon-more"
                      @click="versionSearchInit('up')"
                    ></el-button>
                  </el-input>
                  <span v-else>{{formData.upDocName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_superior_file_name`)" prop="parentDocId">
                  <span>{{formData.parentDocId}}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_file_names`)" prop="docName">
                  <el-input
                    v-if="editStatus"
                    v-model="formData.docName"
                    :placeholder="$t(`doc.this_dept_insert_name`)"
                    clearable
                    :style="{ width: '100%' }"
                    maxlength="1000"
                    show-word-limit
                    :disabled="disabled"
                  ></el-input>
                  <span v-else>{{formData.docName}}</span>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_file_codes`)" prop="docId">
                  <span style="line-height:34px">{{ formData.docId }}</span>
                  <el-button
                    v-if="editStatus&&!(formData.classType===classTypeRecord&&!formData.parentDocId)&&nodeShow('page_oper_prepare_id')"
                    style="float: right;margin-right: 30px"
                    type="primary"
                    size="mini"
                    @click="prepareIdSelect('formData',formData.classType,formData.docClass,formData.parentDocId)"
                  >{{ $t(`doc.this_dept_select_pre_code`) }}</el-button>
                </el-form-item>
              </el-col>
            </el-row>
            <template v-if="internalDocIdShow">
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_internal_file_number`)" prop="internalDocId">
                    <el-input
                      v-if="editStatus"
                      v-model="formData.internalDocId"
                      :placeholder="$t(`doc.this_dept_insert`)+$t(`doc.this_dept_internal_file_number`)"
                      clearable
                      :style="{ width: '100%' }"
                      maxlength="50"
                      show-word-limit
                    ></el-input>
                    <span v-else>{{formData.internalDocId}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <template v-if="ecnCodeShow">
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_ecn_number`)" prop="ecnCode">
                    <el-input
                      v-if="editStatus"
                      v-model="formData.ecnCode"
                      :placeholder="$t(`doc.this_dept_insert`)+$t(`doc.this_dept_ecn_number`)"
                      clearable
                      :style="{ width: '100%' }"
                      maxlength="50"
                      show-word-limit
                    ></el-input>
                    <span v-else>{{formData.ecnCode}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_file_versions`)" prop="versionValue">
                  <el-input
                    v-if="editStatus"
                    v-model="formData.versionValue"
                    :disabled="!versionIsUpdateShow"
                    :placeholder="$t(`doc.this_dept_insert_ver`)"
                    clearable
                    :style="{ width: '100%' }"
                    maxlength="50"
                    show-word-limit
                  ></el-input>
                  <span v-else>{{formData.versionValue}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_staffing_depts`)" prop="deptName">
                  <span>{{ formData.deptName }}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_staff`)" prop="userName">
                  <el-input v-if="editStatus&&!batchStatus" style="width: 80%; margin-right: 10px" readonly v-model="formData.nickName" :placeholder="$t(`doc.this_dept_select`)">
                    <el-button slot="append" icon="el-icon-search" @click="handleSelect"></el-button>
                  </el-input>
                  <span v-else>{{ formData.nickName }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_preparation_time`)+`:`" prop="applyTime">
                  <span>{{ parseTime(formData.applyTime) }}</span>
                </el-form-item>
              </el-col>
            </el-row>

<!--            <el-row gutter="15" v-if="formData.classType===classTypeForeign">-->
<!--              <el-col :span="24">-->
<!--                <el-form-item  :label="$t(`doc.this_dept_file_effective_date`)+`:`" prop="fileEffectiveDate">-->
<!--                  <el-date-picker-->
<!--                    v-if="editStatus"-->
<!--                    v-model="formData.fileEffectiveDate"-->
<!--                    type="datetime"-->
<!--                    value-format="yyyy-MM-dd HH:mm:ss"-->
<!--                    :placeholder="$t(`doc.this_dept_select_date`)"-->
<!--                    align="right">-->
<!--                  </el-date-picker>-->
<!--                  <span v-else>{{ parseTime(formData.fileEffectiveDate) }}</span>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--              <el-col :span="24">-->
<!--                <el-form-item :label="$t(`doc.this_dept_revise_dates`)" prop="revisionDate">-->
<!--                  <el-date-picker-->
<!--                    v-if="editStatus"-->
<!--                    v-model="formData.revisionDate"-->
<!--                    type="datetime"-->
<!--                    value-format="yyyy-MM-dd HH:mm:ss"-->
<!--                    :placeholder="$t(`doc.this_dept_select_date`)"-->
<!--                    align="right">-->
<!--                  </el-date-picker>-->
<!--                  <span v-else>{{ parseTime(formData.revisionDate) }}</span>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--            </el-row>-->
            <template v-if="isSystemClauseShow">
              <el-row gutter="15">
                <el-col :span="12">
                  <el-form-item :label="$t(`doc.system_clause`)" prop="systemClause">
                    <el-select
                      v-if="editStatus"
                      filterable
                      :placeholder="$t(`doc.system_clause_select`)"
                      v-model="formData.systemClause"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="dict in dict.type.system_clause_list"
                        :key="dict.value"
                        :label="dictLanguage(dict)"
                        :value="dict.value"
                      >
                      </el-option>
                    </el-select>
                    <dict-tag v-else :options="dict.type.system_clause_list" :value="formData.systemClause"/>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_file_temp`)">
                  <div class="link-box bzlink-box">
                    <span
                      v-if="mobanwenjian != ''"
                      style="color: #385bb4; cursor: pointer"
                      @click="handlePreview(mobanwenjian[0].id)"
                      >{{ mobanwenjian[0].fileName }}</span
                    >
                    <span
                      v-if="mobanwenjian != ''"
                      style="color: #385bb4; cursor: pointer; margin-left: 10px"
                      @click="
                        handelefileLocalDownload(
                          mobanwenjian[0].id,
                          mobanwenjian[0].fileName
                        )
                      "
                      >{{ $t(`doc.this_dept_download`) }}</span
                    >
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_change_source`)+`:`" prop="invokeId">
                  <span style="color: #385bb4; cursor: pointer" @click="handleDeal(formData)">{{formData.invokeId}}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15"  v-if="programVersionId">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_by_superior`) + `:`" prop="programDocId">
                  <el-input
                    v-if="editStatus"
                    v-model="formData.programDocName"
                    :placeholder="$t(`doc.this_dept_pls_select`)"
                    readonly
                  >
                    <el-button
                      slot="append"
                      type="primary"
                      icon="el-icon-more"
                      @click="versionSearchInit('program')"
                    ></el-button>
                  </el-input>
                  <span v-else>{{formData.programDocName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_superior_file_name`)" prop="programDocId">
                  <span>{{formData.programDocId}}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <!-- 法规/标准相关字段 -->
            <el-row gutter="15" v-if="regulationStatusShow || regulationPublishDateShow">
              <el-col :span="12" v-if="regulationStatusShow">
                <el-form-item :label="$t('dict.regulation_standard_status') + ':'" prop="regulationStandardStatus">
                  <el-select
                    v-if="editStatus"
                    :class="editStatus?'':'fujian'"
                    :disabled="!editStatus"
                    v-model="formData.regulationStandardStatus"
                    :placeholder="$t('doc.places_select') + $t('dict.regulation_standard_status')"
                    :style="{ width: '100%' }"
                    clearable
                  >
                    <el-option
                      v-for="item in dict.type.regulation_standard_status"
                      :key="item.value"
                      :label="dictLanguage(item)"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <div v-else class="project-code-tags">
                    <dict-tag :options="dict.type.regulation_standard_status" :value="formData.regulationStandardStatus"/>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="regulationPublishDateShow">
                <el-form-item :label="$t('field.regulation_publish_date') + ':'" prop="regulationPublishDate">
                  <el-date-picker
                    v-if="editStatus"
                    :class="editStatus?'':'fujian'"
                    :disabled="!editStatus"
                    v-model="formData.regulationPublishDate"
                    type="date"
                    :placeholder="$t('doc.places_select') + $t('field.regulation_publish_date')"
                    :style="{ width: '100%' }"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                  ></el-date-picker>
                  <span v-else>{{formData.regulationPublishDate}}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15" v-if="regulationImplementDateShow">
              <el-col :span="12">
                <el-form-item :label="$t('field.regulation_implement_date') + ':'" prop="regulationImplementDate">
                  <el-date-picker
                    v-if="editStatus"
                    :class="editStatus?'':'fujian'"
                    :disabled="!editStatus"
                    v-model="formData.regulationImplementDate"
                    type="date"
                    :placeholder="$t('doc.places_select') + $t('field.regulation_implement_date')"
                    :style="{ width: '100%' }"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                  ></el-date-picker>
                  <span v-else>{{formData.regulationImplementDate}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <!-- 空列，保持布局对齐 -->
              </el-col>
            </el-row>
            <el-row gutter="15" v-if="!(pButton == 'DISUSE')">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_current_change_ver`)+`:`" prop="standardDocfileList">
                  <!-- <div class="fileuploadbzremark"> -->
                  <fileUpload
                    :showDownload="nodeShow('download_source_file')&&workflowStatus&&!batchStatus"
                    :editStatus="editStatus"
                    v-model.trim="standardDocfileList"
                    limit="1"
                    @input="fileUpdate('standardDocfileList')"
                    :fileType="['docx','doc','xls','xlsx','pdf','ppt','pptx']"
                    :isShowTip="false"
                    :isMode="nodeShow('page_oper_file_edit')"
                  />
                  <!-- </div> -->
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_change_ver_annex`)+`:`" prop="appendixesfileList">
                  <!-- <div class="fileuploadbzremark"> -->
                  <fileUpload
                    :editStatus="editStatus"
                    v-model.trim="appendixesfileList"
                    :fileType="[]"
                    :isfileType="false"
                    :limit="100"
                    :disabled="pButton == 'DISUSE'"
                    @input="fileUpdate('appendixesfileList')"
                    :isShowTip="false"
                  />
                  <!-- </div> -->
                </el-form-item>
              </el-col>
            </el-row>

            <template v-if="projectCodeShow">
              <el-row gutter="15">
                <el-col :span="12">
                  <el-form-item :label="$t(`doc.this_dept_project_code`)" prop="projectCode">
                    <el-select
                      v-if="editStatus"
                      filterable
                      multiple
                      :placeholder="$t(`doc.this_dept_project_code`)"
                      v-model.trim="formData.projectCode"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="dict in dict.type.project_code_list"
                        :key="dict.value"
                        :label="dictLanguage(dict)"
                        :value="dict.value"
                      >
                      </el-option>
                    </el-select>
                    <div v-else class="project-code-tags">
                      <dict-tag :options="dict.type.project_code_list" :value="formData.projectCode"/>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>

            <template v-if="projectNameSecurityKeywordByteShow">
              <template v-if="nodeShow('doc_recheck_act')">
                <el-row gutter="15">
                  <el-col :span="12">
                    <el-form-item :label="$t(`doc.this_dept_project_name`)" prop="projectName">
                      <el-input
                        v-model="formData.projectName"
                        :placeholder="$t(`doc.this_dept_insert`)+$t(`doc.this_dept_project_name`)"
                        clearable
                        :style="{ width: '100%' }"
                        maxlength="50"
                        show-word-limit
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t(`doc.this_dept_security_class`)" prop="securityClass">
                      <el-select
                        :placeholder="$t(`doc.this_dept_pls_select`)+$t(`doc.this_dept_security_class`)"
                        v-model.trim="formData.securityClass"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="dict in dict.type.security_class_list"
                          :key="dict.value"
                          :label="dictLanguage(dict)"
                          :value="dict.value"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row gutter="15">
                  <el-col :span="12">
                    <el-form-item :label="$t(`doc.this_dept_keyword`)" prop="keyword">
                      <el-input
                        v-model="formData.keyword"
                        :placeholder="$t(`doc.this_dept_insert`)+$t(`doc.this_dept_keyword`)"
                        clearable
                        :style="{ width: '100%' }"
                        maxlength="50"
                        show-word-limit
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t(`doc.this_dept_doc_bytes`)" prop="docBytes">
                      <el-input
                        v-model="formData.docBytes"
                        type="number"
                        :placeholder="$t(`doc.this_dept_insert`)+$t(`doc.this_dept_doc_bytes`)"
                        clearable
                        :style="{ width: '100%' }"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </template>
              <template v-else>
                <el-row gutter="15" v-if="formData.projectName && formData.securityClass">
                  <el-col :span="12">
                    <el-form-item :label="$t(`doc.this_dept_project_name`)">
                      <span>{{formData.projectName}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t(`doc.this_dept_security_class`)">
                      <dict-tag :options="dict.type.security_class_list" :value="formData.securityClass"/>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row gutter="15" v-if="formData.keyword && formData.docBytes">
                  <el-col :span="12">
                    <el-form-item :label="$t(`doc.this_dept_keyword`)">
                      <span>{{formData.keyword}}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t(`doc.this_dept_doc_bytes`)">
                      <span>{{formData.docBytes}}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </template>
            </template>

            <template v-if="isCustomerShow">
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_client_codes`)" prop="customerCode">
                    <el-input
                      v-if="editStatus"
                      v-model="formData.customerCode"
                      :placeholder="$t(`doc.this_dept_client_code_select`)"
                      clearable
                      :style="{ width: '100%' }"
                      maxlength="50"
                      show-word-limit
                    ></el-input>
                    <span v-else>{{formData.customerCode}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>

            <template v-if="isShowPart">
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="' '" prop="productTableData">
                    <template v-if="editStatus">
                      <el-form
                        ref="productTableForm"
                        :model="{ productTableData: productTableData }"
                        :rules="productTableRules"
                        label-width="0px"
                      >
                        <el-table
                          :data="productTableData"
                          border
                          style="width: 100%"
                          :show-header="true"
                        >
                          <el-table-column
                            :label="$t(`doc.this_dept_product_codes`)"
                            min-width="200"
                          >
                            <template slot-scope="scope">
                              <el-form-item
                                :prop="'productTableData.' + scope.$index + '.materialCode'"
                                :rules="materialCodeRules"
                                style="margin-bottom: 0;"
                              >
                                <el-input
                                  v-model="scope.row.materialCode"
                                  :placeholder="$t(`doc.this_dept_insert`)"
                                  maxlength="20"
                                  clearable
                                  show-word-limit
                                ></el-input>
                              </el-form-item>
                            </template>
                          </el-table-column>

                          <el-table-column
                            v-if="isShowProductVersion"
                            :label="$t(`doc.this_dept_product_version`)"
                            min-width="200"
                          >
                            <template slot-scope="scope">
                              <el-form-item
                                :prop="'productTableData.' + scope.$index + '.productVersion'"
                                :rules="productVersionRules"
                                style="margin-bottom: 0;"
                              >
                                <el-input
                                  v-model="scope.row.productVersion"
                                  :placeholder="$t(`doc.this_dept_insert`)"
                                  maxlength="50"
                                  clearable
                                  show-word-limit
                                ></el-input>
                              </el-form-item>
                            </template>
                          </el-table-column>

                          <el-table-column
                            :label="$t(`doc.this_dept_product_summarys`)"
                            min-width="200"
                          >
                            <template slot-scope="scope">
                              <el-form-item
                                :prop="'productTableData.' + scope.$index + '.materialDescription'"
                                :rules="materialDescriptionRules"
                                style="margin-bottom: 0;"
                              >
                                <el-input
                                  v-model="scope.row.materialDescription"
                                  :placeholder="$t(`doc.this_dept_insert`)"
                                  maxlength="200"
                                  clearable
                                  show-word-limit
                                ></el-input>
                              </el-form-item>
                            </template>
                          </el-table-column>


                          <el-table-column
                            :label="$t(`doc.this_dept_change_type`)"
                            width="100"
                          >
                            <template slot-scope="scope">
                              <el-button
                                v-if="scope.$index === 0"
                                type="text"
                                @click="addProductTableRow()"
                                style="color: #409eff;"
                              >
                                <i class="el-icon-plus"></i>
                              </el-button>
                              <el-button
                                v-if="scope.$index > 0"
                                type="text"
                                @click="removeProductTableRow(scope.$index)"
                                style="color: #ff4949;"
                              >
                                <i class="el-icon-minus"></i>
                              </el-button>
                            </template>
                          </el-table-column>
                        </el-table>
                      </el-form>
                    </template>

                    <template v-else>
                      <el-table
                        :data="productTableData"
                        border
                        style="width: 100%"
                        :show-header="true"
                      >
                        <el-table-column
                          :label="$t(`doc.this_dept_product_codes`)"
                          prop="materialCode"
                        >
                          <template slot-scope="scope">
                            <span>{{ scope.row.materialCode || '-' }}</span>
                          </template>
                        </el-table-column>

                        <el-table-column
                          v-if="isShowProductVersion"
                          :label="$t(`doc.this_dept_product_version`)"
                          prop="productVersion"
                        >
                          <template slot-scope="scope">
                            <span>{{ scope.row.productVersion || '-' }}</span>
                          </template>
                        </el-table-column>

                        <el-table-column
                          :label="$t(`doc.this_dept_product_summarys`)"
                          prop="materialDescription"
                        >
                          <template slot-scope="scope">
                            <span>{{ scope.row.materialDescription || '-' }}</span>
                          </template>
                        </el-table-column>
                      </el-table>
                    </template>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_factorys`)" prop="factorys">
                    <el-select
                      v-if="editStatus"
                      :placeholder="$t(`doc.this_dept_factory_select`)"
                      v-model.trim="formData.factorys"
                    >
                      <el-option
                        v-for="dict in dict.type.tenant_list"
                        :key="dict.value"
                        :label="dictLanguage(dict)"
                        :value="dict.value"
                      >
                      </el-option>
                    </el-select>
                    <dict-tag v-else :options="dict.type.tenant_list" :value="formData.factorys"/>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>

            <template v-if="isDeviceShow">
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_unit_codes`)" prop="deviceCode">
                    <el-input
                      v-if="editStatus"
                      v-model="formData.deviceCode"
                      :placeholder="$t(`doc.this_dept_unit_code_select`)"
                      clearable
                      :style="{ width: '100%' }"
                      maxlength="50"
                      show-word-limit
                    ></el-input>
                    <span v-else>{{formData.deviceCode}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_unit_name`)+`:`" prop="deviceName">
                    <el-input
                      v-if="editStatus"
                      v-model="formData.deviceName"
                      :placeholder="$t(`doc.this_dept_unit_name_select`)"
                      clearable
                      :style="{ width: '100%' }"
                      maxlength="100"
                      show-word-limit
                    ></el-input>
                    <span v-else>{{formData.deviceName}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>



            <el-row gutter="15" v-if="isShelfLifeShow">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_shelf_life`)+`:`" prop="shelfLife">
                  <el-date-picker
                    :disabled="!editStatus"
                    v-model="formData.shelfLife"
                    type="date"
                    value-format="yyyy-MM-dd"
                    :placeholder="$t(`doc.this_dept_select_year`)">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15" v-if="formData.classType===classTypeForeign&&complianceShow">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_compliancy`) + `:`" prop="compliance">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    resize="none"
                    v-model="formData.compliance"
                    type="textarea"
                    :placeholder="$t(`doc.this_dept_insert`)+$t($t(`doc.this_dept_compliancy`))"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                    maxlength="1000"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15" v-if="filePurposeShow">
              <el-col :span="12">
                <el-form-item :label="$t('dict.file_purpose_type') + ':'" prop="filePurpose">
                  <el-select
                    v-if="editStatus"
                    :class="editStatus?'':'fujian'"
                    :disabled="!editStatus"
                    v-model="formData.filePurpose"
                    :placeholder="$t('doc.places_select') + $t('dict.file_purpose_type')"
                    :style="{ width: '100%' }"
                    clearable
                  >
                    <el-option
                      v-for="item in dict.type.file_purpose_type"
                      :key="item.value"
                      :label="dictLanguage(item)"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <div v-else class="project-code-tags">
                    <dict-tag :options="dict.type.file_purpose_type" :value="formData.filePurpose"/>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_change_reason`)+`:`" prop="changeReason">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    resize="none"
                    v-model="formData.changeReason"
                    type="textarea"
                    :placeholder="$t(`doc.this_dept_insert_change_reason`)"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                    maxlength="1000"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_changes`)+`:`" prop="content">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    resize="none"
                    v-model="formData.content"
                    type="textarea"
                    :placeholder="$t(`doc.this_dept_insert_change_content`)"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                    maxlength="1000"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`sys_mgr.user_remark`)+`:`" prop="remark">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    resize="none"
                    v-model="formData.remark"
                    type="textarea"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15" v-if="(trains&&trains.length>0)||(workflowStatus&&nodeShow('page_oper_add_train_record')&&formData.yNTrain!==no)">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_train_record`)+`:`" prop="remark">
                  <fileUpload
                    v-model.trim="trains"
                    :editStatus="workflowStatus&&nodeShow('page_oper_add_train_record')"
                    limit="100"
                    :fileType="['docx', 'doc','xls','xlsx','pdf','ppt','pptx','bmp','jpg','png','svg','tif','gif','ppts']"
                    :isShowTip="false"
                    @input="(list)=>handelConfirm(list,'train')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15" v-if="nodeShow('whether_customer_record')||formData.whetherCustomer">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.whether_customer_records`)+`:`" prop="whetherCustomer">
                  <el-radio-group  v-if="workflowStatus&&nodeShow('whether_customer_record')&&!batchStatus" v-model.trim="formData.whetherCustomer">
                    <el-radio
                      v-for="(item, index) in dict.type.sys_yes_no"
                      :key="index"
                      :label="item.value"
                    >{{ dictLanguage(item) }}</el-radio>
                  </el-radio-group>
                  <dict-tag v-else :options="dict.type.sys_yes_no" :value="formData.whetherCustomer"/>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label=" ">
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15" v-if="(customerRecordList&&customerRecordList.length>0)||(workflowStatus&&nodeShow('add_customer_record')&&formData.whetherCustomer!==no)">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.customer_record`)+`:`" prop="customerRecord">
                  <fileUpload
                    v-model.trim="customerRecordList"
                    :editStatus="workflowStatus&&nodeShow('add_customer_record')"
                    limit="100"
                    :fileType="['docx', 'doc','xls','xlsx','pdf','ppt','pptx','bmp','jpg','png','svg','tif','gif','ppts','zip','rar','7z']"
                    :isShowTip="false"
                    @input="(list)=>handelConfirm(list,'customer')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>

      <div class="news-card" v-if="!!formData.yNTrain">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_train`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elFormTrain"
            :model="formData"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_train_or_not`)+`:`" prop="yNTrain">
                  <el-radio-group  v-if="editStatus&&trainStatus&&!batchStatus" v-model.trim="formData.yNTrain">
                    <el-radio
                      v-for="(item, index) in dict.type.sys_yes_no"
                      :key="index"
                      :label="item.value"
                    >{{ dictLanguage(item) }}</el-radio>
                  </el-radio-group>
                  <dict-tag v-else :options="dict.type.sys_yes_no" :value="formData.yNTrain"/>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>

      <distribute-box
        v-if="!batchStatus"
        :editStatus="editStatus"
        :workflowStatus="workflowStatus"
        :setDeptReceiver="nodeShow('set_dept_receiver')"
        :deptList="deptList"
        :deptOptions="deptOptions"
        :companyList="companyList"
        :distributeList="distributeList"
        ref="distributeBox"
      ></distribute-box>

      <div class="el-card news-card is-always-shadow"  v-if="formData.classType===classTypeDoc">
        <div class="el-card__body">
          <el-tabs
            v-model.trim="activeIndex"
            class="news-tabs"
          >
            <el-tab-pane name="1" v-if="classTypeRecordMN">
              <span slot="label">{{ $t(`doc.this_dept_related_record`) }}</span>
              <link-note
                :editStatus="editStatus"
                v-show="activeIndex  == '1'"
                :dataList="formData.noteLinks"
                ref="linkNote"
                :dataType="formData.dataType"
              ></link-note>
            </el-tab-pane>
            <el-tab-pane name="1" v-else>
              <span slot="label">{{ $t(`doc.this_dept_related_record`) }}</span>
              <link-record
                ref="linkRecord"
                :dataList="formData.recordLinks"
                :editStatus="editStatus"
                v-show="activeIndex  == '1'"
                :dataType="formData.dataType"
              ></link-record>
            </el-tab-pane>
            <el-tab-pane name="2">
              <span slot="label">{{ $t(`doc.this_dept_related_file`) }}</span>
              <link-file
                :editStatus="editStatus"
                v-show="activeIndex  == '2'"
                :dataList="formData.docLinks"
                ref="linkFile"
                :dataType="formData.dataType"
              ></link-file>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="news-card" v-if="(nodeShow('shenhe')||nodeShow('pizhun'))&&workflowStatus&&!approvalStatus&&!batchStatus">
        <div class="card-head">
          <div class="cell-title">{{submitLabel}}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="validateForm"
            :model="formSubmit"
            :rules="rules"
            size="medium"
            label-position="right"
            label-width="200px"
          >
            <el-row gutter="15" >
              <el-col :span="24">
                <el-form-item :label="submitLabel+$t(`doc.this_dept_conclusion`)" prop="pass">
                  <el-radio-group v-model.trim="formSubmit.pass"  @input="commentItemSelect">
                    <el-radio
                      v-for="dict in passoptions"
                      :key="dict.value"
                      :label="dict.value"
                    >{{ dict.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="submitLabel+$t(`doc.this_dept_comments`)">
                  <el-input
                    v-model="formSubmit.summary"
                    type="textarea"
                    :placeholder="$t(`doc.this_dept_insert`)+submitLabel+$t(`doc.this_dept_comment`)"
                    maxlength="200"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
      <approval-box
        v-if="!nodeShow('top_btn_publish_file')&&workflowStatus&&approvalStatus&&!batchStatus"
        ref="approvalBox" :submitLabel="submitLabel"
        :selected="nodeShow('default_selected')"
        :userListStatus="nodeShow('user_list')"
        :order = "order"
        :searchQuery="searchQuery"
        :hideNodeCode = "hideNodeCode"
        :defaultStaff="defaultStaff"
        :pListData="pListData"
        :status="(nodeShow('shenhe')||nodeShow('pizhun'))"
      ></approval-box>
    </div>
    <div v-show="activeName==='log'">
      <workflow-logs :procInstId = "procInstId"></workflow-logs>
    </div>

    <div v-show="activeName==='relevant_user'" >
      <pre-select-users-table
        v-if="formData.id"
        :apply-id="formData.id"
      />
    </div>

    <!-- 流程选择下一环节及人员  开始 -->
    <el-dialog
      :title="$t(`doc.this_dept_select_next`)"
      v-if="dialogVisible" :visible.sync="dialogVisible"
      width="60%"
      append-to-body
      v-loading = flowStepLoading
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <processcode
        ref="prochild"
        :selected="nodeShow('default_selected')"
        :userListStatus="nodeShow('user_list')"
        :order = "order"
        :searchQuery="searchQuery"
        :hideNodeCode = "hideNodeCode"
        :defaultStaff="defaultStaff"
        :pListData="pListData"
        :isSummary="isSummary"
      ></processcode>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
        <el-button type="primary" v-dbClick @click="handleWorkflowSubmit"
        >{{ $t(`doc.this_dept_annex`) }}</el-button
        >
      </span>
    </el-dialog>
    <!-- 流程选择下一环节及人员  结束 -->
    <monitor-drawer
      v-if="monitorDrawerVisible"
      ref="monitorDrawer"
    ></monitor-drawer>
   <doc-id-box ref="docIdBox" v-if="shenchenbianhao" @setDocId="setDocId" @setRecordDocId="setRecordDocId"></doc-id-box>
    <!-- 文件预览组件 -->
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="close"></as-pre-view>
    <el-drawer
      :wrapperClosable='false'
      :visible.sync="dealDrawerShow"
      :append-to-body="true"
      direction="rtl"
      size="90%"
      :with-header="false"
      :show-close="false"
      modal-append-to-body
      :destroy-on-close="true"
    >
      <div style="width:100%; height:100%;overflow: hidden">
        <workflow-router ref="dealDrawer" @closeDrawer="handleCloseChange"></workflow-router>
      </div>
    </el-drawer>
    <prepare-doc-id ref="prepareDocId" @selectHandle="handleSubmitDocId" ></prepare-doc-id>
    <version-list :dataType="formData.dataType" :classTypeList="[classTypeDoc]" ref="versionList" @selectHandle="versionSelectHandle"></version-list>
    <preset-user ref="presetUser" @selectHandle="selectHandlePresetUser"></preset-user>
    <user-list ref="userList" @selectHandle="handleSubmitUser"></user-list>

    <el-dialog :title="$t(`doc.this_dept_tip`)"
               v-if="pushDialogVisible" :visible.sync="pushDialogVisible"
               width="35%"
               v-loading="loading"
               :close-on-click-modal="false"
               :close-on-press-escape="false"
               append-to-body>
      <div>
        <span>{{ $t(`doc.this_dept_set_effective_date`) }}：
           <el-date-picker
             v-model="setupTime"
             value-format="yyyy-MM-dd HH:mm:ss"
             type="date"
             :placeholder="$t(`doc.this_dept_select_effective_date`)">
          </el-date-picker>
        </span>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="pushDialogVisible = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
        <el-button type="primary" @click="pushCommit">{{ $t(`doc.this_dept_confirm`) }}</el-button>
      </div>
    </el-dialog>
    <transfer-flow ref="transferFlow" @close="close"></transfer-flow>
  </div>
</template>
<script>
import historicalVersion from "./add_import/historicalVersion";
import {settingDocClassId, settingDocClassList} from "@/api/file_settings/type_settings";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import processcode from "@/views/workflowList/processcode/index.vue";
import WorkflowLogs from "@views/workflowList/workflowLogs/index.vue";
import {
  addModifyApply,
  getInfoByBpmnId,
  queryModifyApplyTrain,
  updateModifyApply
} from '@/api/file_processing/modifiyApply'
import {coverEffective, signEffective, downloadResume as downloadResumeApi} from "@/api/file_processing/fileSignature";
import {getByUpDocClassAndBizType} from "@/api/setting/docClassFlow";
import {getInfo} from "@/api/setting/docClassFlowNodeDetail";
import {isExistByName, modifyApplyLinklist} from "@/api/document_account/standard";
import {
  backFlowToOne,
  getExtAttributeModel,
  getRecordbyPorcInstId,
  getRedirectDefId,
  getStartActdef,
  procInstInfoAndStatus,
  workflowbacktostart,
  workflowprocesskey
} from '@/api/my_business/workflow'
import mixin from "@/layout/mixin/Commmon.js";
// PDF本地文件预览
import {queryUserProjectList} from '@/api/system/project'
import LinkFile from '@views/workflowList/addWorkflow/add_import/linkFile.vue'
import LinkRecord from '@views/workflowList/addWorkflow/add_import/linkRecord.vue'
import DealDrawer from '@/components/DealDrawer/index.vue'
import WorkflowRouter from '@views/workflowList/workflowRouter.vue'
import DocIdBox from '@views/workflowList/addWorkflow/add_import/docIdBox.vue'
import {checkPermi} from '@/utils/permission'
import PresetUser from '@views/workflowList/addWorkflow/add_import/presetUser.vue'
import VersionList from '@views/workflowList/addWorkflow/add_import/versionList.vue'
import PrepareDocId from '@views/workflowList/addWorkflow/add_import/prepareDocId.vue'
import {listPresetUser} from '@/api/setting/presetUser'
import {selectStatusByDocId} from '@/api/my_business/workflowApplyLog'
import {checkNoIsExist} from '@/api/system/standard'
import {listWorkflowLog} from '@/api/my_business/workflowLog'
import {getNextVersion} from '@/api/setting/versionRuleDetail'
import {listDept} from '@/api/system/dept'
import {getCompanyList} from '@/api/system/user'
import {listModifyApplyDistribute} from '@/api/my_business/modifyApplyDistribute'
import DistributeBox from '@views/workflowList/addWorkflow/add_import/distributeBox.vue'
import UserList from '@views/workflowList/addWorkflow/add_import/userList.vue'
import {updateModifyApplyTrainList} from '@/api/my_business/modifyApplyTrain'
import {getInfoBy} from '@/api/setting/docClassSetting'
import {parseTime} from '@/utils/ruoyi'
import {validateByUserName} from '@/api/system/userSignature'
import LinkNote from '@views/workflowList/addWorkflow/add_import/linkNote.vue'
import {getLeaderByUserName} from '../../../api/system/user'
import {validatePrepareId} from '../../../api/setting/prepareId'
import TransferFlow from '@views/workflowList/addWorkflow/add_import/transferFlow.vue'
import {getCodeRuleDetailByDocClass} from '../../../api/setting/codeRule'
import ApprovalBox from './add_import/approvalBox.vue'
import {listDistributeGroupDetail} from '../../../api/setting/distributeGroupDetail'
import {backPreviousStep} from '../../../api/my_business/workflow'
import PreSelectUsersTable from './add_import/preSelectUsersTable.vue'

export default {
  dicts: ["business_status","sys_yes_no","tenant_list","system_clause_list","security_class_list","project_code_list",'file_purpose_type','regulation_standard_status'],
  components: {
    ApprovalBox,
    TransferFlow,
    LinkNote,
    UserList,
    DistributeBox,
    PrepareDocId,
    VersionList,
    PresetUser,
    DocIdBox,
    WorkflowRouter,
    DealDrawer,
    LinkRecord,
    LinkFile,
    historicalVersion,
    Treeselect,
    processcode,
    WorkflowLogs,
    PreSelectUsersTable
  },
  name: "Add_doc",
  props: ["dataType",'data'],
  mixins: [mixin],
  data() {
    let validateFileUrl = (rule, value, callback) => {
      if (this.standardDocfileList.length < 1) {
        //我控制了FileList 长度代表文件个数
        callback(new Error(this.$t(`doc.this_dept_to_upload_file`)));
      } else {
        callback();
      }
    };
    let validateDocName = (rule, val, callback) => {
      if (this.editStatus) {
        this.formData.docName = this.formData.docName.trim()
        if (this.formData.docName) {
          isExistByName({docName:this.formData.docName,applyId:this.formData.id}).then((response) => {
            if (response.data == true) {
              callback(new Error(this.$t(`doc.this_dept_file_name_exist`)));
            } else {
              callback();
            }
          });
        } else {
          callback(new Error(this.$t(`doc.this_dept_file_name_not_null`)));
        }
      } else {
        callback();
      }
    };
    return {
      mark:undefined,
      type: 'add_doc',
      classTypeList:undefined,
      dataListIndex: undefined,
      approvalStatus: true,
      order: 0,
      watchStatus: false,
      leader: undefined,
      customerRecordList: [],
      trains:[],
      hideNodeCode: [],
      yes: 'Y',
      no: 'N',
      distributeList: [],
      deptList: [],
      companyList: [],
      deptOptions: [],
      defaultStaff:undefined,
      classTypeRecordMN: true,
      backFlowToOneStatus: true,
      classTypeRecord: 'RECORD',
      classTypeDoc: 'DOC',
      classTypeForeign: 'FOREIGN',
      classTypeNote: 'NOTE',
      searchQuery: {},
      shlkPath:process.env.VUE_APP_SHLK_PATH,
      dealDrawerShow: false,
      submitLabel:this.$t('doc.this_dept_annex'),
      isProject: false,
      shenchenbianhao: false,
      passoptions: [
        { value: true, label: this.$t(`doc.this_dept_pass`) },
        { value: false, label: this.$t(`doc.this_dept_not_pass`) },
      ],
      formSubmit: { summary: "", actionType: "", pass: undefined },
      pButton: 'ADD',
      isSummary: false,
      title: this.$t(`doc.this_dept_add_file`),
      projectList:[],
      project:{id:'',name:''},
      activeName: "info",
      nodeDetail: {},
      nodeDetailList: [],
      procDefKey: undefined,
      processData: {},
      viewId: "",
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      viewShow: false,
      active: 4,
      activeIndex: "1",
      uploadType: ["doc", "docx", "ppt", "xlsx", "pdf", "jpg", "png"],
      monitorDrawerVisible:false,
      redirectDefId: undefined,
      redirectReceivers: undefined,
      redirectOrder: undefined,
      quantity:[],
      settingDetail: {},
      formData: {
        whetherCustomer: undefined,
        distributeList: [],
        classType: undefined,
        projectId: undefined,
        projectName: undefined,
        docClass: undefined,
        changeType: undefined,
        docName: "",
        versionValue: "",
        docId: undefined,
        deptId: undefined,
        deptName: undefined,
        userName: undefined,
        nickName: undefined,
        currentVersion: undefined,
        changeReason: this.$t(`doc.initial_reason_release`),
        content: this.$t(`doc.initial_release`),
        trainDept: undefined,
        dataType: undefined,
        applyTime: undefined,
        appendixes: undefined, //附件
        standardDoc: {fileName:''}, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        noteLinks: undefined,
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined,
        step: undefined,
        presetUserList: [],
        batch: undefined,
        yNDistribute: '',
        distributeType: '',
        yNTrain: '',
        trainType: "",
        partNumber: undefined,
        custodyDeptId: undefined,
        shelfLife: undefined,
        fileEffectiveDate: undefined,
        revisionDate: undefined,
        compliance: undefined,
        filePurpose: undefined,
        regulationStandardStatus: undefined,
        regulationPublishDate: undefined,
        regulationImplementDate: undefined,
        setupTime: undefined,
        partRemark: null,
        factorys: null,
        customerCode: null,
        deviceCode: null,
        deviceName: null,
        productVersion: null,
        projectCode: undefined,
        internalDocId: undefined,
        ecnCode: undefined,
        systemClause: undefined,
        batchId: undefined,
        securityClass: undefined,
        keyword: undefined,
        docBytes: undefined
      },
      partNumberArr: [""],
      // 产品表格数据
      productTableData: [
        {
          materialCode: '',
          productVersion: '',
          materialDescription: '',
        }
      ],

      // 各字段的校验规则
      materialCodeRules: [
        {required: true, message: this.$t(`doc.this_dept_product_code_no_null`), trigger: ['blur', 'change']}
      ],
      productVersionRules: [
        {required: true, message: this.$t(`doc.this_dept_product_version_no_null`), trigger: ['blur', 'change']}
      ],
      materialDescriptionRules: [
        {required: true, message: this.$t(`doc.this_dept_product_summary_no_null`), trigger: ['blur', 'change']}
      ],
      rules: {
        whetherCustomer: [
          { required: true, message: this.$t(`doc.this_dept_pls_select`)+this.$t(`doc.whether_customer_records`)+'!', trigger: "blur,change" },
        ],
        fileEffectiveDate: [
          { required: true, message: this.$t(`doc.this_dept_select_effective_date`), trigger: "blur,change" },
        ],
        yNTrain: [
          { required: true, message: this.$t(`doc.this_dept_select_is_train`), trigger: "blur,change" },
        ],
        yNDistribute: [
          { required: true, message: this.$t(`doc.this_dept_select_is_distribute`), trigger: "blur,change" },
        ],
        projectId: [
          { required: true, message: this.$t(`doc.this_dept_select_project`), trigger: "blur,change" },
        ],
        pass:[
          { required: true, message: this.$t(`doc.this_dept_pls_select`), trigger: "blur,change" },
        ],
        docClass: [
          {
            required: true,
            message: this.$t(`doc.this_dept_preparer`),
            trigger: "change",
          }
        ],
        upVersionId: [
          {
            required: true,
            message: this.$t(`doc.this_dept_select_up_file`),
            trigger: "blur,change",
          }
        ],
        docName: [
          {
            max: 1000,
            message: this.$t(`doc.this_dept_file_name_more_long`),
          },
          { validator: validateDocName, trigger: "blur" },
        ],
        versionValue: [
          { required: true, message: this.$t(`doc.this_dept_insert_ver`), trigger: "blur,change" },
        ],
        custodyDeptId :[
          { required: true, message: this.$t(`file_handle.change_select_dept`), trigger: "blur,change" },
        ],
        shelfLife :[
          { required: true, message: this.$t(`file_handle.change_select_term`), trigger: "blur,change" },
        ],
        compliance: [
          { required: true, message: this.$t(`doc.this_dept_ins_compliancy`), trigger: "blur,change" },
          {
            max: 1000,
            message: this.$t(`doc.this_dept_compliancy_more_long`),
          },
        ],
        filePurpose: [
          { required: true, message: this.$t('doc.places_select') + this.$t('dict.file_purpose_type'), trigger: "blur,change" },
        ],
        regulationStandardStatus: [
          { required: true, message: this.$t('doc.places_select') + this.$t('dict.regulation_standard_status'), trigger: "blur,change" },
        ],
        regulationPublishDate: [
          { required: true, message: this.$t('doc.places_select') + this.$t('field.regulation_publish_date'), trigger: "blur,change" },
        ],
        regulationImplementDate: [
          { required: true, message: this.$t('doc.places_select') + this.$t('field.regulation_implement_date'), trigger: "blur,change" },
        ],
        changeReason: [
          { required: true, message: this.$t(`doc.this_dept_insert_change_reason`), trigger: "blur,change" },
          {
            max: 1000,
            message: this.$t(`doc.this_dept_change_reason_more_long`),
          },
        ],
        content: [
          { required: true, message: this.$t(`doc.this_dept_insert_change_content`), trigger: "blur,change" },
          {
            max: 1000,
            message: this.$t(`doc.this_dept_change_content_more_long`),
          },
        ],
        standardDocfileList: [
          {
            required: true,
            validator: validateFileUrl,
            trigger: ["blur", "change", "input"],
          },
        ],
        partNumber: [
          {
            required: false,
            message: this.$t(`doc.this_dept_product_code_no_null`),
            trigger: "blur,change",
          }
        ],
        partRemark: [
          {
            required: false,
            message: this.$t(`doc.this_dept_product_summary_no_null`),
            trigger: "blur,change",
          }
        ],

        customerCode: [
          {
            required: false,
            message: this.$t(`doc.this_dept_client_code_no_null`),
            trigger: "blur,change",
          }
        ],
        systemClause: [
          {
            required: false,
            message: this.$t(`doc.system_clause_no_null`),
            trigger: "blur,change",
          }
        ],
        deviceCode: [
          {
            required: false,
            message: this.$t(`doc.this_dept_unit_code_no_null`),
            trigger: "blur,change",
          }
        ],
        deviceName: [
          {
            required: false,
            message: this.$t(`doc.this_dept_device_name_no_null`),
            trigger: "blur,change",
          }
        ],
        productVersion: [
          {
            required: true,
            message: this.$t(`doc.this_dept_product_version_no_null`),
            trigger: ["blur", "change"],
          }
        ],
        projectCode: [
          {
            required: true,
            message: this.$t(`doc.this_dept_pls_select`)+this.$t(`doc.this_dept_project_code`),
            trigger: "blur,change",
          }
        ],
        internalDocId: [
          {
            required: false,
            message: this.$t(`doc.this_dept_insert`)+this.$t(`doc.this_dept_internal_file_number`),
            trigger: "blur,change",
          }
        ],
        ecnCode: [
          {
            required: false,
            message: this.$t(`doc.this_dept_insert`)+this.$t(`doc.this_dept_ecn_number`),
            trigger: "blur,change",
          }
        ],
        projectName: [
          {
            required: false,
            message: this.$t(`doc.this_dept_insert`)+this.$t(`doc.this_dept_project_name`),
            trigger: "blur,change",
          }
        ],
        securityClass: [
          {
            required: false,
            message: this.$t(`doc.this_dept_insert`)+this.$t(`doc.this_dept_security_class`),
            trigger: "blur,change",
          }
        ],
        keyword: [
          {
            required: false,
            message: this.$t(`doc.this_dept_insert`)+this.$t(`doc.this_dept_keyword`),
            trigger: "blur,change",
          }
        ],
        docBytes: [
          {
            required: false,
            message: this.$t(`doc.this_dept_insert`)+this.$t(`doc.this_dept_doc_bytes`),
            trigger: "blur,change",
          }
        ],
        factorys: [
          { required: false, message: this.$t(`doc.this_dept_factorys_select`)+'!', trigger: "blur,change" },
        ],
        programDocId: [
          { required: false, message: this.$t(`doc.this_dept_pls_select`)+this.$t(`doc.this_dept_by_superior`)+'!', trigger: "blur,change" },
        ]
      },
      kuozhanshujuBool: {},
      kuozhanshuju: {},
      field117Action: "",
      action: "/dms-admin/process/file/local_upload",
      appendixesfileList: [],
      standardDocfileList: [],
      classLevelOptions: [],
      docClassList: [],
      summary: "",
      pListData: {},
      editStatus:false,
      trainStatus:true,
      transferStatus: false,
      workflowStatus: false,
      dialogVisible: false,
      processcodeData: {},
      processInstanceModel: {},
      disabled: false,
      mobanwenjian: [],
      loading: false,
      detailLoading: false,
      flowStepLoading : false,
      procInstId: undefined,
      pushDialogVisible: false,
      setupTime: undefined,
      isShowPart: false,
      isCustomerShow: false,
      isSystemClauseShow: false,
      isDeviceShow: false,
      isShowProductVersion: false,
      isShelfLifeShow: false,
      isCodeAndTypeShow: false,
      projectCodeShow: false,
      docClassData: undefined,
      internalDocIdShow: false,
      ecnCodeShow: false,
      versionIsUpdateShow: false,
      projectNameSecurityKeywordByteShow: false,
      complianceShow: false,
      filePurposeShow: false,
      regulationStatusShow: false,
      regulationPublishDateShow: false,
      regulationImplementDateShow: false,
      programVersionId: false,
      theFirstStatus: false,
      codeRuleDetail: [],
      distributeSetting:{trainType:'trainType',distributeType:'distributeType',isDistribute:'yNDistribute'}
    };
  },
  computed: {
    batchStatus(){
      return !!this.formData.batchId
    }
  },
  watch: {
    "formData.partRemark"(val,oldVal) {
      let _this = this
      if(_this.isCodeAndTypeShow && !_this.theFirstStatus){
        _this.formData.docName = val ? val + "_" + _this.docClassData.className : _this.docClassData.className
      }
    },
    "formData.docId"(val,oldVal) {
      let _this = this
        let recordLinks = _this.$refs.linkRecord;
        if (_this.editStatus&&recordLinks&&recordLinks.dataList.length>0) {
          _this.formData.recordLinks = []
          _this.$message.warning(_this.$t(`doc.this_dept_master_code_change_record_clear`));
        }
      },
    "formData.docClass"(val, oldVal) {
      let _this = this
      if (val !== oldVal && oldVal && _this.formData.docId) {
        _this.formData.docId = ""
      }
      if (val) {
        if (!_this.procInstId) {
          _this.getNextVersion()
          if (!_this.batchStatus) {
            _this.$refs.distributeBox.initDistributeGroup(val,_this.distributeSetting).then(res=>{
              _this.distributeList = res
            })
          }
          _this.getByUpDocClassAndBizType(val)
        }else {
          _this.getNodeDetailInfo()
        }
        settingDocClassId(val).then((response) => {
          _this.docClassData = response.data
          _this.formData.classType = response.data.classType
          _this.formData.dataType = response.data.dataType
          if (response.data && response.data.fileList != null) {
            this.mobanwenjian = response.data.fileList;
          } else {
            this.mobanwenjian = []
          }
        });
      }else {
        this.mobanwenjian = []
      }
    },
    "formData.classType"(val){
      if (val!==this.classTypeRecord) {
        this.formData.upVersionId = undefined
        this.formData.upDocName = undefined
        this.formData.parentDocId = undefined
      }
    },
    "formData.dataType"(val) {
      let _this = this
      _this.isProject = val==='project'
    },
    data (val) {
      if (val) {
        this.init(JSON.parse(JSON.stringify(val)))
      }
    },
  },
  async created() {
    let response = await this.getConfigKey("record.doc.type")
    this.classTypeRecordMN = response.msg === undefined ? true : response.msg === 'true';
    let response1 = await this.getConfigKey("back_flow_to_one")
    this.backFlowToOneStatus = response1.msg === undefined ? true : response1.msg === 'true';
  },
  mounted() {
    if (this.data) {
      this.init(JSON.parse(JSON.stringify(this.data)))
    }
  },
  methods: {
    handleExport2() {
      this.$refs.pdfView.init()
    },
    init(row) {
      let _this = this
      _this.theFirstStatus = true
      _this.getDeptList()
      _this.getCompanyDataList()
      _this.loading = true
      _this.classTypeList = row.classTypeList
      if (row.order) {
        _this.order = row.order
      }
      if (row.mark) {
        _this.mark = row.mark
      }
      _this.formData.dataType = _this.dataType
      // _this.getQueryUserProjectList()
      //是否编辑模式
      _this.$nextTick(async() => {
        if (row && (!!row.procInstId||!isNaN(row.dataListIndex))) {
          _this.procInstId = row.procInstId
          _this.workflowStatus = row.status == '1'
          _this.procInstInfoAndStatus(_this.procInstId)
          if (row.batchId) {
            _this.setFileList(row)
            _this.getModifyApplyTrain(row.id)
            _this.formData = row
            _this.dataListIndex = row.dataListIndex
            _this.getByDocClass(_this.formData.docClass);
            // _this.initDistributeBox(_this.formData.distributeList)
            _this.restoreProductTableData()
          }else {
            _this.getDetail(_this.procInstId)
          }
        } else {
          _this.rest(row.userInfo)
          _this.workflowStatus = true
          _this.editStatus = true
          await _this.getSettingDocClassTreeseList();
          if (row.batchId) {
            _this.formData.batchId = row.batchId
            _this.formData.applyTime = row.applyTime
            _this.formData.whetherCustomer = row.whetherCustomer
            _this.formData.yNTrain= row.yNTrain
            if (row.docClass) {
              _this.formData.docClass = row.docClass
              _this.getByDocClass(row.docClass);
            }
          }
          // _this.getWorkflowprocesskey();
          _this.loading = false
        }
      });

      setTimeout(() => {
        if(_this.formData.factorys == null || _this.formData.factorys =='' || _this.formData.factorys == undefined){
          let tenantItem = this.dict.type.tenant_list.find(v => window.location.href.includes(v.raw.remark));
          if(tenantItem && tenantItem.value){
            _this.formData.factorys = tenantItem.value
          }
        }
      }, 1000);
    },
    async getByUpDocClassAndBizType(docClass) {
      let _this = this
      let { data } = await getByUpDocClassAndBizType(docClass,_this.pButton)
      _this.procDefKey = data&&data.flowKey?data.flowKey:"";
      _this.getWorkflowprocesskey()
    },
    getDetail(procInstId) {
       let _this = this
      _this.detailLoading = true
      getInfoByBpmnId(procInstId).then(async (res) => {
        let formData = res.data;
        let res1 = await modifyApplyLinklist({applyId: formData.id, linkType: "REF_DOC"})
        formData.docLinks = res1.rows;
        let res2 = await modifyApplyLinklist({applyId: formData.id, linkType: "RECORD"})
        formData.recordLinks = res2.rows;
        let res3 = await listPresetUser({bizId: formData.id})
        formData.presetUserList = res3.data
        let res4 = await modifyApplyLinklist({applyId: formData.id, linkType: "NOTE"})
        formData.noteLinks = res4.rows;
        _this.setFileList(formData)
        _this.project={
          id: formData.projectId,
          name: formData.projectName
        }
        _this.docClass = formData.docClass
        formData.projectCode=formData.projectCode ? formData.projectCode.split(',') : []
        _this.formData = formData
        _this.getModifyApplyTrain(formData.id)
        _this.getDistributeList(formData.id)

        //查询物料是否展示
        this.getByDocClass(formData.docClass);
        this.restoreProductTableData()
      }).finally(()=>{
        _this.detailLoading = false
      });
    },
    getModifyApplyTrain(applyId){
      let _this = this
      if (applyId) {
        let trains= []
        queryModifyApplyTrain({type:'train',applyId:applyId}).then(res=>{
          if (res.data) {
            res.data.forEach(item=>{
              trains.push({
                url: item.fileIds,
                name: item.files[0].fileName
              })
            })
          }
          _this.trains = trains
        })
        let customerRecordList= []
        queryModifyApplyTrain({type:'customer',applyId:applyId}).then(res=>{
          if (res.data) {
            res.data.forEach(item=>{
              customerRecordList.push({
                url: item.fileIds,
                name: item.files[0].fileName
              })
            })
          }
          _this.customerRecordList = customerRecordList
        })
      }
    },
    setFileList(formData){
      let _this = this
      if (formData.standardDoc) {
        if(formData.standardDoc.fileId) {
          _this.standardDocfileList = [
            {
              name: formData.standardDoc.docName,
              url: formData.standardDoc.fileId
            },
          ];
        }
      }
      if (formData.appendixes != null) {
        formData.appendixes.forEach((element) => {
          _this.appendixesfileList.push({
            name: element.docName,
            url: element.fileId,
          });
        });
      }
    },
    handelConfirm(list,type) {
      let trains = []
      list.forEach(item=>{
        trains.push({
          fileIds: item.url,
          fileName: item.name,
          userName: this.userInfo.userName,
          deptId: this.userInfo.deptId,
          docId: this.formData.docId,
          applyId: this.formData.id,
          type:type
        })
      })
      let data = {
        applyId:this.formData.id,
        type: type,
        trains: trains
      }
      updateModifyApplyTrainList(data);
    },
    getDistributeList(applyId){
      let _this = this
      listModifyApplyDistribute({applyId:applyId}).then(res => {
        _this.initDistributeBox(res.data)
      })
    },
    initDistributeBox(distributeList){
      let _this = this
      _this.distributeList = distributeList
      if (_this.$refs.distributeBox) {
        _this.$refs.distributeBox.init(_this.formData,_this.distributeSetting)
      }
    },
    getNextVersion(){
      getNextVersion({version:"",docClass:this.formData.docClass}).then(res=>{
        if (res.data) {
          this.formData.versionValue = res.data
        }
      })
    },
    getQueryUserProjectList(){
      let _this = this
      queryUserProjectList().then(res=>{
        _this.projectList = res.data
      })
    },
    rest(userInfo){
      let _this = this
      _this.activeName = "info"
      _this.procInstId = undefined
      _this.partNumberArr = [""]
      // 初始化产品表格数据
      _this.productTableData = [{
        materialCode: '',
        productVersion: '',
        materialDescription: ''
      }]
      let formData = {
        whetherCustomer: undefined,
        projectId: undefined,
        projectName: undefined,
        docClass: undefined,
        dataType: undefined,
        changeType: _this.pButton,
        docName: "",
        versionValue: "",
        docId: undefined,
        // deptId: _this.userInfo.dept.deptId,
        // 部门对应的组织架构一级部门ID
        deptId: _this.userInfo.dept.deptId,
        deptName: _this.userInfo.dept.deptName,
        userName: _this.userInfo.userName,
        nickName: _this.userInfo.nickName,
        currentVersion: undefined,
        changeReason: this.$t(`doc.initial_reason_release`),
        content: this.$t(`doc.initial_release`),
        trainDept: undefined,
        applyTime: new Date().getTime(),
        appendixes: undefined, //附件
        standardDoc: {fileName:''}, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        noteLinks: undefined,
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined,
        upVersionId: undefined,
        upDocName: undefined,
        classType: "DOC",
        presetUserList: [],
        step: undefined,
        batch: undefined,
        yNDistribute: 'N',
        distributeType: "",
        distributeList: [],
        yNTrain: undefined,
        trainType: '',
        partNumber: undefined,
        partRemark: undefined,
        custodyDeptId: undefined,
        shelfLife: undefined,
        fileEffectiveDate: undefined,
        revisionDate: undefined,
        compliance: undefined,
        filePurpose: undefined,
        regulationStandardStatus: undefined,
        regulationPublishDate: undefined,
        regulationImplementDate: undefined,
        setupTime: undefined,
        projectCode: undefined,
        systemClause: null,
        batchId: undefined,
        programVersionId: undefined,
        programDocName: undefined,
        programDocId: undefined
      }
      if (userInfo){
        formData.deptId= userInfo.deptId
        formData.deptName= userInfo.deptName
        formData.userName= userInfo.userName
        formData.nickName= userInfo.nickName
      }
      _this.formData = formData
    },
    async initStatus() {
      let _this = this
      _this.editStatus = _this.nodeShow('bianji') && _this.workflowStatus
      _this.submitLabel = _this.nodeShow('pizhun') ? _this.$t(`file_handle.change_approve`) : _this.nodeShow('shenhe')?_this.$t(`file_handle.change_auditing`):_this.$t('doc.this_dept_annex')
      if (_this.nodeShow('top_btn_generate_code')) {
        _this.getCodeRuleDetailList()
      }
      if (!_this.batchStatus){
        if (_this.nodeShow('whether_train')) {
          let funCondition = _this.nodeFunCondition('whether_train')
          if (funCondition){
            if(funCondition.limitValue && !_this.formData.yNTrain) {
              _this.formData.yNTrain = funCondition.limitValue
            }
            _this.trainStatus = funCondition.validate
          }
        }
        _this.transferStatus = await _this.$refs.transferFlow.getTransferStatus(_this.pListData)
        if (_this.approvalStatus) {
          _this.jointReviewRedirect()
          await _this.setPresetUserList()
          if (_this.$refs.approvalBox) {
            _this.$refs.approvalBox.init()
          }
        }
      }
    },
    getCodeRuleDetailList(){
      let _this = this
      let query = {
        docClass: _this.formData.docClass,
        ruleTypeList: ['FORM','DICT']
      }
      getCodeRuleDetailByDocClass(query).then(res=>{
        let codeRuleDetail = []
        for (let item of res.data) {
          let keys = item.ruleValue.split('@')
          if ("classCode" !== keys[0]) {
            if (keys.length>1) {
              codeRuleDetail.push(keys[1])
            }else {
              if ("deptCode" === keys[0]) {
                codeRuleDetail.push('deptId')
              }else {
                codeRuleDetail.push(keys[0])
              }
            }
          }
        }
        _this.codeRuleDetail = codeRuleDetail
        //增加监听
        codeRuleDetail.forEach(item=>{
          _this.$watch('formData.'+item,(newVal,oldVal)=>{
            if (newVal!==oldVal&&_this.formData.docId) {
              _this.watchStatus = true
            }
          })
        })
      })
    },
    procInstInfoAndStatus(procInstId){
      let _this = this
      procInstInfoAndStatus(procInstId).then((res) => {
        if (res) {
          _this.procDefKey = res.procDefKey
          _this.pListData = res
        }else {
          _this.pListData = {procInstId:procInstId}
          this.workflowStatus = false
        }
        _this.getExtAttributeModel()
      });
    },
    nodeShow(code){
        let _this = this
        if (_this.nodeDetail) {
            return !!_this.nodeDetail[code]
        }else  {
            return  false
        }
    },
    nodeFunCondition(code){
      let _this = this
      let nodeDetail = _this.nodeDetailList.find(item=>item.code===code)
      if (nodeDetail&&nodeDetail.funCondition) {
        return JSON.parse(nodeDetail.funCondition)
      }else {
        return undefined
      }
    },
    getWorkflowprocesskey() {
      let _this = this
      // _this.loading = true
      _this.pListData = {}
      if (_this.procDefKey) {
          workflowprocesskey(_this.procDefKey).then((data) => {
              getStartActdef(data.data.procDefId).then((res) => {
                  _this.pListData = res.data;
                this.getExtAttributeModel()
              });
          });
      }else {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
      }
    },
    getExtAttributeModel(){
      let _this = this
      let procDefId = _this.pListData.procDefId
      let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
      if (procDefId&&curActDefId) {
        _this.getNodeDetailInfo()
        getExtAttributeModel(
          procDefId,
          curActDefId
        ).then((res) => {

          let kuozhanshujuBool = {}
          let kuozhanshuju = {}
          res.data.forEach(item=>{
            if (item.objType==='Boolean') {
              kuozhanshujuBool[item.objKey] = item.objValue
            } else {
              kuozhanshuju[item.objKey] = item.objValue
            }
          })
          _this.kuozhanshujuBool = kuozhanshujuBool;
          _this.kuozhanshuju = kuozhanshuju;
        }).finally(()=>{
          _this.loading = false
        });
      }else {
        _this.kuozhanshujuBool = {}
        _this.kuozhanshuju = {}
        _this.loading = false
      }
    },
    attributeModelBool(val){
      if (this.kuozhanshujuBool&&this.kuozhanshujuBool!=={}) {
        let obj = this.kuozhanshujuBool[val]
        return !!obj&&obj==='true'
      }else {
        return false
      }
    },
    attributeModel(val){
      return this.kuozhanshuju[val]
    },
    getNodeDetailInfo(){
        let _this = this
        let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
        if (_this.pListData&&curActDefId&&_this.formData.docClass) {
            getInfo(_this.formData.docClass,_this.pButton,curActDefId).then(res=>{
                let nodeDetail = {}
                res.data.forEach(item=>{
                  nodeDetail[item.code] = true
                })
                _this.nodeDetail = nodeDetail
                _this.nodeDetailList = res.data
                _this.initStatus()
            })
        }
    },
    close() {
      this.viewShow = false;
      this.$emit("close")
    },
    handlePreview(id) {
      this.viewId = id;
      this.$refs.viewRef.handleOpenView(id);
      this.viewShow = true;
    },
    // 处理提交数据，将数组字段转换为字符串
    processFormDataForSubmit(formData) {
      // 将 projectCode 数组转换为字符串
      if (formData.projectCode && Array.isArray(formData.projectCode)) {
        formData.projectCode = formData.projectCode.join(',')
      }
      return formData
    },
    saveFormConfirm(){
      let _this = this
      if (_this.watchStatus) {
        _this.watchStatus = false
        _this.$confirm(_this.$t(`file_handle.watch_status_text`), _this.$t(`file_handle.change_tip`), {
          confirmButtonText: _this.$t(`file_handle.change_confirm`),
          cancelButtonText: _this.$t(`doc.this_dept_abolish`),
          type: "warning",
        })
          .then(() => {
            _this.saveForm()
          })
      }else {
        _this.saveForm()
      }
    },
    //不需要验证必填的保存
    async saveForm() {
      let _this = this
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
        return;
      }
      if (await _this.validateCoincide()){
        return
      }
      _this.loading = true

      // 构建产品表格数据
      if (_this.isShowPart) {
        _this.buildProductTableData()
      }
      let formData = JSON.parse(JSON.stringify(_this.formData))
      // 处理数组字段转换为字符串
      formData = _this.processFormDataForSubmit(formData)
      if (_this.editStatus) {
        formData.recordStatus = "draft";
        if (_this.batchStatus) {
          formData.notUpdateTitle = true
        }
      }else {
        formData.onlyEdit = true
      }
      //变更操作（新增、修订、作废）的提交和保存的类型区分统一使用recordStatus字段（draft：草稿, doing:进行中，done:已完成，cancel:作废）
      formData.docLinks = [];
      if (_this.$refs.linkFile&&_this.$refs.linkFile.dataList) {
        formData.docLinks = _this.$refs.linkFile.dataList;
      }
      formData.noteLinks = [];
      if (_this.$refs.linkNote&&_this.$refs.linkNote.dataList) {
        formData.noteLinks = _this.$refs.linkNote.dataList;
      }
      if (_this.$refs.linkRecord&&_this.$refs.linkRecord.dataList) {
        formData.recordLinks = _this.$refs.linkRecord.dataList;
      }
      if (_this.standardDocfileList != "") {
        formData.standardDoc={
          docName: _this.standardDocfileList[0].name,
          fileId: _this.standardDocfileList[0].url,
          status: 1,
        }
      }else {
        formData.standardDoc = {docName:''}
      }
      formData.appendixes = [];
      _this.appendixesfileList.forEach((element) => {
        formData.appendixes.push({ fileId: element.url,docName:element.name, status: 1 });
      });
      formData.remarkDoc = [];
      if (_this.$refs.distributeBox) {
        formData.distributeList = _this.$refs.distributeBox.getDistributeList()
        let boxFormData = _this.$refs.distributeBox.formData
        let boxSetting = _this.$refs.distributeBox.setting
        for (let item in boxSetting) {
          formData[boxSetting[item]] = boxFormData[item]
        }
      }
      if (formData.id) {
        let res = await updateModifyApply(formData)
          if (res.code===200) {
            _this.$message({
              message: _this.$t(`file_handle.change_save_succ`),//提示的信息
              type:'success',　　//类型是成功
              duration:1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
              onClose:()=>{
                _this.loading = false
              }
            });
            if (_this.batchStatus) {
              _this.loading = false
              this.$emit("close",formData,_this.dataListIndex)
            }
        }
      } else {
        if (_this.batchStatus) {
          _this.loading = false
          this.$emit("close",formData,_this.dataListIndex)
          return
        }
        let wf_receivers = [];
        wf_receivers.push({
          receiveUserId: _this.userInfo.userName,
          receiveUserOrgId: _this.userInfo.deptId
        });
        formData.bpmClientInputModel = {
          model: {
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_procTitle: formData.docName.trim(),
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_curActDefName: _this.pListData.actDefName,
            wf_curActDefId: _this.pListData.actDefId,
            wf_nextActDefName: _this.pListData.actDefName,
            wf_nextActDefId: _this.pListData.actDefId,
          },
          order: _this.order,
          review: false,
          mark: _this.mark,
          type: _this.type,
        };
        formData.editStatus = _this.editStatus
        let res = await addModifyApply(formData)
          if (res.code===200) {
            _this.formData.id = res.data.businessKey;
            _this.procInstId = res.data.procInstId
            _this.procInstInfoAndStatus(res.data.procInstId)
            _this.$message({
              message: _this.$t(`file_handle.change_save_succ`),//提示的信息
              type:'success',　　//类型是成功
              duration:1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
              onClose:()=>{
                _this.loading = false
              }
            });
          }
      }
    },
    handleBackFlowToOne(){
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_withdraw_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value)=>{
          if(!value) {
            return _this.$t(`file_handle.change_fill_withdrawal_text`);
          }
        },
      }).then(({ value })=> {
          _this.loading = true
          getRecordbyPorcInstId(_this.procInstId).then(async res => {
            for (const item of res.data) {
              let revocationMsg = _this.$t(`file_handle.revocation.mgs`) == 'file_handle.revocation.mgs'?'':_this.$t(`file_handle.revocation.mgs`)
              let bpmClientInputModel = {
                model: {
                  wf_procInstId: _this.procInstId,
                  wf_procDefKey: item.procDefKey,
                  wf_procDefId: item.procDefId,
                  wf_procTitle: _this.formData.docName.trim(),
                  wf_curActInstId: item.curActInstId,
                  wf_sendUserId: item.recUserId,
                  wf_sendUserOrgId: item.recOrgId,
                  wf_curComment: revocationMsg+value,
                  wf_curActDefId: item.curActDefId,
                  wf_curActDefName: item.curActDefName,
                },
                bizType: _this.pButton,
                review: true,
                applyStatus: false,
                status: 'draft',
                type: _this.type,
                mark: _this.mark,
                order: 0,
              };
              //抢单模式多个待办只执行一个 第二个执行返回null跳出循环
              let res1 = await backFlowToOne(bpmClientInputModel)
              if (!res1.data) {
                break;
              }
            }
            _this.close(true);
          })
        })
    },
    deleteForm(){
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_revoke_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value)=>{
          if(!value) {
            return _this.$t(`file_handle.change_fill_revocation_text`);
          }
        },
      }).then(({ value })=> {
          _this.loading = true
          let formData = {
            id: _this.formData.id,
            bpmClientInputModel: {
              model: {
                wf_procDefKey: _this.procDefKey,
                wf_procDefId: _this.pListData.procDefId,
                wf_procInstId: _this.pListData.procInstId,
                wf_sendUserId: _this.userInfo.userName,
                wf_sendUserOrgId: _this.userInfo.deptId,
                wf_curActDefName: _this.pListData.curActDefName,
                wf_curActDefId: _this.pListData.curActDefId,
                wf_curActInstId: _this.pListData.curActInstId,
                wf_curComment: value,
              },
              order: _this.order,
              type: _this.type,
              mark: _this.mark,
              review: true,
              applyStatus: false,
            },
            recordStatus: 'cancel',
            editStatus: false
          }
          addModifyApply(formData).then((res) => {
            if (res.code === 200) {
              this.$modal.msgSuccess(_this.$t(`file_handle.change_revoke_success`));
              this.close(true);
            }
          });
        })
    },
    transferForm(){
      let _this = this
      _this.$refs.transferFlow.init(_this.pListData,_this.formData.id,_this.type,_this.order,_this.pButton)
    },
    rejectPreviousStep(){
      let _this = this
      _this.$prompt(_this.$t(`file_handle.reject_previous_step_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value)=>{
          if(!value) {
            return _this.$t(`doc.this_dept_ins_summary_remark`);
          }
        },
      }).then(async({ value }) => {
        _this.loading = true
        let bpmClientInputModel = {
          model: {
            wf_procInstId: _this.procInstId,
            wf_procDefKey: _this.procDefKey,
            wf_procDefId: _this.pListData.procDefId,
            wf_procTitle: _this.formData.docName.trim(),
            wf_curActInstId: _this.pListData.curActInstId,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_curComment: value,
            wf_curActDefId: _this.pListData.curActDefId,
            wf_curActDefName: _this.pListData.curActDefName,
          },
          bizType: _this.pButton,
          review: true,
          applyStatus: false,
          type: _this.type,
          mark: _this.mark,
        };
        backPreviousStep(bpmClientInputModel).then(res=>{
          _this.close(true);
        })
      })
    },
    submitFormConfirm(){
      let _this = this
      if (_this.watchStatus) {
        _this.watchStatus = false
        _this.$confirm(_this.$t(`file_handle.watch_status_text`), _this.$t(`file_handle.change_tip`), {
          confirmButtonText: _this.$t(`file_handle.change_confirm`),
          cancelButtonText: _this.$t(`doc.this_dept_abolish`),
          type: "warning",
        })
          .then(() => {
            _this.submitForm()
          })
      }else {
        _this.submitForm()
      }
    },
    // 提交
    async submitForm() {
      let _this = this
      // 首先验证产品表格数据（放在最开始确保一定执行）
      let productTableValid=true
      if (_this.isShowPart) {
         productTableValid = await _this.validateProductTable()
      }

      // 首先页签调整为 信息内容
      _this.activeName = 'info'
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
        return;
      }
      let dialogVisible = true

      // 构建产品表格数据
      if (_this.isShowPart) {
        _this.buildProductTableData()
      }


      //审核
      if ((_this.nodeShow('shenhe')||_this.nodeShow('pizhun'))) {
        if (_this.approvalStatus) {
          _this.formSubmit = _this.$refs.approvalBox.formSubmit
        }
        if (_this.formSubmit.pass===undefined) {
          _this.$modal.msgError(_this.submitLabel+_this.$t(`file_handle.change_result_not_null`));
          return;
        }
        // 验证是否填写了审核意见
        if(!_this.formSubmit.pass&&_this.formSubmit.summary.trim() == '') {
          _this.$modal.msgError(_this.$t(`doc.this_dept_pls_fill`)+_this.submitLabel+_this.$t(`doc.this_dept_comments`));
          return;
        }
      }
      let direction = _this.pListData.actDefOrder>_this.order
      //培训记录
      if (direction&&_this.nodeShow('page_oper_add_train_record')&&_this.formData.yNTrain!==_this.no) {
        let funCondition = _this.nodeFunCondition('page_oper_add_train_record')
        if (!funCondition||(funCondition&&funCondition.validate)) {
          if (!(_this.trains&&_this.trains.length>0)) {
            _this.$modal.msgError(_this.$t(`doc.this_dept_pls_upload_train_file`));
            return true;
          }
        }
      }
      //客户记录
      if (direction&&_this.nodeShow('add_customer_record')&&_this.formData.whetherCustomer!==_this.no) {
        let funCondition = _this.nodeFunCondition('add_customer_record')
        if (!funCondition||(funCondition&&funCondition.validate)) {
          if (!(_this.customerRecordList&&_this.customerRecordList.length>0)) {
            _this.$modal.msgError(_this.$t(`sys_mgr_log.user_signature_upload_text1`)+_this.$t(`doc.customer_record`)+'！');
            return true;
          }
        }
      }
      //签名
      if (_this.nodeShow('log_title')) {
        // 如果是编制环节校验编制人
        if(_this.editStatus){
          let res = await validateByUserName({userCode: _this.formData.userName})
          if (!res.data) {
            _this.$modal.msgError(_this.$t(`doc.user_organizer_validate`));
            return true;
          }
        }
        let res = await validateByUserName({userCode: _this.userInfo.userName})
        if (!res.data) {
          _this.$modal.msgError(_this.$t(`doc.user_signature_validate`));
          return true;
        }
      }

      // 验证主表单（包括单独的产品版本字段）
      let mainFormValid = true;
      if (!!_this.$refs["elForm"]) {
        mainFormValid = await _this.$refs["elForm"].validate()
        if (!mainFormValid) {
          dialogVisible = false

        }
      }

      // 如果主表单验证失败，直接返回
      if (!mainFormValid || !productTableValid) {
        return;
      }
      // 验证分发和培训表单
      if (!!_this.$refs["distributeBox"]) {
        let distributeForm = _this.$refs["distributeBox"].$refs["elFormDistribute"]
        if (!!distributeForm) {
          let validateValid = await distributeForm.validate()
          if (!validateValid) {
            dialogVisible = false
          }
        }
      }
      if (!!_this.$refs["distributeBox"]) {
        let trainForm = _this.$refs["distributeBox"].$refs["elFormTrain"]
        if (!!trainForm) {
          let validateValid = await trainForm.validate()
          if (!validateValid) {
            dialogVisible = false
          }
        }
      }
      if (!!_this.$refs["distributeBox"]) {
        let valid=await _this.$refs["distributeBox"].validateForm()
        if(!valid){
          dialogVisible = false
          return
        }
      }
      // 验证环节参与人员是否选择
      if (_this.nodeShow('top_btn_preset_user')){
        let nodeCode = this.pListData.actDefId || this.pListData.curActDefId
        let bool = await _this.$refs.presetUser.validate(_this.formData.docClass,_this.pButton,_this.formData.presetUserList,nodeCode)
        if (bool) {
          _this.$modal.msgWarning(_this.$t(`file_handle.change_auditing_text`));
          return false;
        }
      }
      //验证生成编号
      if (_this.nodeShow('top_btn_generate_code')) {
        if (_this.formData.docId == null) {
          _this.$modal.alert(_this.$t(`doc.this_dept_pls_gen_code_gen_cover_relase`));
          return false;
        }
        let res = await checkNoIsExist({ newNo: _this.formData.docId, busId: _this.formData.id })
        if (res.data) {
          _this.$modal.msgError(_this.formData.docId + _this.$t(`doc.this_dept_doc_code_exist`));
          return false;
        }
      }
      // if (await _this.validateCoincide()){
      //   return
      // }

      //校验
      if (await _this.validate()){
        return
      }
      _this.loading = true;
      _this.formData.docLinks = [];
      if (_this.$refs.linkFile&&_this.$refs.linkFile.dataList) {
        _this.formData.docLinks = _this.$refs.linkFile.dataList;
      }
      _this.formData.noteLinks = [];
      if (_this.$refs.linkNote&&_this.$refs.linkNote.dataList) {
        _this.formData.noteLinks = _this.$refs.linkNote.dataList;
      }
      if (_this.$refs.linkRecord&&_this.$refs.linkRecord.dataList) {
        _this.formData.recordLinks = _this.$refs.linkRecord.dataList;
        //验证记录文件编号
        if(!_this.classTypeRecordMN&&_this.formData.recordLinks.some(item=>!item.docId)) {
          _this.$modal.alert(_this.$t(`doc.this_dept_no_code_pls_gen_code_gen_cover_relase`));
          return false;
        }
      }
      if (_this.standardDocfileList != "") {
        this.formData.standardDoc={
          docName: _this.standardDocfileList[0].name,
          fileId: _this.standardDocfileList[0].url
        }
      }
      this.formData.appendixes = [];
      this.appendixesfileList.forEach((element) => {
        this.formData.appendixes.push({ fileId: element.url,docName:element.name, status: 1 });
      });
      this.formData.remarkDoc = [];
      if (_this.$refs.distributeBox) {
        this.formData.distributeList = _this.$refs.distributeBox.getDistributeList()
        let boxFormData = _this.$refs.distributeBox.formData
        let boxSetting = _this.$refs.distributeBox.setting
        for (let item in boxSetting) {
          this.formData[boxSetting[item]] = boxFormData[item]
        }
      }
      this.loading = false;
      if (!_this.approvalStatus) {
        _this.jointReviewRedirect()
        await _this.setPresetUserList()
        this.dialogVisible = true;
      }else {
        _this.$confirm(_this.$t(`file_handle.submit_text`), _this.$t(`file_handle.change_tip`), {
          confirmButtonText: _this.$t(`file_handle.change_confirm`),
          cancelButtonText: _this.$t(`doc.this_dept_abolish`),
          type: "warning",
        })
          .then(() => {
            _this.handleWorkflowSubmit({})
          })
      }
    },
    async setPresetUserList(){
      let _this = this
      //会签人员
      if(_this.nodeShow('preset_countersign')){
        let funCondition = _this.nodeFunCondition('preset_countersign')
        if (funCondition&&funCondition.nodeCode&& funCondition.nodeCode.length > 0&&funCondition.groupId) {
          let users = [];
          let res = await listDistributeGroupDetail({ groupId: funCondition.groupId })
          res.rows.forEach(item=>{
            users.push({
              userName: item.receiveUserName,
              nickName: item.receiveNickName,
              deptId: item.receiveUserDeptId,
              deptName: item.receiveUserDept,
            })
          })
          funCondition.nodeCode.forEach(nodeCode => {
            let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
            if (preset) {
              preset.users = JSON.stringify(users)
            } else {
              _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
            }
          })
        }
      }
      // 下个环节预选直属部门领导
      if(_this.nodeShow('next_set_leader')){
        let funCondition = _this.nodeFunCondition('next_set_leader')
        if (funCondition&&funCondition.nodeCode&& funCondition.nodeCode.length > 0) {
          let users = undefined;
          if (funCondition.validate) {
            let res = await getLeaderByUserName(_this.formData.userName)
            if (res.data) {
               users = [{userName:res.data.userName, nickName:res.data.nickName, deptId:res.data.deptId, deptName:res.data.dept.deptName}]
            }
          } else {
            let leader = _this.$store.getters.leader
            if (leader) {
               users = [{ userName: leader.userName,nickName: leader.nickName,deptId: leader.deptId,deptName: leader.dept.deptName}]
            }
          }
          if (users) {
            funCondition.nodeCode.forEach(nodeCode => {
              let preset = _this.formData.presetUserList.find(item => item.nodeCode === nodeCode)
              if (preset) {
                preset.users = JSON.stringify(users)
              } else {
                _this.formData.presetUserList.push({ nodeCode: nodeCode, users: JSON.stringify(users) })
              }
            })
          }
        }
      }
      //设定流程默认执行人
      let defaultStaff = []
      if (_this.formData.presetUserList.length>0) {
        defaultStaff.push(...JSON.parse(JSON.stringify(this.formData.presetUserList)))
      }
      if(_this.nodeShow('cdxmd')&&_this.formData.batch){
        // 文件类型设置中是否设置了 需要谁驳回就再只发送给驳回的人
        let funCondition = _this.nodeFunCondition('cdxmd')
        if (funCondition&&funCondition.validate) {
          //查询本次驳回有哪些人员
          let res = await listWorkflowLog({batch:_this.formData.batch,nextDefId:_this.pListData.curActDefId, havaDetail:true})
          let nodeCode = ""
          let users = []
          res.rows.forEach(item=>{
            nodeCode = item.actDefId
            users.push({userName:item.sender, nickName:item.nickName, deptId:item.senderDeptId, deptName:item.deptName})
          })
          if (defaultStaff.length>0) {
            let staff = defaultStaff.find(item=>item.nodeCode = nodeCode)
            if (staff){
              staff.users = JSON.stringify(users)
            }
          } else {
            defaultStaff.push({nodeCode:nodeCode,users:JSON.stringify(users)})
          }
        }
      }
      _this.defaultStaff = defaultStaff
      _this.searchQuery.step = _this.formData.step?_this.formData.step:0
      _this.searchQuery.isTrain = _this.formData.yNTrain
      _this.searchQuery.isCustomer = _this.formData.whetherCustomer
      _this.searchQuery.pass = _this.formSubmit.pass
      let hideNodeCode = []
      //下一环节未预选人员隐藏
      if (_this.nodeShow('xyhjwyxryyc')){
        let funCondition = _this.nodeFunCondition('xyhjwyxryyc')
        if (funCondition&&funCondition.nodeCode) {
          let length = funCondition.nodeCode.length
          //下一环节隐藏范围 都隐藏
          hideNodeCode = funCondition.nodeCode
          //过滤有预选人员的环节
          defaultStaff.forEach(item=>{
            if (item.users) {
              let users = JSON.parse(item.users)
              if (hideNodeCode.includes(item.nodeCode)&&users&&users.length>0){
                hideNodeCode = hideNodeCode.filter(code=>code!==item.nodeCode)
              }
            }
          })
          //配置了反向节点 隐藏范围环节内都没预选人员 过滤掉反向节点
          if (funCondition.neNodeCode&&hideNodeCode.length===length) {
            hideNodeCode = hideNodeCode.filter(code=>!funCondition.neNodeCode.includes(code))
          }
          //填写了限定值 只能显示最多限定的数量
          if (funCondition.limitValue) {
            let limitValue = Number(funCondition.limitValue)
            //总数-隐藏数=显示数 显示数>限定数量
            if (!isNaN(limitValue)&&(length-hideNodeCode.length)>limitValue){
              //倒叙再插回去
              let reverse = funCondition.nodeCode.reverse()
              for (let item of reverse) {
                if (!hideNodeCode.includes(item)) {
                  hideNodeCode.push(item)
                }
                if ((length-hideNodeCode.length)<=limitValue) {
                  break
                }
              }
            }
          }
          // if (funCondition.validate) {
          //验证开启 配置了反向节点 隐藏范围环节内都有预选人员 增加反向节点
          if (funCondition.neNodeCode&&hideNodeCode.length!==length&&hideNodeCode.length===0) {
            defaultStaff.forEach(item=>{
              if (funCondition.neNodeCode.includes(item.nodeCode)) {
                hideNodeCode.push(item.nodeCode)
              }
            })
            // }
          }
        }
      }
      //隐藏环节列表
      _this.hideNodeCode = hideNodeCode
    },
    jointReviewRedirect(){
      let _this = this
      if(_this.nodeShow('hscdx')){
        getRecordbyPorcInstId(_this.procInstId).then(res=>{
          //会审只剩最后一个环节
          if (res.data.length===1){
            let query={
              docClass: _this.formData.docClass,
              bizType: _this.pButton,
              code: "cdxmd",
              batch: _this.formData.batch,
            }
            getRedirectDefId(query).then(res1=>{
              if (res1.data) {
                let funCondition = _this.nodeFunCondition('hscdx')
                if (funCondition&&funCondition.nodeCode&&funCondition.nodeCode.length===1) {
                  let next = res1.data.find(item=>item.nextDefId===funCondition.nodeCode[0])
                  if (next) {
                    _this.redirectDefId = next.nextDefId
                    _this.redirectReceivers = JSON.parse(next.receiver)
                    _this.redirectOrder = next.actDefOrder
                  }
                }
              }
            })
          }
        })
      }
    },
    // 审批结论选择
    commentItemSelect(val) {
      let lang = this.$t(`doc.this_dept_pass`)
      if(val) {
        lang = this.$t(`doc.this_dept_pass`)
      } else {
        lang = this.$t(`doc.this_dept_not_pass`)
      }
      let summary = this.formSubmit.summary
      // 赋值审核意见 this.formSubmit.summary = lang +' '+summary
      this.formSubmit.summary = lang
    },
    async validateCoincide(){
      let _this = this
      let validate = false
      if (_this.editStatus&&_this.nodeShow('page_oper_prepare_id')&&_this.formData.docId){
        let res =await validatePrepareId({businessId:_this.formData.id,docId:_this.formData.docId})
        if (res.data) {
          _this.$modal.msgError(_this.formData.docId + _this.$t(`doc.this_dept_doc_code_exist`));
          validate = true
        }
      }
      return validate;
    },
    async validate(){
      let _this = this
      let validate = false
      // 验证文件状态
      if (_this.editStatus&&_this.formData.classType === _this.classTypeRecord) {
        let res = await selectStatusByDocId({versionId:_this.formData.upVersionId, notInDraft: true})
        if (res.data!=0) {
          _this.$modal.msgWarning(res.msg);
          return true;
        }
      }

      // 验证物流编码+物流描述+文件类型是否唯一
      // if (_this.editStatus && _this.formData.partNumber && _this.formData.partRemark){
        /*let res = await validateUniqueness({docClass:_this.formData.docClass,
          partNumber:_this.formData.partNumber, partRemark:_this.formData.partRemark, applyId: _this.formData.id})
        if(res){
          let item = _this.docClassList.find(item=>item.id===_this.formData.docClass)
          let name = item?item.className:_this.formData.docClass
          _this.$modal.msgError(_this.$t(`doc.this_dept_file_types`) + name + "，" + _this.$t(`doc.this_dept_product_codes`) + _this.formData.partNumber + "，" + _this.$t(`doc.this_dept_product_summarys`) + _this.formData.partRemark + "，" + _this.$t(`doc.this_dept_exist_pls_edit`));
          return true;
        }*/
      // }

      // 验证设备名称+设备编码+文件类型是否唯一
      /*if (_this.editStatus && _this.formData.deviceCode && _this.formData.deviceName){
        let docClassFlag = await validateUniqueness({docClass:_this.formData.docClass, deviceCode: _this.formData.deviceCode,
          deviceName: _this.formData.deviceName, applyId: _this.formData.id})
        if(docClassFlag){
          let item = _this.docClassList.find(item=>item.id===_this.formData.docClass)
          let name = item?item.className:_this.formData.docClass
          _this.$modal.msgError("文件类型：" + name + "，设备编码：" + _this.formData.deviceCode + "，设备名称描述：" + _this.formData.deviceName + "，已存在，请修改");
          return true;
        }
      }*/
      return false;
    },
    async handleCoverEffective() {
      let self = this
      // 验证设置生效时间
      if (self.nodeShow('publish_setup_time')&&self.formData.setupTime == null) {
        self.$modal.alert(self.$t(`doc.this_dept_set_effective_date`));
        return false;
      }
      //不用重复生成封面
      /*if ('C' === self.formData.isSignature) {
        self.$modal.alert(self.$t(`file_handle.change_signature_text1`));
        return false;
      }*/
      //验证生成编号
      if (self.nodeShow('top_btn_generate_code')) {
        if (self.formData.docId == null) {
          self.$modal.alert(self.$t(`doc.this_dept_pls_gen_code_gen_cover_relase`));
          return false;
        }
        let res = await checkNoIsExist({ newNo: self.formData.docId, busId: self.formData.id })
        if (res.data) {
          self.$modal.msgError(self.formData.docId + self.$t(`doc.this_dept_doc_code_exist`));
          return false;
        }
        if(self.formData.recordLinks.some(item=>!item.docId)) {
          self.$modal.alert(self.$t(`doc.this_dept_no_code_pls_gen_code_gen_cover_relase`));
          return false;
        }
      }

      self.$modal.msgSuccess(self.$t(`file_handle.change_signature_text2`));
      self.loading = true
      coverEffective(self.formData.id, 'cover').then((res) => {
        if (res.code === 200) {
          self.$modal.msgSuccess(self.$t(`doc.this_dept_operation_succ`));
          self.formData.encryptFileId = res.data
          self.formData.isSignature = 'C'
        } else {
          self.$modal.msgError(res.msg);
        }
        self.loading = false
      });
    },
    // 签章生效
    async handleSignEffective() {
      let self = this
      if ('E' === self.formData.isSignature) {
        self.$modal.alert(self.$t(`file_handle.change_signature_text1`));
        return false;
      }
      //生成封面
      if (self.nodeShow('top_btn_file_cover')) {
        if (self.formData.isSignature !== 'C') {
          self.$modal.alert(self.$t(`doc.this_dept_pls_gen_execute`)+'【'+self.$t(`file_handle.change_generate_cover`)+'】');
          return false;
        }
      }else {
        //设置了生成封面 就不用验证了
        if (self.nodeShow('top_btn_generate_code')) {
          if (self.formData.docId == null) {
            self.$modal.alert(self.$t(`doc.this_dept_pls_gen_code_gen_cover_relase`));
            return false;
          }
          let res = await checkNoIsExist({ newNo: self.formData.docId, busId: self.formData.id })
          if (res.data) {
            self.$modal.msgError(self.formData.docId + self.$t(`doc.this_dept_doc_code_exist`));
            return false;
          }
          if(self.formData.recordLinks.some(item=>!item.docId)) {
            self.$modal.alert(self.$t(`doc.this_dept_no_code_pls_gen_code_gen_cover_relase`));
            return false;
          }
        }
      }
      self.$modal.msgSuccess(self.$t(`file_handle.change_signature_text2`));
      self.loading = true
      signEffective(self.formData.id, 'effective').then((res) => {
        if (res.code === 200) {
          self.$modal.alert(self.$t(`file_handle.change_signature_text3`));
          self.formData.encryptFileId = res.data
          self.formData.isSignature = 'E'
        } else {
          self.$modal.alert(res.msg);
        }
        self.loading = false
      });
    },
    setupStartTime(){
      let _this = this
      _this.setupTime = _this.formData.setupTime
      if (!_this.setupTime) {
        _this.setupTime = parseTime(new Date())
      }
      _this.pushDialogVisible = true
    },
    // 发布二次确认
    pushCommit(){
      let _this = this
      if(!_this.setupTime){
        _this.$modal.msgError(_this.$t(`doc.this_dept_set_effective_date`))
        return
      }
      if (_this.formData.setupTime !== _this.setupTime) {
        let form = {
          id: _this.formData.id,
          setupTime: _this.setupTime,
          isSignature: 'N',
          onlyEdit: true
        }
        updateModifyApply(form).then((res) => {
          if (res.code===200) {
            _this.formData.isSignature = form.isSignature
            _this.formData.setupTime = form.setupTime
            _this.pushDialogVisible = false
          }
        });
      }else {
        _this.pushDialogVisible = false
      }
    },
    // 执行发布（推送文件到文件台账）
    async handlePublish() {
      let self = this
      if (self.nodeShow('top_btn_setup_time')&&self.formData.isSignature !== 'E') {
        self.$modal.alert(self.$t(`doc.this_dept_pls_effect_signature_relase`));
        return false;
      }
      if (self.nodeShow('set_dept_receiver')) {
        if (!!self.$refs["distributeBox"]) {
          let valid=await self.$refs["distributeBox"].validateForm()
          if(!valid){
            return
          }
        }
      }
      self.$modal.confirm(self.$t(`doc.this_dept_confirm_release`)).then(function() {
        // 执行发布的签章
        self.loading = true
        signEffective(self.formData.id, 'publish').then((res) => {
          if (res.code === 200) {
            self.$modal.msgSuccess(self.$t(`file_handle.change_signature_text6`));
            self.formData.encryptFileId = res.data
            self.formData.isSignature = 'Y'
            self.pushDialogVisible = false
            self.handleWorkflowSubmit('publish');
          } else {
            self.$modal.alert(res.msg);
          }
          self.loading = false
        });
      })
    },
    // 下载履历
    async downloadResume() {
      let self = this
      if (!self.formData.id) {
        self.$modal.msgError(self.$t(`doc.this_dept_no_apply_id`));
        return;
      }

      try {
        self.loading = true
        const response = await downloadResumeApi(self.formData.id,'ADD')

        // 创建下载链接
        const blob = new Blob([response], { type: 'application/octet-stream' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        // 设置下载文件名，可以根据需要调整
        const fileName = `${self.formData.docName}.pdf`
        link.download = fileName

        // 触发下载
        document.body.appendChild(link)
        link.click()

        // 清理
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        self.$modal.msgSuccess(self.$t(`doc.download_resume`) + self.$t(`doc.this_dept_operation_succ`))
      } catch (error) {
        console.error('下载履历失败:', error)
        self.$modal.msgError(self.$t(`doc.download_resume`) + self.$t(`doc.this_dept_operation_fail`))
      } finally {
        self.loading = false
      }
    },
    //提交表单和流程数据
    async handleWorkflowSubmit(invokeFrom) {
      let _this = this
      if (await _this.validateCoincide()) {
        return
      }
      let formData = JSON.parse(JSON.stringify(_this.formData))
      // 处理数组字段转换为字符串
      formData = _this.processFormDataForSubmit(formData)
      let wf_receivers = [];
      let wf_nextActDefId = null
      let wf_nextActDefName = null
      let direction = null
      let prochild = _this.approvalStatus? _this.$refs.approvalBox:_this.$refs.prochild
      if (typeof (invokeFrom) == 'object') {
        if (prochild.receiveUserList.length < 1 && prochild.nextData.actDefType !== 'endEvent') {
          if (_this.approvalStatus) {
            _this.$message.warning(_this.$t(`doc.this_dept_directory_select_user_alert`,[prochild.curOptionLabel]));
          }else {
            _this.$message.warning(_this.$t(`doc.this_dept_select_user_alert`));
          }
          return;
        }
        prochild.receiveUserList.forEach((element) => {
          wf_receivers.push({
            receiveUserId: element.id,
            receiveUserOrgId: element.parentId,
          });
        });
        wf_nextActDefId = prochild.nextData.actDefId;
        wf_nextActDefName = prochild.nextData.actDefName;
        direction = _this.pListData.actDefOrder>_this.order
      } else if (typeof (invokeFrom) == 'string' && invokeFrom == 'publish') {
        // 来源于按钮【执行发布】
        wf_nextActDefId = 'end'
        wf_nextActDefName = '结束'
        direction = true
      }

      // 显示加载中
      _this.flowStepLoading = true
      _this.detailLoading = true
      if (_this.pListData && _this.pListData.procInstId) {
        //流程执行参数
        formData.bpmClientInputModel = {
          model: {
            wf_procDefKey: _this.procDefKey,
            wf_procDefId: _this.pListData.procDefId,
            wf_procTitle: _this.formData.docName.trim(),
            wf_curActInstId: _this.pListData.curActInstId,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_nextActDefId: wf_nextActDefId,
            wf_curComment: _this.formSubmit.summary,
            wf_curActDefId: _this.pListData.curActDefId,
            wf_curActDefName: _this.pListData.curActDefName,
            wf_nextActDefName: wf_nextActDefName,
          },
          order: _this.pListData.actDefOrder,
          review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
          applyStatus: _this.formSubmit.pass,
          type: _this.type,
          mark: _this.mark,
          direction: direction,
          step: _this.attributeModel("step")
        };
      } else {
        //创建流程参数
        formData.bpmClientInputModel = {
          type: _this.type,
          model: {
            wf_procTitle: _this.formData.docName.trim(),
            wf_nextActDefId: wf_nextActDefId,
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_curActDefName: _this.pListData.actDefName,
            wf_curActDefId: _this.pListData.actDefId,
            wf_nextActDefName: wf_nextActDefName,
          },
          order: _this.pListData.actDefOrder,
          review: _this.nodeShow('shenhe') || _this.nodeShow('pizhun'),
          applyStatus: _this.formSubmit.pass,
          mark: _this.mark,
          direction: direction,
          step: _this.attributeModel("step")
        };
      }
      if (_this.nodeShow('log_title')) {
        let funCondition = _this.nodeFunCondition('log_title')
        if (funCondition && funCondition.limitValue) {
          formData.bpmClientInputModel.title = funCondition.limitValue
        }
      }
      if (_this.nodeShow('top_btn_publish_file') && wf_nextActDefId === 'end') {
        //办结
        formData.recordStatus = 'done'
        formData.bpmClientInputModel.jointReview = false
      } else {
        //进行中
        formData.recordStatus = 'doing'
        formData.bpmClientInputModel.jointReview = prochild.nextData.multi
      }
      if (_this.nodeShow('hscdx')) {
        formData.bpmClientInputModel.batch = formData.batch
        formData.bpmClientInputModel.redirectDefId = _this.redirectDefId
        formData.bpmClientInputModel.redirectReceivers = _this.redirectReceivers
        if (_this.redirectOrder) {
          formData.bpmClientInputModel.order = _this.redirectOrder
        }
      }
      formData.editStatus = _this.editStatus
      formData.onlyEdit = _this.nodeShow('doc_recheck_act')
      formData.presetUserEdit = _this.nodeShow('top_btn_preset_user') || _this.nodeShow('next_set_leader')
      formData.customerEdit = _this.nodeShow('whether_customer_record')
      addModifyApply(formData).then((res) => {
        if (res.code === 200) {
          _this.$message({
            message: _this.$t(`doc.this_dept_process_sub_succ`),//提示的信息
            type: 'success',　　//类型是成功
            duration: 1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
            onClose: () => {
              _this.flowStepLoading = false
              _this.detailLoading = false
              _this.dialogVisible = false;
              _this.close();
            }
          });
        }
      });
    },
    async getSettingDocClassTreeseList() {
      let _this = this
      let query = { classStatus: "1", dataType:this.formData.dataType,classTypeList:this.classTypeList,openPurview:true }
      await settingDocClassList(query).then(
        (res) => {
          this.docClassList = JSON.parse(JSON.stringify(res.rows))
          let classLevelOptions = []
          if (_this.editStatus&&!_this.procInstId) {
            classLevelOptions = this.handleTree(res.rows.filter(item=>item.purview),"id","parentClassId");
            classLevelOptions = classLevelOptions.filter(item=>{
              return checkPermi(["doc:class:"+item.id])
            })
          }else {
            classLevelOptions = this.handleTree(res.rows,"id","parentClassId");
          }
          this.classLevelOptions = classLevelOptions
        }
      );
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    normalizerDept(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    // 树形结构数据转化成列表结构数据
    deepTraversal(data) {
      const result = [];
      data.forEach((item) => {
        const loop = (data) => {
          result.push({
            deptId: data.deptId,
            deptName: data.deptName,
            notPrint: data.notPrint,
            nums: data.nums,
          });
          let child = data.children;
          if (child) {
            for (let i = 0; i < child.length; i++) {
              loop(child[i]);
            }
          }
        };
        loop(item);
      });
      return result;
    },
    handleMonitor() {
      this.monitorDrawerVisible = true;
      this.$nextTick(() => {
        this.$refs.monitorDrawer.init(this.procInstId);
      });
    },
    handelpbohuiqicaoren() {
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_reject_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        inputValue: _this.formSubmit.summary,
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value)=>{
          if(!value) {
            return _this.$t(`file_handle.change_fill_reject_text`);
          }
        },
      })
        .then(({ value }) => {
      // _this.$modal.confirm('是否确认将流程驳回至编制人？').then(res => {
        let backarr = {
          model: {
            wf_procDefKey: _this.procDefKey,
            wf_businessKey: _this.formData.id,
            wf_curActInstId: _this.pListData.curActInstId,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_procTitle: _this.formData.docName.trim(),
            wf_procDefId: _this.pListData.procDefId,
            wf_curComment: value,
            wf_curActDefName: _this.pListData.curActDefName,
            wf_curActDefId: _this.pListData.curActDefId,
            wf_receivers: [
              {
                receiveUserId: _this.formData.userName,
                receiveUserOrgId: _this.formData.deptId
              }
            ],
          },
          review: true,
          applyStatus: false,
          type: _this.type,
          step: _this.attributeModel("step"),
          bizType: _this.pButton
        };
        workflowbacktostart(backarr).then((response) => {
          this.$modal.msgSuccess(_this.$t(`file_handle.change_reject_succ`));
          _this.close();
        });
      })
    },
    async shengchengbianhao() {
      let _this = this
      if (_this.editStatus&&!_this.formData.docId) {
        if (_this.codeRuleDetail){
          let bool = false
          for (let item of _this.codeRuleDetail){
            if (_this.rules[item]&&_this.rules[item][0].required&&!_this.formData[item]) {
              _this.$modal.msgWarning(_this.rules[item][0].message);
              bool = true
            }
          }
          if (bool) {
            return
          }
        }
        await _this.saveForm()
      }
      _this.shenchenbianhao = true;
      let fileIds = _this.formData.recordLinks && _this.formData.recordLinks.length > 0 ? _this.formData.recordLinks.map(item => item.fileId) : null
      _this.$nextTick(() => {
        _this.$refs.docIdBox.init(_this.formData.id, fileIds);
      })
    },
    setDocId(docId){
      let _this =this
      if (_this.formData.docId !== docId) {
        _this.formData.docId = docId
        _this.setSignatureStatus()
      }
    },
    setRecordDocId(recordLinks){
      let _this =this
      recordLinks.forEach((element, i) => {
        _this.formData.recordLinks.forEach((val, ix) => {
          if (val.fileId === element.busId) {
            _this.formData.recordLinks[ix].docId = element.newNo;
          }
        });
      });
    },
    onProjectChange(val){
      let _this = this
      _this.formData.projectId = val.id
      _this.formData.projectName = val.name
    },
    prepareIdSelect(source,codeType,docClass,parentDocId){
      let _this = this
      _this.$nextTick(() => {
        _this.$refs.prepareDocId.init(source, null,_this.formData.userName,_this.formData.dataType, codeType,docClass,parentDocId,null,_this.oldDocId)
      })
    },
    handleSubmitDocId(source,index,data){
      let _this = this
      if (_this.formData.docId !== data.docId) {
        _this.$set(_this.formData,"docId",data.docId)
        _this.$set(_this.formData,"docClass",data.docClass)
        _this.$set(_this.formData,"classType",data.codeType)
      }
    },
    versionSearchInit(source){
      let _this = this
      _this.$nextTick(()=>{
        _this.$refs.versionList.init(source,null,false)
      })
    },
    versionSelectHandle(source,index,data){
      if (source==='up') {
        this.formData.upVersionId = data.versionId
        this.formData.upDocName = data.docName
        this.formData.parentDocId = data.docId
        this.$refs.elForm.validateField("upVersionId");
      }else {
        this.formData.programVersionId = data.versionId
        this.formData.programDocName = data.docName
        this.formData.programDocId = data.docId
      }
    },
    selectDocClass(node){
      this.formData.classType = node.classType
      this.formData.docClass = node.id
      this.$refs.elForm.validateField("docClass")
      //查询物料、工厂的字段是否开启
      this.getByDocClass(node.id);
    },
    getByDocClass(docClass) {
      let _this = this
      _this.detailLoading = true
      getInfoBy({type:'formShow',docClass:docClass}).then(async res => {
        this.isShowPart = false
        this.materialCodeRules[0].required = false
        this.materialDescriptionRules[0].required = false
        this.rules.factorys[0].required = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.isShowPart = true
            this.materialCodeRules[0].required = true
            this.materialDescriptionRules[0].required = true
            this.rules.factorys[0].required = true
          }
        }
        if(!this.isShowPart){
          this.$set(this.formData,"partNumber",'')
          this.$set(this.formData,"partRemark",'')
          this.$set(this.formData,"factorys",'')
        }
        _this.detailLoading = false
      }).catch(err => {
        console.log(err)
        _this.detailLoading = false
      })


      getInfoBy({type:'formCustomerShow',docClass:docClass}).then(async res => {
        this.isCustomerShow = false
        this.rules.customerCode[0].required = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.isCustomerShow = true
            this.rules.customerCode[0].required = true
          }
        }
        if(!this.isCustomerShow){
          this.$set(this.formData,"customerCode",'')
        }
        _this.detailLoading = false
      }).catch(err => {
        console.log(err)
        _this.detailLoading = false
      })

      getInfoBy({type:'shelfLifeShow',docClass:docClass}).then(async res => {
        this.isShelfLifeShow = false
        this.rules.shelfLife[0].required = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.isShelfLifeShow = true
            this.rules.shelfLife[0].required = true
          }
        }
        if(!this.isShelfLifeShow){
          this.$set(this.formData,"shelfLife",'')
        }
        _this.detailLoading = false
      }).catch(err => {
        console.log(err)
        _this.detailLoading = false
      })

      getInfoBy({type:'codeAndTypeShow',docClass:docClass}).then(async res => {
        this.isCodeAndTypeShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.isCodeAndTypeShow = true
            if(this.isCodeAndTypeShow && !this.theFirstStatus){
              this.formData.docName = this.formData.partRemark ? this.formData.partRemark + "_" + this.docClassData.className : this.docClassData.className
            }
          }
        }
        if(!this.isCodeAndTypeShow && !this.theFirstStatus){
          this.$set(this.formData,"docName",'')
        }
        _this.detailLoading = false
        _this.theFirstStatus = false
      }).catch(err => {
        console.log(err)
        _this.detailLoading = false
      })

      getInfoBy({type:'projectCodeShow',docClass:docClass}).then(async res => {
        this.projectCodeShow = false
        this.rules.projectCode[0].required = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.projectCodeShow = true
            this.rules.projectCode[0].required = true
          }
        }
        if(!this.projectCodeShow){
          this.$set(this.formData,"projectCode",'')
        }
        _this.detailLoading = false
      }).catch(err => {
        console.log(err)
        _this.detailLoading = false
      })

      getInfoBy({type:'internalDocIdShow',docClass:docClass}).then(async res => {
        this.internalDocIdShow = false
        this.rules.internalDocId[0].required = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.internalDocIdShow = true
            this.rules.internalDocId[0].required = true
          }
        }
        if(!this.internalDocIdShow){
          this.$set(this.formData,"internalDocId",'')
        }
        _this.detailLoading = false
      }).catch(err => {
        console.log(err)
        _this.detailLoading = false
      })

      getInfoBy({type:'ecnCodeShow',docClass:docClass}).then(async res => {
        this.ecnCodeShow = false
        this.rules.ecnCode[0].required = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.ecnCodeShow = true
            this.rules.ecnCode[0].required = true
          }
        }
        if(!this.ecnCodeShow){
          this.$set(this.formData,"ecnCode",'')
        }
        _this.detailLoading = false
      }).catch(err => {
        console.log(err)
        _this.detailLoading = false
      })

      getInfoBy({type:'versionIsUpdateShow',docClass:docClass}).then(async res => {
        this.versionIsUpdateShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.versionIsUpdateShow = true
          }
        }
        _this.detailLoading = false
      }).catch(err => {
        console.log(err)
        _this.detailLoading = false
      })

      getInfoBy({type:'projectNameSecurityKeywordByteShow',docClass:docClass}).then(async res => {
        this.projectNameSecurityKeywordByteShow = false
        this.rules.projectName[0].required = false
        this.rules.securityClass[0].required = false
        this.rules.keyword[0].required = false
        this.rules.docBytes[0].required = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.projectNameSecurityKeywordByteShow = true
            this.rules.projectName[0].required = true
            this.rules.securityClass[0].required = true
            this.rules.keyword[0].required = true
            this.rules.docBytes[0].required = true
          }
        }
        if(!this.projectNameSecurityKeywordByteShow){
          this.$set(this.formData,"projectName",'')
          this.$set(this.formData,"securityClass",'')
          this.$set(this.formData,"keyword",'')
          this.$set(this.formData,"docBytes",'')
        }
        _this.detailLoading = false
      }).catch(err => {
        console.log(err)
        _this.detailLoading = false
      })

      getInfoBy({type:'formDeviceShow',docClass:docClass}).then(async res => {
        this.isDeviceShow = false
        this.rules.deviceCode[0].required = false
        this.rules.deviceName[0].required = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.isDeviceShow = true
            this.rules.deviceCode[0].required = true
            this.rules.deviceName[0].required = true
          }
        }
        if(!this.isDeviceShow){
          this.$set(this.formData,"deviceCode",'')
          this.$set(this.formData,"deviceName",'')
        }
        _this.detailLoading = false
      }).catch(err => {
        console.log(err)
        _this.detailLoading = false
      })

      getInfoBy({type:'formProductVersionShow',docClass:docClass}).then(async res => {
        this.isShowProductVersion = false
        this.rules.productVersion[0].required = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.isShowProductVersion = true
            this.rules.productVersion[0].required = true
          }
        }
        _this.detailLoading = false
      }).catch(err => {
        console.log(err)
        _this.detailLoading = false
      })

      getInfoBy({type:'systemClauseShow',docClass:docClass}).then(async res => {
        this.isSystemClauseShow = false
        this.rules.systemClause[0].required = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.isSystemClauseShow = true
            this.rules.systemClause[0].required = true
          }
        }
        if(!this.isSystemClauseShow){
          this.$set(this.formData,"systemClause",'')
        }
        _this.detailLoading = false
      }).catch(err => {
        console.log(err)
        _this.detailLoading = false
      })

      getInfoBy({type:'complianceShow',docClass:docClass}).then(async res => {
        this.complianceShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.complianceShow = true
          }
        }
        _this.detailLoading = false
      }).catch(err => {
        console.log(err)
        _this.detailLoading = false
      })

      // 获取文件用途字段显示控制
      getInfoBy({type:'filePurposeShow',docClass:docClass}).then(async res => {
        this.filePurposeShow = false
        this.rules.filePurpose[0].required = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.filePurposeShow = true
            this.rules.filePurpose[0].required = true
          }
        }
        if(!this.filePurposeShow){
          this.$set(this.formData,"filePurpose",'')
        }
        _this.detailLoading = false
      }).catch(err => {
        console.log(err)
        // 默认显示文件用途字段
        this.filePurposeShow = true
        this.rules.filePurpose[0].required = true
        _this.detailLoading = false
      })

      // 获取法规标准状态字段显示控制
      getInfoBy({type:'regulationStatusShow',docClass:docClass}).then(async res => {
        this.regulationStatusShow = false
        this.rules.regulationStandardStatus[0].required = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.regulationStatusShow = true
            this.rules.regulationStandardStatus[0].required = true
          }
        }
        if(!this.regulationStatusShow){
          this.$set(this.formData,"regulationStandardStatus",'')
        }
      }).catch(err => {
        console.log(err)
        // 默认不显示法规标准状态字段
        this.regulationStatusShow = false
        this.rules.regulationStandardStatus[0].required = false
      })

      // 获取法规/标准发布日期字段显示控制
      getInfoBy({type:'regulationPublishDateShow',docClass:docClass}).then(async res => {
        this.regulationPublishDateShow = false
        this.rules.regulationPublishDate[0].required = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.regulationPublishDateShow = true
            this.rules.regulationPublishDate[0].required = true
          }
        }
        if(!this.regulationPublishDateShow){
          this.$set(this.formData,"regulationPublishDate",'')
        }
      }).catch(err => {
        console.log(err)
        // 默认不显示法规/标准发布日期字段
        this.regulationPublishDateShow = false
        this.rules.regulationPublishDate[0].required = false
      })

      // 获取法规/标准实施日期字段显示控制
      getInfoBy({type:'regulationImplementDateShow',docClass:docClass}).then(async res => {
        this.regulationImplementDateShow = false
        this.rules.regulationImplementDate[0].required = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.regulationImplementDateShow = true
            this.rules.regulationImplementDate[0].required = true
          }
        }
        if(!this.regulationImplementDateShow){
          this.$set(this.formData,"regulationImplementDate",'')
        }
      }).catch(err => {
        console.log(err)
        // 默认不显示法规/标准实施日期字段
        this.regulationImplementDateShow = false
        this.rules.regulationImplementDate[0].required = false
      })

      // 获取归属上级文件字段显示控制
      getInfoBy({type:'programVersionId',docClass:docClass}).then(async res => {
        this.programVersionId = false
        this.rules.programDocId[0].required = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.programVersionId = true
            this.rules.programDocId[0].required = true
          }
        }
        if(!this.programVersionId){
          this.$set(this.formData,"programVersionId",'')
          this.$set(this.formData,"programDocName",'')
          this.$set(this.formData,"programDocId",'')
        }
      }).catch(err => {
        console.log(err)
        this.programVersionId = false
        this.rules.programDocId[0].required = false
      })
    },
    selectPresetUser(){
      let _this = this
      _this.$nextTick(()=>{
        let nodeCode = this.pListData.actDefId || this.pListData.curActDefId
        _this.$refs.presetUser.init(null,null,_this.formData.docClass,_this.pButton,_this.formData.presetUserList,nodeCode)
      })
    },
    async selectHandlePresetUser(source, index, data) {
      let _this = this
      _this.$set(_this.formData, "presetUserList", data)
      if (_this.approvalStatus) {
        await _this.setPresetUserList()
        if (_this.$refs.approvalBox) {
          _this.$refs.approvalBox.init()
        }
      }
    },
    handleCloseChange(){
      this.dealDrawerShow = false
    },
    handleDeal(formData) {
      let _this = this
      _this.dealDrawerShow = true;
      _this.$nextTick(() => {
        _this.$refs.dealDrawer.init({type:formData.invokeType,preChangeCode:formData.invokeId});
      });
    },
    fileUpdate(field){
      let _this = this
      //文件重新上传 需要重新签章
      _this.setSignatureStatus()
      _this.$refs.elForm.validateField(field)
    },
    setSignatureStatus(){
      let _this = this
      if (_this.formData.id && _this.formData.isSignature !== 'N') {
        _this.formData.isSignature = 'N'
        updateModifyApply({
          id: _this.formData.id,
          isSignature: _this.formData.isSignature,
          onlyEdit: true
        })
      }
    },
    getDeptList(){
      // deptLevel = 2 只显示组织层级2级以内的节点
      listDept({ status: 0 }).then((response) => {
        this.deptList = JSON.parse(JSON.stringify(response.data))
        this.deptOptions = this.handleTree(response.data, "deptId");
      });
    },
    getCompanyDataList() {
      let _this = this
      getCompanyList({}).then(res=>{
        _this.companyList = res.data
      })
    },
    handleSelect() {
      let _this = this
      _this.$nextTick(()=>{
        _this.$refs.userList.init(null,null,null)
      })
    },
    async handleSubmitUser(source, index, user) {
      let _this = this
      this.formData.nickName = user.nickName;
      this.formData.userName = user.userName;
      this.formData.deptId = user.dept.deptId;
      this.formData.deptName = user.dept.deptName;
      if (_this.approvalStatus) {
        await _this.setPresetUserList()
        if (_this.$refs.approvalBox) {
          _this.$refs.approvalBox.init()
        }
      }
    },
    // 产品表格操作方法
    addProductTableRow() {
      this.productTableData.push({
        materialCode: '',
        productVersion: '',
        materialDescription: ''
      });
    },
    removeProductTableRow(index) {
      if (this.productTableData.length > 1) {
        this.productTableData.splice(index, 1);
        // 删除后需要重新校验表单
        this.$nextTick(() => {
          if (this.$refs.productTableForm) {
            this.$refs.productTableForm.clearValidate();
          }
        });
      }
    },
    // 验证产品表格数据
    validateProductTable() {
      if (!this.isShowPart) {
        return Promise.resolve(true);
      }

      if (this.$refs.productTableForm) {
        return new Promise((resolve) => {
          // 先清除之前的验证状态
          this.$refs.productTableForm.clearValidate();

          // 使用 $nextTick 确保清除操作完成后再进行验证
          this.$nextTick(() => {
            // 调用表单的整体验证，这会触发所有字段的验证
            this.$refs.productTableForm.validate((valid) => {
              resolve(valid);
            });
          });
        });
      }
      return Promise.resolve(true);
    },
    // 构建产品表格数据用于提交
    buildProductTableData() {
      if (this.isShowPart && this.productTableData.length > 0) {
        // 将表格数据转换为原有格式以保持兼容性
        const partNumbers = this.productTableData.map(item => item.materialCode).filter(code => code.trim());
        this.formData.partNumber = partNumbers.join(';');

        // 处理产品版本，使用 ; 分隔
        if (this.isShowProductVersion) {
          const productVersions = this.productTableData.map(item => item.productVersion || '').filter(version => version.trim());
          this.formData.productVersion = productVersions.join(';');
        }

        // 处理产品描述，使用 ; 分隔
        const descriptions = this.productTableData.map(item => item.materialDescription || '').filter(desc => desc.trim());
        this.formData.partRemark = descriptions.join(';');

        // 保存完整的表格数据
        this.formData.productTableData = JSON.stringify(this.productTableData);
      }
    },
    // 从保存的数据恢复表格数据
    restoreProductTableData() {
      this.restoreFromLegacyData();
    },
    // 从原有数据格式恢复
    restoreFromLegacyData() {
      if (this.formData.partNumber) {
        const partNumbers = this.formData.partNumber.split(';');
        const descriptions = this.formData.partRemark ? this.formData.partRemark.split(';') : [];
        const productVersions = this.formData.productVersion ? this.formData.productVersion.split(';') : [];

        this.productTableData = partNumbers.map((code, index) => ({
          materialCode: code || '',
          productVersion: productVersions[index] || '',
          materialDescription: descriptions[index] || ''
        }));

      } else {
        this.productTableData = [{
          materialCode: '',
          productVersion: '',
          materialDescription: ''
        }];
      }
    },
  },
};
</script>
<style>
body {
  background: #fff;
  font-size: 14px;
  color: #333;
  margin: 0;
  padding: 0;
  min-width: 1200px;
}
@media screen and (max-width: 1200px) {
  .el-drawer{
    overflow-x: auto;
    overflow-x: auto !important;
    width: 100% !important;
  }
  body .el-drawer .el-drawer__header{
    min-width: 1200px;
  }
  body .el-drawer .el-drawer__body{
    min-width: 1200px;
  }
  .phpage .el-drawer .el-drawer__header{
    min-width: 100px;
  }
  .phpage .el-drawer .el-drawer__body{
    min-width: 100px;
  }
}

.document_change_add {
  .fujian .el-textarea__inner {
    border: 0 solid #dcdfe6;
    padding: 0;
  }
}

/* 项目代码标签样式优化 */
.project-code-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
}

.project-code-tags .el-tag {
  margin: 0;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.project-code-tags span {
  display: inline-block;
  margin: 0 8px 8px 0;
  padding: 4px 8px;
  background-color: #f0f2f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
