<template>
  <div class="document_change_add"  v-loading="loading||detailLoading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{ $t(`doc.this_dept_file_detail`) }}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button v-if="formData&&formData.procInstId" @click="handleMonitor">{{ $t(`doc.this_dept_process_monitor`) }}</el-button>
        <el-button @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <el-tabs v-if="formData&&formData.procInstId" v-model="activeName">
      <el-tab-pane :label="$t(`doc.this_dept_info_content`)" name="info"></el-tab-pane>
      <el-tab-pane :label="$t(`doc.this_dept_approval_records`)" name="log"></el-tab-pane>
    </el-tabs>
    <div class="dialog-body" v-show="activeName==='info'">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_base_msg`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elForm"
            :model="formData"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row gutter="15" v-if="isProject">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_project`)+`:`" prop="projectId">
                  <span>{{formData.projectName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label=" " prop="">
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`myItem.borrow_file_type`)+`:`" prop="docClass">
                  <treeselect
                    v-model.trim="formData.docClass"
                    :options="classLevelOptions"
                    :normalizer="normalizer"
                    :show-count="true"
                    :searchable="false"
                    :placeholder="$t(`doc.this_dept_select_type`)"
                    :disabled="!editStatus"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label=" " prop="changeType">

                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15"  v-if="formData.classType===classTypeRecord">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_by_superior`)+`:`" prop="upVersionId">
                  <span>{{formData.upDocName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_superior_file_name`)" prop="parentDocId">
                  <span>{{formData.parentDocId}}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`myItem.borrow_file_name`)+`:`" prop="docName">
                  <el-input
                    v-if="editStatus"
                    v-model="formData.docName"
                    :placeholder="$t(`doc.this_dept_insert_name`)"
                    clearable
                    :style="{ width: '100%' }"
                    maxlength="50"
                    show-word-limit
                    :disabled="disabled"
                  ></el-input>
                  <span v-else>{{formData.docName}}</span>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item :label="$t(`myItem.borrow_file_id`)+`:`" prop="docId">
                  <span>{{ formData.docId }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <template v-if="internalDocIdShow">
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_internal_file_number`)" prop="internalDocId">
                    <span>{{formData.internalDocId}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <template v-if="ecnCodeShow">
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_ecn_number`)" prop="ecnCode">
                    <span>{{formData.ecnCode}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`myItem.borrow_file_ver`)+`:`" prop="versionValue">
                  <el-input
                    v-if="editStatus"
                    v-model="formData.versionValue"
                    :placeholder="$t(`doc.this_dept_insert_ver`)"
                    clearable
                    :style="{ width: '100%' }"
                    maxlength="50"
                    show-word-limit
                  ></el-input>
                  <span v-else>{{formData.versionValue}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t(`myItem.borrow_preparation_dept`)+`:`" prop="deptName">
                  <span>{{ formData.deptName }}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`myItem.borrow_preparer`)+`:`" prop="userName">
                  <span>{{ formData.nickName }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_preparation_time`)+`:`" prop="applyTime">
                  <span>{{ parseTime(formData.applyTime) }}</span>
                </el-form-item>
              </el-col>
            </el-row>

<!--            <el-row gutter="15" v-if="formData.classType===classTypeForeign">-->
<!--              <el-col :span="24">-->
<!--                <el-form-item :label="$t(`doc.this_dept_file_effective_date`)+`:`" prop="fileEffectiveDate">-->
<!--                  <span>{{ parseTime(formData.fileEffectiveDate) }}</span>-->
<!--                </el-form-item>-->
<!--              </el-col>-->

<!--              <el-col :span="24">-->
<!--                <el-form-item :label="$t(`doc.this_dept_revise_date`)+`:`" prop="revisionDate">-->
<!--                  <span>{{ parseTime(formData.revisionDate) }}</span>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--            </el-row>-->
            <template v-if="isSystemClauseShow">
              <el-row gutter="15">
                <el-col :span="12">
                  <el-form-item :label="$t(`doc.system_clause`)" prop="systemClause">
                    <dict-tag :options="dict.type.system_clause_list" :value="formData.systemClause"/>
<!--                    <span>{{formData.systemClause}}</span>-->
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`file_set.type_temp`)+`:`">
                  <div class="link-box bzlink-box">
                    <span
                      v-if="mobanwenjian != ''"
                      style="color: #385bb4; cursor: pointer"
                      @click="handlePreview(mobanwenjian[0].id)"
                      >{{ mobanwenjian[0].fileName }}</span
                    >
                    <span
                      v-if="mobanwenjian != ''"
                      style="color: #385bb4; cursor: pointer; margin-left: 10px"
                      @click="
                        handelefileLocalDownload(
                          mobanwenjian[0].id,
                          mobanwenjian[0].fileName
                        )
                      "
                      >{{ $t(`doc.this_dept_download`) }}</span
                    >
                  </div>
                </el-form-item>
              </el-col>
                <el-col :span="24">
                <el-form-item :label="$t(`doc.this_document_review_date`)" prop="reviewTime">
                  <span>{{ parseTime(formData.reviewTime, "{y}-{m}-{d}") }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_current_ver`)+`:`">
                  <div class="link-box bzlink-box">
                    <span
                      v-if="isEmpty(formData.preStandardDoc)"
                      style="color: #385bb4; cursor: pointer"
                      @click="handlePreview(formData.preStandardDoc.fileId)"
                      >{{ formData.preStandardDoc.fileName }}</span>
                    <span
                      v-show="(formData.classType!==classTypeRecord&&checkPermi(['doc:file:download']))||(formData.classType===classTypeRecord&&checkPermi(['record:file:download']))"
                      v-if="isEmpty(formData.preStandardDoc)"
                      style="color: #385bb4; cursor: pointer; margin-left: 10px"
                      @click="
                        handelefileLocalDownload(
                          formData.preStandardDoc.fileId,
                          formData.preStandardDoc.fileName
                        )
                      "
                      >{{ $t(`doc.this_dept_download`) }}</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_annexes_ver`)+`:`" prop="">
                  <div
                    v-for="(item, i) in formData.preAppendixes"
                    class="link-box bzlink-box"
                    :key="i"
                  >
                    <span
                      style="color: #385bb4; margin-left: 10px; cursor: pointer"
                      :key="i"
                      @click="handlePreview(item.fileId)"
                      >{{ item.fileName }}
                    </span>
                    <span
                      :key="i"
                      v-show="(formData.classType!==classTypeRecord&&checkPermi(['doc:file:download']))||(formData.classType===classTypeRecord&&checkPermi(['record:file:download']))"
                      style="color: #385bb4; cursor: pointer; margin-left: 10px"
                      @click="
                        handelefileLocalDownload(item.fileId, item.fileName)
                      "
                      >{{ $t(`doc.this_dept_download`) }}</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_effective_date`)+`:`" prop="startDate">
                  <span>{{parseTime(formData.startDate,"{y}-{m}-{d}")}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_release_date`)+`:`" prop="releaseTime">
                  <span>{{parseTime(formData.releaseTime,"{y}-{m}-{d}")}}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15" v-if="status!=='1'">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_cancel_date`)+`:`" prop="endDate">
                  <span>{{formData.endDate}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label=" " prop="">
                </el-form-item>
              </el-col>
            </el-row>

            <template v-if="projectCodeShow">
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_project_code`)" prop="projectCode">
<!--                    <span>{{formData.projectCode}}</span>-->
                    <div class="project-code-tags">
                      <dict-tag :options="dict.type.project_code_list" :value="formData.projectCode"/>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>

            <template v-if="projectNameSecurityKeywordByteShow">
              <el-row gutter="15">
                <el-col :span="12">
                  <el-form-item :label="$t(`doc.this_dept_project_name`)" prop="projectName">
                    <span>{{formData.projectName}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t(`doc.this_dept_security_class`)" prop="securityClass">
                    <span> <dict-tag :options="dict.type.security_class_list" :value="formData.securityClass"/></span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row gutter="15">
                <el-col :span="12">
                  <el-form-item :label="$t(`doc.this_dept_keyword`)" prop="keyword">
                    <span>{{formData.keyword}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t(`doc.this_dept_doc_bytes`)" prop="docBytes">
                    <span>{{formData.docBytes}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>

            <template v-if="isCustomerShow">
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_client_code`)+`:`" prop="customerCode">
                    <el-input
                      v-if="editStatus"
                      v-model="formData.customerCode"
                      :placeholder="$t(`doc.this_dept_client_code_select`)"
                      clearable
                      :style="{ width: '100%' }"
                      maxlength="50"
                      show-word-limit
                    ></el-input>
                    <span v-else>{{formData.customerCode}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>

            <template v-if="isShowPart">
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_product_codes`)" prop="productTableData">
                    <el-table
                      :data="productTableData"
                      border
                      style="width: 100%"
                      :show-header="true"
                    >
                      <el-table-column
                        :label="$t(`doc.this_dept_product_codes`)"
                        prop="materialCode"
                      >
                        <template slot-scope="scope">
                          <span>{{ scope.row.materialCode || '-' }}</span>
                        </template>
                      </el-table-column>

                      <el-table-column
                        v-if="isShowProductVersion"
                        :label="$t(`doc.this_dept_product_version`)"
                        prop="productVersion"
                      >
                        <template slot-scope="scope">
                          <span>{{ scope.row.productVersion || '-' }}</span>
                        </template>
                      </el-table-column>

                      <el-table-column
                        :label="$t(`doc.this_dept_product_summarys`)"
                        prop="materialDescription"
                      >
                        <template slot-scope="scope">
                          <span>{{ scope.row.materialDescription || '-' }}</span>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_factory`)+`:`" prop="factorys">
                    <template v-if="editStatus">
                      <el-select
                        :placeholder="$t(`doc.this_dept_factory_select`)"
                        v-model="formData.factorys"
                      >
                        <el-option
                          v-for="dict in dict.type.tenant_list"
                          :key="dict.value"
                          :label="dictLanguage(dict)"
                          :value="dict.value"
                        >
                        </el-option>
                      </el-select>
                    </template>
                    <template v-else>
                      <span>{{ getDictLabel(formData.factorys) }}</span>
                    </template>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>

            <template v-if="isDeviceShow">
              <el-row gutter="15">
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_unit_code`)+`:`" prop="deviceCode">
                    <el-input
                      v-if="editStatus"
                      v-model="formData.deviceCode"
                      :placeholder="$t(`doc.this_dept_unit_code_select`)"
                      clearable
                      :style="{ width: '100%' }"
                      maxlength="50"
                      show-word-limit
                    ></el-input>
                    <span v-else>{{formData.deviceCode}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item :label="$t(`doc.this_dept_unit_name`)+`:`" prop="deviceName">
                    <el-input
                      v-if="editStatus"
                      v-model="formData.deviceName"
                      :placeholder="$t(`doc.this_dept_unit_name_select`)"
                      clearable
                      :style="{ width: '100%' }"
                      maxlength="100"
                      show-word-limit
                    ></el-input>
                    <span v-else>{{formData.deviceName}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>


            <el-row v-if="isShelfLifeShow">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_shelf_life`)+`:`" prop="shelfLife">
                  <el-date-picker
                    :disabled="!editStatus"
                    v-model="formData.shelfLife"
                    type="date"
                    value-format="yyyy-MM-dd"
                    :placeholder="$t(`doc.this_dept_select_year`)">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_document_review_date`)+`:`" prop="custodyDeptId">
                  <span>{{parseTime(formData.reviewTime, "{y}-{m}-{d}")}}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15" v-if="formData.classType===classTypeForeign&&complianceShow">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_compliancy`)+`:`" prop="compliance">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    resize="none"
                    v-model="formData.compliance"
                    type="textarea"
                    placeholder="请输入合规性"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                    maxlength="1000"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15" v-if="filePurposeShow">
              <el-col :span="12">
                <el-form-item :label="$t('dict.file_purpose_type') + ':'" prop="filePurpose">
                  <div class="project-code-tags">
                    <dict-tag :options="dict.type.file_purpose_type" :value="formData.filePurpose"/>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15"  v-if="programVersionId">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_by_superior`) + `:`" prop="programDocId">
                  <span>{{formData.programDocName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_superior_file_name`)" prop="programDocId">
                  <span>{{formData.programDocId}}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 法规/标准相关字段 -->
            <el-row gutter="15" v-if="regulationStatusShow || regulationPublishDateShow">
              <el-col :span="12" v-if="regulationStatusShow">
                <el-form-item :label="$t('dict.regulation_standard_status') + ':'" prop="regulationStandardStatus">
                  <div class="project-code-tags">
                    <dict-tag :options="dict.type.regulation_standard_status" :value="formData.regulationStandardStatus"/>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="regulationPublishDateShow">
                <el-form-item :label="$t('field.regulation_publish_date') + ':'" prop="regulationPublishDate">
                  <span>{{formData.regulationPublishDate}}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15" v-if="regulationImplementDateShow">
              <el-col :span="12">
                <el-form-item :label="$t('field.regulation_implement_date') + ':'" prop="regulationImplementDate">
                  <span>{{formData.regulationImplementDate}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <!-- 空列，保持布局对齐 -->
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_change_reason`)+`:`" prop="changeReason">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    resize="none"
                    v-model.trim="formData.changeReason"
                    type="textarea"
                    :placeholder="$t(`doc.this_dept_insert_change_reason`)"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                    maxlength="500"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_changes`)+`:`" prop="content">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    resize="none"
                    v-model.trim="formData.content"
                    type="textarea"
                    :placeholder="$t(`doc.this_dept_insert_change_content`)"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                    maxlength="500"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.review_record`)+`:`" prop="additionalFileList" v-if="formData.fileList">
                  <fileUpload
                    :editStatus="false"
                    v-model.trim="formData.fileList"
                    :isShowTip="false"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`sys_mgr.user_remark`)+`:`" prop="remark">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    resize="none"
                    v-model="formData.remark"
                    type="textarea"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_train_record`)+`:`" prop="remark">
                  <fileUpload
                    v-model.trim="trains"
                    :editStatus="userInfo.userName===formData.userName&&status==='1'"
                    limit="100"
                    :fileType="['docx', 'doc','xls','xlsx','pdf','ppt','pptx','bmp','jpg','png','svg','tif','gif']"
                    :isShowTip="false"
                    @input="(list)=>handelConfirm(list,'train')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="$t(`doc.customer_record`)+`:`" prop="customerRecord">
                  <fileUpload
                    v-model.trim="customerRecordList"
                    :editStatus="false"
                    limit="100"
                    :fileType="['docx', 'doc','xls','xlsx','pdf','ppt','pptx','bmp','jpg','png','svg','tif','gif']"
                    :isShowTip="false"
                    @input="(list)=>handelConfirm(list,'customer')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>

      <distribute-view-box ref="distributeViewBox"></distribute-view-box>

      <div class="el-card news-card is-always-shadow">
        <div class="el-card__body">
          <el-tabs
            v-model.trim="activeIndex"
            class="news-tabs"
          >
            <el-tab-pane name="1" v-if="formData.classType===classTypeNote&&classTypeRecordMN">
              <span slot="label">{{ $t(`doc.this_dept_master_file`) }}</span>
              <link-doc
                :editStatus="false"
                :status="false"
                v-show="activeIndex  == '1'"
                :dataList="formData.docLinks"
                ref="linkDoc"
                :dataType="formData.dataType"
              ></link-doc>
            </el-tab-pane>
            <el-tab-pane name="1" v-if="formData.classType===classTypeDoc&&classTypeRecordMN">
              <span slot="label">{{ $t(`doc.this_dept_related_record`) }}</span>
              <link-note
                :editStatus="false"
                :status="false"
                v-show="activeIndex  == '1'"
                :dataList="formData.noteLinks"
                ref="linkNote"
                :dataType="formData.dataType"
              ></link-note>
            </el-tab-pane>
            <el-tab-pane name="1" v-if="formData.classType===classTypeDoc&&!classTypeRecordMN">
              <span slot="label">{{ $t(`doc.this_dept_related_record`) }}</span>
              <link-record
                ref="linkRecord"
                :status="false"
                :editStatus="false"
                v-show="activeIndex == '1'"
                :dataList="formData.recordLinks"
                :dataType="formData.dataType"
              ></link-record>
            </el-tab-pane>
            <el-tab-pane name="2"  v-if="formData.classType===classTypeDoc">
              <span slot="label">{{ $t(`doc.this_dept_related_file`) }}</span>
              <link-file
                :editStatus="false"
                v-show="activeIndex == '2'"
                ref="linkFile"
                :dataList="formData.fileLinks"
                :dataType="formData.dataType"
              ></link-file>
            </el-tab-pane>
            <el-tab-pane name="3">
              <span slot="label">{{ $t(`doc.this_dept_file_history`) }}</span>
              <el-card class="gray-card">
                <historicalVersion
                  :detatailsData="formData"
                  @handleClick="handleDeal"
                  v-show="activeIndex == '3'"
                ></historicalVersion
              ></el-card>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
    <div v-show="activeName==='log'">
      <workflow-logs :procInstId = "formData.procInstId"></workflow-logs>
    </div>
    <monitor-drawer
      v-if="monitorDrawerVisible"
      ref="monitorDrawer"
    ></monitor-drawer>
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="closeAS"></as-pre-view>
    <DealDrawer v-if="dealDrawerShow" ref="dealDrawer" @closeDrawer="handleCloseChange"></DealDrawer>
  </div>
</template>
<script>
import historicalVersion from "../add_import/historicalVersion";
import { settingDocClassList } from "@/api/file_settings/type_settings";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import processcode from "@views/workflowList/processcode/index.vue";
import WorkflowLogs from "@views/workflowList/workflowLogs/index.vue";
import {
  linkLoglistlink,
} from "@/api/file_processing/modifiyApply";
import { settingDocClassId } from "@/api/file_settings/type_settings";
import {standardGetDetail} from "@/api/document_account/standard";
import mixin from "@/layout/mixin/Commmon.js";
// PDF本地文件预览
import { updateModifyApplyTrainList } from '@/api/my_business/modifyApplyTrain'
import LinkFile from '@views/workflowList/addWorkflow/add_import/linkFile.vue'
import LinkRecord from '@views/workflowList/addWorkflow/add_import/linkRecord.vue'
import DealDrawer from '@/components/DealDrawer/index.vue'
import { checkPermi } from '@/utils/permission'
import { getInfoBy } from '@/api/setting/docClassSetting'
import LinkNote from '@views/workflowList/addWorkflow/add_import/linkNote.vue'
import LinkDoc from '@views/workflowList/addWorkflow/add_import/linkDoc.vue'
import DistributeViewBox from '../add_import/distributeViewBox.vue'

export default {
  components: {
    DistributeViewBox,
    LinkDoc,
    LinkNote,
    DealDrawer,
    LinkRecord,
    LinkFile,
    historicalVersion,
    Treeselect,
    processcode,
    WorkflowLogs
  },
  dicts:['sys_yes_no','doc_product_line','doc_procedure','doc_product_type','system_clause_list','doc_security_level','tenant_list','security_class_list',"project_code_list",'file_purpose_type','regulation_standard_status'],
  name: "Detail",
  props: ["dataType",'data'],
  mixins: [mixin],
  data() {
    return {
      classTypeRecordMN: true,
      classTypeRecord: 'RECORD',
      classTypeDoc: 'DOC',
      classTypeForeign: 'FOREIGN',
      classTypeNote: 'NOTE',
      dealDrawerShow: false,
      shlkPath:process.env.VUE_APP_SHLK_PATH,
      status: undefined,
      trains:[],
      customerRecordList: [],
      pButton: undefined,
      activeName: "info",
      viewId: "",
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      viewShow: false,
      active: 4,
      activeIndex: "1",
      uploadType: ["doc", "docx", "ppt", "xlsx", "pdf", "jpg", "png"],
      monitorDrawerVisible:false,
      formData: {
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "01",
        docId: undefined,
        deptId: undefined,
        deptName: undefined,
        userName: undefined,
        nickName: undefined,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: undefined,
        appendixes: undefined, //附件
        standardDoc: {fileName:''}, //编制文件
        fileLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        docLinks: undefined,
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined,
        partRemark: null,
        factorys: null,
        customerCode: null,
        deviceCode: null,
        deviceName: null,
        custodyDeptName: undefined,
        systemClause: null
      },
      partNumberArr: [""],
      // 产品表格数据
      productTableData: [
        {
          materialCode: '',
          productVersion: '',
          materialDescription: '',
        }
      ],
      isProject:false,
      classLevelOptions: [],
      editStatus:false,
      disabled: false,
      mobanwenjian: [],
      loading: false,
      detailLoading: false,
      isShowPart: false,
      isSystemClauseShow: false,
      isCustomerShow: false,
      projectCodeShow: false,
      internalDocIdShow: false,
      ecnCodeShow: false,
      projectNameSecurityKeywordByteShow: false,
      complianceShow: false,
      filePurposeShow: false,
      regulationStatusShow: false,
      regulationPublishDateShow: false,
      regulationImplementDateShow: false,
      programVersionId:false,
      isDeviceShow: false,
      isShelfLifeShow: false,
      isShowProductVersion: false,
    };
  },
  computed: {},
  watch: {
    "formData.docClass"(val) {
      let _this = this
      settingDocClassId(val).then((response) => {
        if (response.data&&response.data.fileList != null) {
          _this.mobanwenjian = response.data.fileList;
        }else {
          _this.mobanwenjian = []
        }
      });
    },
    "formData.dataType"(val) {
      let _this = this
      _this.isProject = val==='project'
    },
    data (val) {
      if (val) {
        this.init(val)
      }
    },
  },
  async created() {
    let response = await this.getConfigKey("record.doc.type")
    this.classTypeRecordMN = response.msg === undefined ? true : response.msg === 'true';
  },
  mounted() {
    if(this.$route.query.docId &&
      this.$route.query.versionId
    ){
      let param = {
        docId: this.$route.query.docId,
        versionId: this.$route.query.versionId
      }
      this.init(param)
    }


    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    checkPermi,
    init(row){
      let _this = this
      _this.rest()
      _this.getSettingDocClassTreeseList();
      _this.status = row.status
      _this.getDetail(row)
      _this.$nextTick(()=>{
        _this.$refs.distributeViewBox.init(row.versionId)
      })
    },
    getDetail(query) {
       let _this = this
      _this.detailLoading = true
      standardGetDetail(query).then(async (res) => {
        let formData = res.data;
        //关联文件
        let res1 = await linkLoglistlink({linkType: "REF_DOC", versionId: formData.versionId})
        formData.fileLinks = res1.data
        if (_this.classTypeRecordMN){
          //关联记录
          if (formData.classType===_this.classTypeDoc) {
            let res3 = await linkLoglistlink({linkType: "NOTE", versionId: formData.versionId})
            if (_this.status==="1") {
              formData.noteLinks = res3.data.filter(item=>item.status===1)
            }else {
              formData.noteLinks = res3.data
            }
          }
          if (formData.classType===_this.classTypeNote) {
            let res3 = await linkLoglistlink({linkType: "NOTE_DOC", versionId: formData.versionId})
            if (_this.status==="1") {
              formData.docLinks = res3.data.filter(item=>item.status===1)
            }else {
              formData.docLinks = res3.data
            }
          }
        }else {
          //关联记录
          let res2 = await linkLoglistlink({linkType: "RECORD", versionId: formData.versionId})
          if (_this.status==="1") {
            formData.recordLinks = res2.data.filter(item=>item.status===1)
          }else {
            formData.recordLinks = res2.data
          }
        }


        formData.type = _this.formData.type
        formData.changeType = _this.pButton;
        if(formData.classType!==_this.classTypeDoc&&formData.classType!==_this.classTypeNote){
          _this.activeIndex = '3'
        }
        formData.projectCode=formData.projectCode ? formData.projectCode.split(',') : []
        _this.formData = formData
        let trains=[]
        let customerRecordList=[]
        if (formData.trains&&formData.trains.length>0) {
          formData.trains.forEach(item=>{
            if (item.type==='train') {
              trains.push({
                url: item.fileIds,
                name: item.files[0].fileName
              })
            }else if (item.type==='customer') {
              customerRecordList.push({
                url: item.fileIds,
                name: item.files[0].fileName
              })
            }
          })
        }
        _this.trains = trains
        _this.customerRecordList = customerRecordList
        this.getByDocClass(formData.docClass)
        this.restoreProductTableData()
      }).finally(()=>{
        _this.detailLoading = false
      });
    },
    handleCloseChange(){
      this.dealDrawerShow = false
    },
    handleDeal(row) {
      this.dealDrawerShow = true;
      let url = this.shlkPath+'/#/workflow?type='+row.invokeType+'&preChangeCode='+row.invokeId
      this.$nextTick(() => {
        this.$refs.dealDrawer.init(url);
      });
    },
    rest(){
      let _this = this
      _this.activeName = "info"
      _this.formData= {
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "01",
        docId: undefined,
        deptId: _this.userInfo.dept.deptId,
        deptName: _this.userInfo.dept.deptName,
        userName: _this.userInfo.userName,
        nickName: _this.userInfo.nickName,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: new Date().getTime(),
        appendixes: undefined, //附件
        standardDoc: {fileName:''}, //编制文件
        fileLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined
      }
      // 初始化产品表格数据
      _this.productTableData = [{
        materialCode: '',
        productVersion: '',
        materialDescription: ''
      }]
    },
    closeAS() {
      this.viewShow = false;
    },
    close() {
      this.$emit("close")
    },
    handlePreview(id) {
      this.viewId = id;
      this.$refs.viewRef.handleOpenView(id);
      this.viewShow = true;
    },
    getSettingDocClassTreeseList() {
      settingDocClassList({classStatus: "1", dataType:this.dataType }).then(
        (response) => {
          this.classLevelOptions = [];
          //console.log(response.rows);
          response.rows.forEach((element, index) => {
            response.rows[index].children = [];
          });
          //console.log(response.rows);
          this.classLevelOptions = this.handleTree(
            response.rows,
            "id",
            "parentClassId"
          );
        }
      );
    },
    handelConfirm(list,type) {
        let trains = []
        list.forEach(item=>{
          trains.push({
            fileIds: item.url,
            fileName: item.name,
            userName: this.userInfo.userName,
            deptId: this.userInfo.deptId,
            docId: this.formData.docId,
            versionId: this.formData.versionId,
            type:type
          })
        })
        let data = {
          versionId: this.formData.versionId,
          type:type,
          trains: trains
        }
        updateModifyApplyTrainList(data);
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    normalizerDept(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    handleMonitor() {
      this.monitorDrawerVisible = true;
      this.$nextTick(() => {
        this.$refs.monitorDrawer.init(this.formData.procInstId);
      });
    },
    getByDocClass(docClass) {
      let _this = this
      _this.loading = true
      getInfoBy({type:'formShow',docClass:docClass}).then(async res => {
        this.isShowPart = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.isShowPart = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      _this.loading = true
      getInfoBy({type:'formCustomerShow',docClass:docClass}).then(async res => {
        _this.isCustomerShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            _this.isCustomerShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'shelfLifeShow',docClass:docClass}).then(async res => {
        this.isShelfLifeShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.isShelfLifeShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })


      _this.loading = true
      getInfoBy({type:'formDeviceShow',docClass:docClass}).then(async res => {
        _this.isDeviceShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            _this.isDeviceShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'formProductVersionShow',docClass:docClass}).then(async res => {
        this.isShowProductVersion = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.isShowProductVersion = true
          }
        }
        _this.loading = false
      }).catch(err => {
        _this.loading = false
      })

      getInfoBy({type:'systemClauseShow',docClass:docClass}).then(async res => {
        this.isSystemClauseShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.isSystemClauseShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'projectCodeShow',docClass:docClass}).then(async res => {
        this.projectCodeShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.projectCodeShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'internalDocIdShow',docClass:docClass}).then(async res => {
        this.internalDocIdShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.internalDocIdShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'ecnCodeShow',docClass:docClass}).then(async res => {
        this.ecnCodeShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.ecnCodeShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'projectNameSecurityKeywordByteShow',docClass:docClass}).then(async res => {
        this.projectNameSecurityKeywordByteShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.projectNameSecurityKeywordByteShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'complianceShow',docClass:docClass}).then(async res => {
        this.complianceShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.complianceShow = true
          }
        }
        _this.detailLoading = false
      }).catch(err => {
        console.log(err)
        _this.detailLoading = false
      })

      // 获取文件用途字段显示控制
      getInfoBy({type:'filePurposeShow',docClass:docClass}).then(async res => {
        this.filePurposeShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.filePurposeShow = true
          }
        }
      }).catch(err => {
        console.log(err)
        // 默认显示文件用途字段
        this.filePurposeShow = true
      })

      // 获取法规标准状态字段显示控制
      getInfoBy({type:'regulationStatusShow',docClass:docClass}).then(async res => {
        this.regulationStatusShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.regulationStatusShow = true
          }
        }
      }).catch(err => {
        console.log(err)
        this.regulationStatusShow = false
      })

      // 获取法规/标准发布日期字段显示控制
      getInfoBy({type:'regulationPublishDateShow',docClass:docClass}).then(async res => {
        this.regulationPublishDateShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.regulationPublishDateShow = true
          }
        }
      }).catch(err => {
        console.log(err)
        this.regulationPublishDateShow = false
      })

      // 获取法规/标准实施日期字段显示控制
      getInfoBy({type:'regulationImplementDateShow',docClass:docClass}).then(async res => {
        this.regulationImplementDateShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.regulationImplementDateShow = true
          }
        }
      }).catch(err => {
        console.log(err)
        this.regulationImplementDateShow = false
      })

      // 获取归属上级文件字段显示控制
      getInfoBy({type:'programVersionId',docClass:docClass}).then(async res => {
        this.programVersionId = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            this.programVersionId = true
          }
        }
      }).catch(err => {
        console.log(err)
        this.programVersionId = false
      })
    },
    getDictLabel(dictValue) {
      const dictItem = this.dict.type.tenant_list.find(item => item.value === dictValue);
      return dictItem ? this.dictLanguage(dictItem) : dictValue;
    },
    // 从保存的数据恢复表格数据
    restoreProductTableData() {
      this.restoreFromLegacyData();
    },
    // 从原有数据格式恢复
    restoreFromLegacyData() {
      if (this.formData.partNumber) {
        const partNumbers = this.formData.partNumber.split(';');
        const descriptions = this.formData.partRemark ? this.formData.partRemark.split(';') : [];
        const productVersions = this.formData.productVersion ? this.formData.productVersion.split(';') : [];

        this.productTableData = partNumbers.map((code, index) => ({
          materialCode: code || '',
          productVersion: productVersions[index] || '',
          materialDescription: descriptions[index] || ''
        }));

      } else {
        this.productTableData = [{
          materialCode: '',
          productVersion: '',
          materialDescription: ''
        }];
      }
    },
  },
};
</script>
<style scoped>
.document_change_add{
  .fujian .el-textarea__inner {
    border: 0 solid #dcdfe6;
    padding: 0;
  }
}


/* 项目代码标签样式优化 */
.project-code-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
}

.project-code-tags .el-tag {
  margin: 0;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.project-code-tags span {
  display: inline-block;
  margin: 0 8px 8px 0;
  padding: 4px 8px;
  background-color: #f0f2f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

</style>
