<template>
  <div class="document_change_add"  v-loading="loading||detailLoading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{ $t(`doc.this_dept_file_add`) }}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button v-if="pListData&&pListData.procInstId" @click="handleMonitor">{{ $t(`doc.this_dept_process_monitor`) }}</el-button>
        <el-button v-if="editStatus&&pListData&&pListData.procInstId" type="danger" @click="deleteForm" v-dbClick>{{ $t(`file_handle.change_revoke`) }}</el-button>
        <el-button v-if="pListData&&pListData.procInstId&&workflowStatus" @click="transferForm" v-dbClick>{{ transferStatus?$t(`file_handle.transfer_return`):$t(`file_handle.transfer`) }}</el-button>
        <el-button v-if="pListData&&pListData.procInstId&&!workflowStatus&&'doing'===formData.status&&formData.createBy===userInfo.userName" type="danger" @click="handleBackFlowToOne" v-dbClick>{{$t(`file_handle.change_withdraw`)}}</el-button>
        <el-button v-if="workflowStatus" type="primary" @click="submitForm" v-dbClick>{{ $t(`doc.this_dept_annex`) }}</el-button>
        <el-button v-if="editStatus" type="primary" @click="saveForm" v-dbClick>{{ $t(`doc.this_dept_save`) }}</el-button>
        <el-button @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <el-tabs v-if="pListData&&pListData.procInstId" v-model="activeName">
      <el-tab-pane :label="$t(`doc.this_dept_info_content`)" name="info"></el-tab-pane>
      <el-tab-pane :label="$t(`doc.this_dept_approval_records`)" name="log"></el-tab-pane>
    </el-tabs>
    <div class="dialog-body" v-show="activeName==='info'">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_appli_info`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_title`) + `:`" prop="applyTitle">
                  <sapn>{{formData.applyTitle}}</sapn>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_appli_dept`)" prop="deptId">
                  <sapn>{{formData.deptName}}</sapn>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_claimant`) + `:`" prop="userName">
                  <sapn>{{formData.nickName}}</sapn>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_appli_date`)" prop="applyTime">
                  <sapn>{{formData.applyTime}}</sapn>
                </el-form-item>
              </el-col>
            </el-row>
<!--            <el-row>-->
<!--              <el-col :span="24">-->
<!--                <el-form-item label="是否外发:" prop="isPuttingOut">-->
<!--                  <el-radio-group  v-if="editStatus" v-model.trim="formData.isPuttingOut">-->
<!--                    <el-radio-->
<!--                      v-for="(item, index) in dict.type.sys_yes_no"-->
<!--                      :key="index"-->
<!--                      :label="item.value"-->
<!--                    >{{ item.label }}</el-radio>-->
<!--                  </el-radio-group>-->
<!--                  <dict-tag v-else :options="dict.type.sys_yes_no" :value="formData.isPuttingOut"/>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--            </el-row>-->
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_appli_reason`)+`:`" prop="reason">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    v-model="formData.reason"
                    type="textarea"
                    resize="none"
                    :placeholder="$t(`doc.this_dept_insert_appli_reason`)"
                    :autosize="{minRows:4, maxRows: 10 }"
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_file_apply_file`) }}</div>
          <div class="cell-btn">
            <button
              type="button"
              @click="handleSelectFile()"
              class="el-button blue el-button--default"
              v-if="editStatus"
            >
              <span>{{ $t(`doc.this_dept_select_file`) }}</span>
            </button>
          </div>
        </div>
        <div class="el-card gray-card is-always-shadow">
          <div class="el-card__body">
            <el-table :data="formData.itemList">
              <el-table-column :label="$t(`doc.this_dept_file_type`)" align="left" prop="docClass" :formatter="formatterDocClass">
              </el-table-column>
              <el-table-column :label="$t(`doc.this_dept_file_name`)" align="left" prop="docName">
              </el-table-column>
              <el-table-column :label="$t(`doc.this_dept_file_code`)" align="left" prop="docId" />
              <el-table-column
                :label="$t(`doc.this_dept_file_versions2`)"
                align="left"
                prop="versionValue"
              />
              <el-table-column
                :label="$t(`doc.this_dept_disuse_train_obj`)"
                align="left"
                prop="deptName"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleWatch(scope.row)"
                  >{{ $t(`doc.this_dept_view`) }}</el-button>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t(`doc.this_dept_operation`)"
                align="left"
                class-name="small-padding fixed-width"
                v-if="editStatus"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleDelete(formData.itemList,scope.$index)"
                  >{{ $t(`doc.this_dept_delete`) }}</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <distribute-box
        :editStatus="editStatus"
        :workflowStatus="workflowStatus"
        :setDeptReceiver="attributeModel('set_dept_receiver')"
        :deptList="deptList"
        :deptOptions="deptOptions"
        :companyList="companyList"
        :distributeList="distributeList"
        :trainAlike = "true"
        ref="distributeBox"
      ></distribute-box>

      <div class="news-card" v-if="(attributeModel('shenhe')||attributeModel('pizhun'))&&workflowStatus&&!approvalStatus">
        <div class="card-head">
          <div class="cell-title">{{submitLabel}}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="validateForm"
            :model="formSubmit"
            :rules="rules"
            size="medium"
            label-position="right"
            label-width="200px"
          >
            <el-row gutter="15" >
              <el-col :span="24">
                <el-form-item :label="submitLabel+this.$t(`doc.this_dept_conclusion`)" prop="pass">
                  <el-radio-group v-model.trim="formSubmit.pass"  @input="commentItemSelect">
                    <el-radio
                      v-for="dict in passoptions"
                      :key="dict.value"
                      :label="dict.value"
                    >{{ dict.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="submitLabel+$t(`doc.this_dept_comments`)+':'">
                  <el-input
                    v-model="formSubmit.summary"
                    type="textarea"
                    :placeholder="$t(`doc.this_dept_insert`)+submitLabel+$t(`doc.this_dept_comments`)"
                    maxlength="200"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
      <approval-box
        v-if="workflowStatus&&approvalStatus"
        ref="approvalBox" :submitLabel="submitLabel"
        :selected="attributeModel('default_selected')"
        :userListStatus="attributeModel('user_list')"
        :order = "order"
        :searchQuery="searchQuery"
        :pListData="pListData"
        :status="(attributeModel('shenhe')||attributeModel('pizhun'))"
      ></approval-box>
    </div>
    <div v-show="activeName==='log'">
      <workflow-logs :procInstId = "pListData.procInstId"></workflow-logs>
    </div>
    <!-- 流程选择下一环节及人员  开始 -->
    <el-dialog
      :title="$t(`doc.this_dept_select_next`)"
      v-if="dialogVisible" :visible.sync="dialogVisible"
      width="60%"
      append-to-body
      v-loading = flowStepLoading
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <processcode
        ref="prochild"
        :selected="attributeModel('default_selected')"
        :userListStatus="attributeModel('user_list')"
        :order = "order"
        :searchQuery="searchQuery"
        :pListData="pListData"
        :isSummary="isSummary"
      ></processcode>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
        <el-button type="primary" v-dbClick @click="handleWorkflowSubmit"
        >{{ $t(`doc.this_dept_annex`) }}</el-button
        >
      </span>
    </el-dialog>
    <!-- 流程选择下一环节及人员  结束 -->
    <monitor-drawer
      v-if="monitorDrawerVisible"
      ref="monitorDrawer"
    ></monitor-drawer>
    <!-- 文件预览组件 -->
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="close"></as-pre-view>
    <version-list
      style="overflow:auto;"
      :classTypeList="classTypeList"
      :versionData="formData.itemList"
      @selectHandle="versionSelectHandle"
      ref="versionList"
    ></version-list>
    <user-list ref="userList" @selectHandle="userSelectHandle"></user-list>
    <el-dialog :title="$t(`doc.this_dept_disuse_train_obj`)" :visible.sync="distributeShow" append-to-body width="80%">
      <distribute-view-box ref="distributeViewBox" style="height: 500px;overflow: auto"></distribute-view-box>
      <span slot="footer" class="dialog-footer">
        <el-button @click="distributeShow = false">{{ $t(`doc.this_dept_close`) }}</el-button>
      </span>
    </el-dialog>
    <transfer-flow ref="transferFlow" @close="close"></transfer-flow>
  </div>
</template>
<script>
import { settingDocClassList } from "@/api/file_settings/type_settings";
import processcode from "@/views/workflowList/processcode/index.vue";
import WorkflowLogs from "@views/workflowList/workflowLogs/index.vue";
import {
  workflowprocesskey,
  getStartActdef,
  getExtAttributeModel,
  procInstInfoAndStatus,
  backFlowToOne
} from '@/api/my_business/workflow'
// PDF本地文件预览
import VersionList from '@views/workflowList/addWorkflow/add_import/versionList.vue'
import {getWorkflowApplyLog, selectApplySerial} from '@/api/my_business/workflowApplyLog'
import UserList from '@views/workflowList/addWorkflow/add_import/userList.vue'
import {
  addExtraApply,
  addExtraApplyByBpmnId,
  listExtraApplyItem,
  updateExtraApply
} from '@/api/document_account/extraApply'
import DistributeBox from '@views/workflowList/addWorkflow/add_import/distributeBox.vue'
import { getCompanyList } from '@/api/system/user'
import { listDept } from '@/api/system/dept'
import { listModifyApplyDistribute } from '@/api/my_business/modifyApplyDistribute'
import DistributeViewBox from '@views/workflowList/addWorkflow/add_import/distributeViewBox.vue'
import TransferFlow from '@views/workflowList/addWorkflow/add_import/transferFlow.vue'
import { getRecordbyPorcInstId } from '../../../api/my_business/workflow'
import ApprovalBox from './add_import/approvalBox.vue'
export default {
  dicts: ['sys_yes_no'],
  components: {
    ApprovalBox,
    TransferFlow,
    DistributeViewBox,
    DistributeBox,
    UserList,
    VersionList,
    processcode,
    WorkflowLogs
  },
  name: "Extra_doc",
  props: ['data'],
  data() {
    return {
      classTypeList: undefined,
      approvalStatus: true,
      order: 0,
      transferStatus: false,
      pButton: 'extra',
      distributeShow: false,
      searchQuery: {},
      deptList: [],
      companyList: [],
      deptOptions: [],
      distributeList: [],
      docClassList: [],
      submitLabel:this.$t('doc.this_dept_annex'),
      passoptions: [
        { value: true, label: this.$t(`doc.this_dept_pass`) },
        { value: false, label: this.$t(`doc.this_dept_not_pass`) },
      ],
      formSubmit: { summary: "", actionType: "", pass: undefined },
      isSummary: false,
      activeName: "info",
      nodeDetail: [],
      procDefKey: undefined,
      viewId: "",
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      viewShow: false,
      monitorDrawerVisible:false,
      applyTitleSerial: "",
      formData: {
        id: undefined,
        applyTitle: undefined,
        deptId:undefined,
        userName: undefined,
        reason: undefined,
        status: undefined,
        applyTime: undefined,
        distributeType: undefined,
        trainType: undefined,
        isPuttingOut: undefined,
        isDistribute: 'N',
        isTrain: 'N',
        itemList:[]
      },
      rules: {
        isTrain: [
          { required: true, message: this.$t(`doc.this_dept_select_is_train`), trigger: "blur,change" },
        ],
        isDistribute: [
          { required: true, message: this.$t(`doc.this_dept_select_is_distribute`), trigger: "blur,change" },
        ],
        isPuttingOut: [
          { required: true, message: this.$t(`doc.this_dept_select_whether_outgoing`), trigger: "blur" },
        ],
        pass:[
          { required: true, message: this.$t(`doc.this_dept_pls_select`), trigger: "blur" },
        ],
        reason: [
          { required: true, message: this.$t(`doc.this_dept_insert_appli_reason`), trigger: "blur" },
        ],
      },
      kuozhanshuju: {},
      pListData: {},
      editStatus:false,
      workflowStatus: false,
      dialogVisible: false,
      loading: false,
      detailLoading: false,
      flowStepLoading : false,
      classTypeRecord: 'RECORD',
      classTypeDoc: 'DOC',
      classTypeForeign: 'FOREIGN',
    };
  },
  computed: {},
  watch: {
    data (val) {
      if (val) {
        this.init(val)
      }
    },
    "formData.itemList"(val) {
      let _this = this
      if(_this.editStatus){
        _this.updateGenerateApplyTitle()
      }
    }
  },
  mounted() {
    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    handleExport2() {
      this.$refs.pdfView.init()
    },
    async init(row) {
      let _this = this
      _this.rest()
      _this.loading = true
      _this.procDefKey = row.type+'_mh'
      _this.formData.type = row.type
      _this.classTypeList = row.classTypeList
      if (row.order) {
        _this.order = row.order
      }
      _this.getCompanyDataList()
      _this.getDeptList()
      if (row.preChangeCode) {
        let res = await getWorkflowApplyLog(row.preChangeCode)
        row.procInstId = res.data.procInstId
      }
      //是否编辑模式
      _this.$nextTick(() => {
        if (row && row.procInstId) {
          let procInstId = row.procInstId
          _this.workflowStatus = row.status == '1'
          _this.procInstInfoAndStatus(procInstId)
          _this.getDetail(procInstId)
        } else {
          _this.workflowStatus = true
          _this.editStatus = true
          _this.getSettingDocClassTreeseList();
          _this.getWorkflowprocesskey();
          _this.$refs.distributeBox.init(_this.formData,{trainType:'trainType',distributeType:'distributeType',isDistribute:'isDistribute',isTrain:'isTrain'})
        }
      });
    },
    getDetail(procInstId) {
      let _this = this
      _this.detailLoading = true
      addExtraApplyByBpmnId(procInstId).then(async (res) => {
        let formData = res.data;
        formData.type = _this.formData.type
        _this.formData = formData
        _this.getSettingDocClassTreeseList();
        _this.getItemList(formData.id)
        _this.getDistributeList(formData.id)
      }).finally(()=>{
        _this.detailLoading = false
      });
    },
    getItemList(applyId){
      listExtraApplyItem({applyId:applyId}).then(res =>{
        this.$set(this.formData,'itemList',res.data)
      })
    },
    getDistributeList(applyId){
      let _this = this
      listModifyApplyDistribute({applyId:applyId}).then(res => {
        _this.distributeList = res.data
        _this.$refs.distributeBox.init(_this.formData,{trainType:'trainType',distributeType:'distributeType',isDistribute:'isDistribute',isTrain:'isTrain'})
      })
    },
    rest(){
      let _this = this
      _this.activeName = "info"
      _this.formData= {
        id: undefined,
        applyTitle: undefined,
        deptId: this.userInfo.deptId,
        userName: this.userInfo.userName,
        deptName: this.userInfo.dept.deptName,
        nickName: this.userInfo.nickName,
        reason: undefined,
        status: undefined,
        applyTime: _this.parseTime(new Date()),
        dataTypeL:undefined,
        distributeType: '',
        trainType: '',
        isPuttingOut: undefined,
        isDistribute: 'N',
        isTrain: 'N',
        itemList: []
      }
    },
    procInstInfoAndStatus(procInstId){
      let _this = this
      procInstInfoAndStatus(procInstId).then((res) => {
        if (res) {
          _this.pListData = res
        }else {
          _this.pListData = {procInstId:procInstId}
        }
        _this.getExtAttributeModel()
      });
    },
    nodeShow(code){
      let _this = this
      if (_this.nodeDetail) {
        return !!_this.nodeDetail.find(node=>node.code===code)
      }else  {
        return  false
      }
    },
    async generateApplyTitle(){
      let _this = this
      await selectApplySerial("extra").then((res) => {
        _this.applyTitleSerial = res.data
        _this.formData.applyTitle = _this.formData.deptName + "-" + _this.formData.itemList.length + this.$t(`doc.this_file_set`) + "-" + _this.applyTitleSerial
      });
    },
    updateGenerateApplyTitle(){
      if(this.formData.applyTitle){
        let parts = this.formData.applyTitle.split('-');
        let deptNameDash = parts[0];
        let applyTitleSerialDash = parts[parts.length - 1];
        this.formData.applyTitle = deptNameDash + "-" + this.formData.itemList.length+ this.$t(`doc.this_file_set`) + "-" + applyTitleSerialDash
      }
    },
    getWorkflowprocesskey() {
      let _this = this
      _this.pListData = {}
      if (_this.procDefKey) {
        workflowprocesskey(_this.procDefKey).then((data) => {
          getStartActdef(data.data.procDefId).then((res) => {
            _this.pListData = res.data;
            this.getExtAttributeModel()
          });
        });
      }else {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
      }
    },
    getExtAttributeModel(){
      let _this = this
      let procDefId = _this.pListData.procDefId
      let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
      if (procDefId&&curActDefId) {
        getExtAttributeModel(
          procDefId,
          curActDefId
        ).then((res) => {
          let kuozhanshuju = {}
          res.data.forEach(item=>{
            kuozhanshuju[item.objKey] = item.objValue
          })
          _this.kuozhanshuju = kuozhanshuju;
        }).finally(()=>{
          _this.loading = false
          _this.initStatus()
        });
      }else {
        _this.kuozhanshuju = {}
        _this.loading = false
      }
    },
    async initStatus() {
      let _this = this
      _this.editStatus = _this.attributeModel('bianji') && _this.workflowStatus
      _this.submitLabel = _this.attributeModel('pizhun') ? _this.$t(`file_handle.change_approve`) : _this.attributeModel('shenhe') ? _this.$t(`file_handle.change_auditing`) : _this.$t('doc.this_dept_annex')
      _this.transferStatus = await _this.$refs.transferFlow.getTransferStatus(_this.pListData)
      if (_this.approvalStatus) {
        await _this.setPresetUserList()
        if (_this.$refs.approvalBox) {
          _this.$refs.approvalBox.init()
        }
      }
    },
    attributeModel(val){
      if (this.kuozhanshuju&&this.kuozhanshuju!=={}) {
        let obj = this.kuozhanshuju[val]
        return obj?obj==='true':false
      }else {
        return false
      }
    },

    close() {
      this.viewShow = false;
      this.$emit("close")
    },
    handlePreview(id) {
      this.viewId = id;
      this.$refs.viewRef.handleOpenView(id);
      this.viewShow = true;
    },
    //不需要验证必填的保存
    async saveForm() {
      let _this = this
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
        return;
      }
      if (!_this.formData.applyTitle) {
        await _this.generateApplyTitle();
      }
      _this.loading = true
      let formData = JSON.parse(JSON.stringify(_this.formData))
      formData.recordStatus = "draft";
      formData.editStatus = _this.editStatus
      formData.distributeList = _this.$refs.distributeBox.getDistributeList()
      let boxFormData = _this.$refs.distributeBox.formData
      let boxSetting = _this.$refs.distributeBox.setting
      for (let item in boxSetting) {
        formData[boxSetting[item]] = boxFormData[item]
      }
      if (formData.id) {
        updateExtraApply(formData).then((res) => {
          if (res.code===200) {
            _this.$message({
              message: _this.$t(`file_handle.change_save_succ`),//提示的信息
              type:'success',　　//类型是成功
              duration:1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
              onClose:()=>{
                _this.loading = false
              }
            });
          }
        });
      } else {
        let wf_receivers = [];
        wf_receivers.push({
          receiveUserId: _this.userInfo.userName,
          receiveUserOrgId: _this.userInfo.deptId
        });
        formData.bpmClientInputModel = {
          model: {
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_procTitle: formData.applyTitle,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_curActDefName: _this.pListData.actDefName,
            wf_curActDefId: _this.pListData.actDefId,
            wf_nextActDefName: _this.pListData.actDefName,
            wf_nextActDefId: _this.pListData.actDefId,
          },
          order: _this.order,
          type: formData.type,
          review: false,
        };
        formData.editStatus = _this.editStatus
        addExtraApply(formData).then((res) => {
          if (res.code===200) {
            _this.formData.id = res.data.businessKey;
            _this.procInstInfoAndStatus(res.data.procInstId)
            _this.$message({
              message: _this.$t(`file_handle.change_save_succ`),//提示的信息
              type:'success',　　//类型是成功
              duration:1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
              onClose:()=>{
                _this.loading = false
              }
            });
          }
        });
      }
    },
    transferForm(){
      let _this = this
      _this.$refs.transferFlow.init(_this.pListData,_this.formData.id,_this.formData.type,_this.order,_this.pButton)
    },
    // 提交
    async submitForm() {
      let _this = this
      // 首先页签调整为 信息内容
      _this.activeName = 'info'
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
        return;
      }
      let dialogVisible = true
      //审核
      if (_this.attributeModel('shenhe')||_this.attributeModel('pizhun')) {
        if (_this.approvalStatus) {
          _this.formSubmit = _this.$refs.approvalBox.formSubmit
        }
        if (_this.formSubmit.pass===undefined) {
          _this.$modal.msgError(_this.submitLabel+_this.$t(`file_handle.change_result_not_null`));
          return;
        }
        // 验证是否填写了审核意见
        if(!_this.formSubmit.pass&&_this.formSubmit.summary.trim() == '') {
          _this.$modal.msgError(_this.$t(`doc.this_dept_pls_fill`)+_this.submitLabel+_this.$t(`doc.this_dept_comments`));
          return;
        }
      }
      if (!!_this.$refs["elForm"]) {
        let valid = await _this.$refs["elForm"].validate()
        if (!valid) {
          dialogVisible = false
        }
      }
      // 验证分发和培训表单
      if (!!_this.$refs["distributeBox"]) {
        let distributeForm = _this.$refs["distributeBox"].$refs["elFormDistribute"]
        if (!!distributeForm) {
          let validateValid = await distributeForm.validate()
          if (!validateValid) {
            dialogVisible = false
          }
        }
      }
      if (!!_this.$refs["distributeBox"]) {
        let trainForm = _this.$refs["distributeBox"].$refs["elFormTrain"]
        if (!!trainForm) {
          let validateValid = await trainForm.validate()
          if (!validateValid) {
            dialogVisible = false
          }
        }
      }
      if (!!_this.$refs["distributeBox"]) {
        let valid=await _this.$refs["distributeBox"].validateForm()
        if(!valid){
          dialogVisible = false
          return
        }
      }
      if (_this.validate()){
        return
      }
      this.loading = false;
      if (!_this.approvalStatus) {
        await _this.setPresetUserList()
        this.dialogVisible = true;
      }else {
        _this.$confirm(_this.$t(`file_handle.submit_text`), _this.$t(`file_handle.change_tip`), {
          confirmButtonText: _this.$t(`file_handle.change_confirm`),
          cancelButtonText: _this.$t(`doc.this_dept_abolish`),
          type: "warning",
        })
          .then(() => {
            _this.handleWorkflowSubmit({})
          })
      }
    },
    async setPresetUserList(){
      let _this = this
      _this.searchQuery.isPuttingOut = _this.formData.isPuttingOut
      _this.searchQuery.pass = _this.formSubmit.pass
    },
    // 审批结论选择
    commentItemSelect(val) {
      let lang = this.$t(`doc.this_dept_pass`)
      if(val) {
        lang = this.$t(`doc.this_dept_pass`)
      } else {
        lang = this.$t(`doc.this_dept_not_pass`)
      }
      let summary = this.formSubmit.summary
      // 赋值审核意见 this.formSubmit.summary = lang +' '+summary
      this.formSubmit.summary = lang
    },
    validate(){
      // 验证是否填写了审核意见
      let _this = this
      let validate = false
      if(!_this.formData.itemList||_this.formData.itemList.length < 1) {
        _this.$modal.msgError(_this.$t(`doc.this_dept_select_file`));
        return true;
      }
      return false;
    },

    //提交表单和流程数据
    async handleWorkflowSubmit(invokeFrom) {
      let _this = this
      let wf_receivers = [];
      let wf_nextActDefId = null
      let wf_nextActDefName = null
      let prochild = _this.approvalStatus? _this.$refs.approvalBox:_this.$refs.prochild
      if(typeof(invokeFrom) == 'object'  ) {
        if (prochild.receiveUserList.length < 1 &&prochild.nextData.actDefType!=='endEvent') {
          if (_this.approvalStatus) {
            _this.$message.warning(_this.$t(`doc.this_dept_directory_select_user_alert`,[prochild.curOptionLabel]));
          }else {
            _this.$message.warning(_this.$t(`doc.this_dept_select_user_alert`));
          }
          return;
        }
        prochild.receiveUserList.forEach((element) => {
          wf_receivers.push({
            receiveUserId: element.id,
            receiveUserOrgId: element.parentId,
          });
        });
        wf_nextActDefId = prochild.nextData.actDefId;
        wf_nextActDefName = prochild.nextData.actDefName;
      } else if (typeof(invokeFrom) == 'string' && invokeFrom == 'publish') {
        // 来源于按钮【执行发布】
        wf_nextActDefId = 'end'
        wf_nextActDefName = '结束'
      }

      if (!_this.formData.applyTitle) {
        await _this.generateApplyTitle();
      }
      let formData = JSON.parse(JSON.stringify(_this.formData))

        // 显示加载中
        _this.flowStepLoading = true
        _this.detailLoading = true
        if (_this.pListData && _this.pListData.procInstId) {
          //流程执行参数
          formData.bpmClientInputModel = {
            model: {
              wf_procDefKey: _this.procDefKey,
              wf_procDefId: _this.pListData.procDefId,
              wf_procTitle: _this.formData.applyTitle,
              wf_curActInstId: _this.pListData.curActInstId,
              wf_sendUserId: _this.userInfo.userName,
              wf_sendUserOrgId: _this.userInfo.deptId,
              wf_receivers: wf_receivers,
              wf_nextActDefId: wf_nextActDefId,
              wf_curComment: _this.formSubmit.summary,
              wf_curActDefId: _this.pListData.curActDefId,
              wf_curActDefName: _this.pListData.curActDefName,
              wf_nextActDefName: wf_nextActDefName,
            },
            order: _this.pListData.actDefOrder,
            applyStatus: _this.formSubmit.pass,
            review: _this.attributeModel('shenhe')||_this.attributeModel('pizhun'),
            type: formData.type
          };
        }else{
          //创建流程参数
          formData.bpmClientInputModel = {
            type: formData.type,
            model: {
              wf_procTitle:  _this.formData.applyTitle,
              wf_nextActDefId: wf_nextActDefId,
              wf_procDefId: _this.pListData.procDefId,
              wf_procDefKey: _this.procDefKey,
              wf_sendUserId: _this.userInfo.userName,
              wf_sendUserOrgId: _this.userInfo.deptId,
              wf_receivers: wf_receivers,
              wf_curActDefName: _this.pListData.actDefName,
              wf_curActDefId: _this.pListData.actDefId,
              wf_nextActDefName: wf_nextActDefName,
            },
            order: _this.pListData.actDefOrder,
            review: _this.attributeModel('shenhe') || _this.attributeModel('pizhun'),
            applyStatus: _this.formSubmit.pass,
          };
        }
        if (_this.attributeModel('fabu')&&wf_nextActDefId==='end') {
          //办结
          formData.recordStatus = 'done'
        } else {
          //进行中
          formData.recordStatus = 'doing'
        }
        formData.editStatus = _this.editStatus
        formData.distributeList = _this.$refs.distributeBox.getDistributeList()
        let boxFormData = _this.$refs.distributeBox.formData
        let boxSetting = _this.$refs.distributeBox.setting
        for (let item in boxSetting) {
          formData[boxSetting[item]] = boxFormData[item]
        }
        addExtraApply(formData).then((res) => {
          if (res.code===200) {
            _this.$message({
              message: this.$t(`doc.this_dept_process_sub_succ`),//提示的信息
              type:'success',　　//类型是成功
              duration:1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
              onClose:()=>{
                _this.flowStepLoading = false
                _this.detailLoading = false
                _this.dialogVisible = false;
                _this.close();
              }
            });
          }
        });
    },
    handleSelect(source, index,deptId) {
        let _this = this
        _this.$nextTick(()=>{
          _this.$refs.userList.init(source,index,deptId)
        })
    },
    getSettingDocClassTreeseList() {
      let query = {classStatus: "1",classTypeList:this.classTypeList}
      settingDocClassList(query).then(res => {
          this.docClassList = JSON.parse(JSON.stringify(res.rows))
          this.docClassTree = this.handleTree(res.rows, "id", "parentClassId")
        }
      );
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    // 树形结构数据转化成列表结构数据
    deepTraversal(data) {
      const result = [];
      data.forEach((item) => {
        const loop = (data) => {
          result.push({
            deptId: data.deptId,
            deptName: data.deptName,
            notPrint: data.notPrint,
            nums: data.nums,
          });
          let child = data.children;
          if (child) {
            for (let i = 0; i < child.length; i++) {
              loop(child[i]);
            }
          }
        };
        loop(item);
      });
      return result;
    },
    handleMonitor() {
      this.monitorDrawerVisible = true;

      this.$nextTick(() => {
        this.$refs.monitorDrawer.init(this.pListData.procInstId);
      });
    },
    handleBackFlowToOne(){
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_withdraw_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value)=>{
          if(!value) {
            return _this.$t(`file_handle.change_fill_withdrawal_text`);
          }
        },
      }).then(({ value })=> {
        _this.loading = true
        getRecordbyPorcInstId(_this.pListData.procInstId).then(async res => {
          for (const item of res.data) {
            let bpmClientInputModel = {
              model: {
                wf_procInstId: _this.pListData.procInstId,
                wf_procDefKey: item.procDefKey,
                wf_procDefId: item.procDefId,
                wf_procTitle: _this.formData.applyTitle,
                wf_curActInstId: item.curActInstId,
                wf_sendUserId: item.recUserId,
                wf_sendUserOrgId: item.recOrgId,
                wf_curComment: value,
                wf_curActDefId: item.curActDefId,
                wf_curActDefName: item.curActDefName,
              },
              bizType: _this.pButton,
              review: 1,
              applyStatus: false,
              status: 'draft',
              type: _this.formData.type,
              order: 0,
            };
            //抢单模式多个待办只执行一个 第二个执行返回null跳出循环
            let res1 = await backFlowToOne(bpmClientInputModel)
            if (!res1.data) {
              break;
            }
          }
          _this.close(true);
        })
      })
    },
    deleteForm(){
      let _this = this
      _this.$prompt(_this.$t(`file_handle.change_revoke_text`), _this.$t(`file_handle.change_tip`), {
        inputType: 'textarea',
        confirmButtonText: _this.$t(`doc.this_dept_confirm`),
        cancelButtonText: _this.$t(`doc.this_dept_abolish`),
        closeOnClickModal: false,
        inputValidator: (value)=>{
          if(!value) {
            return _this.$t(`file_handle.change_fill_revocation_text`);
          }
        },
      }).then(({ value })=> {
          _this.loading = true
          let formData = {
            id: _this.formData.id,
            bpmClientInputModel: {
              model: {
                wf_procDefKey: _this.procDefKey,
                wf_procDefId: _this.pListData.procDefId,
                wf_procInstId: _this.pListData.procInstId,
                wf_sendUserId: _this.userInfo.userName,
                wf_sendUserOrgId: _this.userInfo.deptId,
                wf_curActDefName: _this.pListData.curActDefName,
                wf_curActDefId: _this.pListData.curActDefId,
                wf_curActInstId: _this.pListData.curActInstId,
                wf_curComment: value,
              },
              order: _this.order,
              type: _this.formData.type,
              review: true,
              applyStatus: false,
            },
            recordStatus: 'cancel',
            editStatus: false
          }
          addExtraApply(formData).then((res) => {
            if (res.code === 200) {
              this.$modal.msgSuccess(_this.$t(`file_handle.change_revoke_success`));
              this.close(true);
            }
          });
        })
    },
    handleSelectFile() {
      this.$nextTick(()=>{
        this.$refs.versionList.init(null,null,true)
      })
    },
    versionSelectHandle(source,index,data) {
      let _this = this
      if (data.length < 1) {
        this.$modal.msg("请选择文件！");
      } else {
        data.forEach(item=>{
          console.log(_this.formData.itemList)
          _this.formData.itemList.push({
            docName: item.docName,
            docId: item.docId,
            docClass: item.docClass,
            versionId: item.versionId,
            versionValue: item.versionValue,
            distributeType: item.distributeType,
            userName: item.userName,
            deptId: item.deptId,
            nickName: item.nickName,
            deptName: item.deptName
          })
        })
      }
    },
    userSelectHandle(source,index,user){
      this.formData[source][index].nickName = user.nickName
      this.formData[source][index].deptName = user.dept.deptName
      this.formData[source][index].userName = user.userName
      this.formData[source][index].deptId= user.deptId
    },
    handleWatch(row){
      let _this = this
      _this.distributeShow = true
      _this.$nextTick(()=>{
        _this.$refs.distributeViewBox.init(row.versionId)
      })
    },
    handleDelete(dataList,index){
      dataList.splice(index,1)
    },
    formatterDocClass(row, column, cellValue, index){
      let _this = this
      if (_this.docClassList) {
        let item = _this.docClassList.find(item=>item.id===cellValue)
        return item?item.className:cellValue
      }
      return cellValue
    },
    getCompanyDataList() {
      let _this = this
      getCompanyList({}).then(res=>{
        _this.companyList = res.data
      })
    },
    getDeptList(){
      // deptLevel = 2 只显示组织层级2级以内的节点
      listDept({ status: 0 }).then((response) => {
        this.deptList = JSON.parse(JSON.stringify(response.data))
        this.deptOptions = this.handleTree(response.data, "deptId");
      });
    },
  },
};
</script>
<style scoped>
.document_change_add{
  .fujian .el-textarea__inner {
    border: 0 solid #dcdfe6;
    padding: 0;
  }
}

</style>
