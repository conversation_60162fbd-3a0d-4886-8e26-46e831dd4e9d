<template>
  <div class="document_change_add"  v-loading="loading||detailLoading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{ $t(`doc.this_dept_file_change`) }}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button v-if="pListData&&pListData.procInstId" @click="handleMonitor">{{ $t(`doc.this_dept_process_monitor`) }}</el-button>
        <el-button v-if="(editStatus||workflowStatus)" type="primary" @click="submitForm" v-dbClick>{{ $t(`doc.this_dept_annex`) }}</el-button>
        <el-button v-if="editStatus" type="primary" @click="saveForm" v-dbClick>{{ $t(`doc.this_dept_save`) }}</el-button>
        <el-button @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <el-tabs v-if="pListData&&pListData.procInstId" v-model="activeName">
      <el-tab-pane :label="$t(`doc.this_dept_info_content`)" name="info"></el-tab-pane>
      <el-tab-pane :label="$t(`doc.this_dept_approval_records`)" name="log"></el-tab-pane>
    </el-tabs>
    <div class="dialog-body" v-show="activeName==='info'">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_base_msg`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_title`)+`:`" prop="applyTitle">
                  <el-input
                    v-if="editStatus"
                    v-model="formData.applyTitle"
                    :placeholder="$t(`doc.this_dept_insert_name`)"
                    maxlength="50"
                    clearable
                    style="width: 80%; margin-right: 10px"
                  >
                  </el-input>
                  <sapn v-else>{{formData.applyTitle}}</sapn>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label=" " prop="">
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_change_type`)+`:`" prop="changeType">
                  <template>
                    <el-radio-group :disabled="!editStatus" v-model.trim="formData.changeType" @change="onChangeType">
                      <el-radio label="ADD">{{ $t(`doc.this_dept_new_add`) }}</el-radio>
                      <el-radio label="UPDATE">{{ $t(`doc.this_dept_revision`) }}</el-radio>
                      <el-radio label="DISUSE">{{ $t(`doc.this_dept_cancel`) }}</el-radio>
                    </el-radio-group>
                  </template>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_file_type`) +`:`" prop="docClass">
                  <treeselect
                    v-model.trim="formData.docClass"
                    :disabled="formData.changeType !== 'ADD'||!editStatus"
                    :options="classLevelOptions"
                    :disable-branch-nodes="true"
                    :normalizer="normalizer"
                    :searchable="false"
                    :show-count="true"
                    :placeholder="$t(`doc.this_dept_select_type`)"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_names`)" prop="docName">
                  <div>
                    <el-input
                      v-model.trim="formData.docName"
                      v-if="editStatus"
                      :placeholder="$t(`doc.this_dept_insert_name`)"
                      :disabled="formData.changeType != 'ADD'"
                      maxlength="1000"
                      clearable
                      style="width: 100%"
                    >
                      <el-button
                        slot="append"
                        icon="el-icon-search"
                        @click="handleSelectFile"
                        v-if="formData.changeType != 'ADD' && formData.changeType"
                      ></el-button>
                    </el-input>
                    <span v-else>{{formData.docName}}</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_codes`)" prop="docId">
                  <span>{{ formData.docId }}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_versions`)" prop="versionValue">
                  <el-input
                    v-if="editStatus"
                    v-model.trim="formData.versionValue"
                    :placeholder="$t(`doc.this_dept_insert_ver`)"
                    clearable
                    :style="{ width: '100%' }"
                    maxlength="50"
                    show-word-limit
                  ></el-input>
                  <span v-else>{{formData.versionValue}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_staffing_depts`)" prop="standDeptName">
                  <span>{{ formData.standDeptName }}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_staff`)" prop="standUserName">
                  <div>
                    <el-input
                      v-if="editStatus"
                      style="width: 80%; margin-right: 10px"
                      readonly
                      v-model.trim="formData.standUserName"
                      :placeholder="$t(`doc.this_dept_select`)"
                    >
                      <el-button
                        slot="append"
                        icon="el-icon-search"
                        @click="handleSelect"
                      ></el-button>
                    </el-input>
                    <span v-else>{{ formData.standUserName }}</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_claimant`)+`:`" prop="field120">
                  <span>{{ formData.applyUserName }}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_current_effec_ver`)+`:`">
                  <div class="link-box">
                  <span
                    v-if="standardDocfileList&&standardDocfileList.length>0"
                    style="color: rgb(56, 91, 180); cursor: pointer"
                    @click="handlePreview(standardDocfileList[0].url)"
                  >{{ standardDocfileList[0].name }}</span
                  >
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_effec_ver`)+`:`" prop="preAppendixes">
                  <div
                    v-for="(item, i) in appendixsList"
                    class="link-box bzlink-box"
                    :key="i"
                  >
                    <span
                      style="color: #385bb4; margin-left: 10px; cursor: pointer"
                      :key="i"
                      @click="handlePreview(item.fileId)"
                    >{{ item.fileName }}
                    </span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_change_reason`)+`:`" prop="changeReason">
                  <el-input
                    v-model.trim="formData.changeReason"
                    v-if="editStatus"
                    type="textarea"
                    :placeholder="$t(`doc.this_dept_insert_change_reason`)"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                    maxlength="500"
                  ></el-input>
                  <span v-else>{{formData.changeReason}}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_changes`)+`:`" prop="content">
                  <el-input
                    v-if="editStatus"
                    v-model.trim="formData.content"
                    type="textarea"
                    :placeholder="$t(`doc.this_dept_insert_change_content`)"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                    maxlength="500"
                  ></el-input>
                  <span v-else>{{formData.content}}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>

      <div class="news-card" v-if="(attributeModel('shenhe')||attributeModel('pizhun'))&&workflowStatus">
        <div class="card-head">
          <div class="cell-title">{{submitLabel}}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="validateForm"
            :model="formSubmit"
            :rules="rules"
            size="medium"
            label-position="right"
            label-width="200px"
          >
            <el-row gutter="15" >
              <el-col :span="24">
                <el-form-item :label="submitLabel+$t(`file_handle.change_result`)+':'" prop="pass">
                  <el-radio-group v-model.trim="formSubmit.pass"  @input="commentItemSelect">
                    <el-radio
                      v-for="dict in passoptions"
                      :key="dict.value"
                      :label="dict.value"
                    >{{ dict.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="submitLabel+$t(`doc.this_dept_comments`)+':'">
                  <el-input
                    v-model="formSubmit.summary"
                    type="textarea"
                    :placeholder="$t(`doc.this_dept_insert`)+submitLabel+$t(`doc.this_dept_comments`)"
                    maxlength="200"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </div>
    <div v-show="activeName==='log'">
      <workflow-logs :procInstId = "pListData.procInstId"></workflow-logs>
    </div>
    <!-- 流程选择下一环节及人员  开始 -->
    <el-dialog
      :title="$t(`doc.this_dept_select_next`)"
      v-if="dialogVisible" :visible.sync="dialogVisible"
      width="60%"
      append-to-body
      v-loading = flowStepLoading
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <processcode
        ref="prochild"
        :pListData="pListData"
        :isSummary="isSummary"
      ></processcode>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
        <el-button type="primary" v-dbClick @click="handleWorkflowSubmit"
        >{{ $t(`doc.this_dept_annex`) }}</el-button
        >
      </span>
    </el-dialog>
    <!-- 流程选择下一环节及人员  结束 -->
    <monitor-drawer
      v-if="monitorDrawerVisible"
      ref="monitorDrawer"
    ></monitor-drawer>
    <!-- 文件预览组件 -->
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="close"></as-pre-view>
    <version-list
      style="overflow:auto;"
      :dataType="formData.dataType"
      :versionData="[formData]"
      ref="versionList"
      @selectHandle="versionSelectHandle"
    ></version-list>
    <user-list ref="userList" @selectHandle="userSelectHandle"></user-list>
  </div>
</template>
<script>
import { processFileLocalUpload } from "@/api/commmon/file";
import { settingDocClassList } from "@/api/file_settings/type_settings";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import processcode from "@/views/workflowList/processcode/index.vue";
import WorkflowLogs from "@views/workflowList/workflowLogs/index.vue";
import {
  getInfoByBpmnId
} from "@/api/file_processing/changeApply";
import {signEffective} from "@/api/file_processing/fileSignature";
import { settingDocClassId } from "@/api/file_settings/type_settings";
import { standardGetDetail} from "@/api/document_account/standard";
import { isExistByName } from "@/api/document_account/standard";
import {
  workflowprocesskey,
  getStartActdef,
  getExtAttributeModel,
  procInstInfoAndStatus, workflowbacktostart
} from '@/api/my_business/workflow'
import mixin from "@/layout/mixin/Commmon.js";
// PDF本地文件预览
import {fileLocalPdfView} from "@/api/pdf_preview/index";
import { queryUserProjectList } from '@/api/system/project'
import LinkFile from '@views/workflowList/addWorkflow/add_import/linkFile.vue'
import LinkRecord from '@views/workflowList/addWorkflow/add_import/linkRecord.vue'
import { listNoProcessDistributeLog } from '@/api/file_processing/distributeLog'
import VersionList from '@views/workflowList/addWorkflow/add_import/versionList.vue'
import { getWorkflowApplyLog, selectStatusByDocId } from '@/api/my_business/workflowApplyLog'
import UserList from '@views/workflowList/addWorkflow/add_import/userList.vue'
import { addChangeApply, updateChangeApply } from '@/api/file_processing/changeApply'
export default {
  dicts: ["business_status"],
  components: {
    UserList,
    VersionList,
    Treeselect,
    processcode,
    WorkflowLogs
  },
  name: "Add_doc",
  props: ["dataType",'data'],
  mixins: [mixin],
  data() {
    let validateFileUrl = (rule, value, callback) => {
      if (this.standardDocfileList.length < 1) {
        //我控制了FileList 长度代表文件个数
        callback(new Error(this.$t(`doc.this_dept_to_upload_file`)));
      } else {
        callback();
      }
    };
    let validateDocName = (rule, val, callback) => {
      if (this.editStatus&&this.formData.changeType==='Add') {
        if (this.formData.docName != "" && this.formData.docName != undefined) {
          isExistByName({docName:this.formData.docName,applyId:this.formData.id}).then((response) => {
            if (response.data == true) {
              return callback(new Error(this.$t(`doc.this_dept_file_name_exist`)));
            } else {
              callback();
            }
          });
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      open: false,
      appendixsList:[],
      remarkfileList: [],
      changeFactor:[],
      submitLabel:undefined,
      isProject: false,
      docIdData: {},
      jiluliData: [],
      shenchenbianhao: false,
      passoptions: [
        { value: "pass", label: this.$t(`doc.this_dept_pass`) },
        { value: "un_pass", label: this.$t(`doc.this_dept_not_pass`) },
      ],
      formSubmit: { summary: "", actionType: "", pass: "" },
      pButton: undefined,
      isSummary: false,
      projectList:[],
      project:{id:'',name:''},
      activeName: "info",
      nodeDetail: [],
      procDefKey: undefined,
      processData: {},
      viewId: "",
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      viewShow: false,
      active: 4,
      activeIndex: "1",
      uploadType: ["doc", "docx", "ppt", "xlsx", "pdf", "jpg", "png"],
      monitorDrawerVisible:false,
      formData: {
        id: undefined,
        applyTitle: undefined,
        docId: undefined,
        create_time: undefined,
        changeType: undefined,
        docName: undefined,
        deptId: undefined,
        applyUserName:undefined,
        userName: undefined,
        changeReason: undefined,
        content: undefined,
        remark: undefined,
        status: undefined,
        versionValue: undefined,
        versionId: undefined,
        editUserName: undefined,
        editDeptId: undefined,
        standUserName: undefined,
        standDeptName: undefined,
        appendixs: undefined,
        remarkDoc:undefined,
        changeFactor: undefined, //附件
      },
      rules: {
        applyTitle: [
          { required: true, message: this.$t(`doc.this_dept_insert_title`), trigger: "blur" },
        ],
        projectId: [
          { required: true, message: this.$t(`doc.this_dept_select_project`), trigger: "blur" },
        ],
        pass:[
          { required: true, message: this.$t(`doc.this_dept_pls_select`), trigger: "blur" },
        ],
        docClass: [
          { required: true, message: this.$t(`doc.this_dept_preparer`), trigger: "blur" },
        ],
        docName: [
          {
            required: true,
            message: this.$t(`doc.this_dept_file_name_not_null`),
            trigger: "blur,change",
          },
          {
            max: 1000,
            message: this.$t(`doc.this_dept_file_name_more_long`),
          },
          { validator: validateDocName, trigger: "blur" },
        ],
        versionValue: [
          { required: true, message: this.$t(`doc.this_dept_insert_ver`), trigger: "blur" },
        ],
        changeReason: [
          { required: true, message: this.$t(`doc.this_dept_insert_change_reason`), trigger: "blur,change" },
          {
            max: 500,
            message: this.$t(`doc.this_dept_change_reason_more500`),
          },
        ],
        content: [
          { required: true, message: this.$t(`doc.this_dept_insert_change_content`), trigger: "blur,change" },
          {
            max: 500,
            message: this.$t(`doc.this_dept_change_content_more500`),
          },
        ],
        standardDocfileList: [
          {
            required: true,
            validator: validateFileUrl,
            trigger: ["blur", "change", "input"],
          },
        ],
      },
      kuozhanshuju: {},
      field117Action: "",
      action: "/dms-admin/process/file/local_upload",
      appendixesfileList: [],
      standardDocfileList: [],
      menuitem: "1",
      classLevelOptions: [],
      summary: "",
      pListData: {},
      editStatus:false,
      workflowStatus: false,
      dialogVisible: false,
      processcodeData: {},
      processInstanceModel: {},
      disabled: false,
      mobanwenjian: [],
      loading: false,
      detailLoading: false,
      flowStepLoading : false
    };
  },
  computed: {},
  watch: {
    "formData.docClass"(val) {
      let _this = this
      if (val) {
        settingDocClassId(val).then((response) => {
          if (response.data&&response.data.fileList != null) {
            this.mobanwenjian = response.data.fileList;
          }else {
            this.mobanwenjian = []
          }
        });
      }
    },
    "formData.dataType"(val) {
      let _this = this
      _this.isProject = val==='project'
    },
    data (val) {
      if (val) {
        this.init(val)
      }
    },
  },
  mounted() {
    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    handleExport2() {
      this.$refs.pdfView.init()
    },
    async init(row) {
      let _this = this
      _this.rest()
      _this.loading = true
      _this.procDefKey = row.type
      _this.formData.type = row.type
      _this.formData.dataType = _this.dataType
      _this.getQueryUserProjectList()
      if (row.preChangeCode) {
        let res = await getWorkflowApplyLog(row.preChangeCode)
        row.procInstId = res.data.procInstId
      }
      //是否编辑模式
      _this.$nextTick(() => {
        if (row && row.procInstId) {
          let procInstId = row.procInstId
          _this.workflowStatus = row.status == '1'
          _this.procInstInfoAndStatus(procInstId)
          _this.getDetail(procInstId)
        } else {
          _this.workflowStatus = true
          _this.loading = false
          _this.editStatus = true
          _this.getSettingDocClassTreeseList();
          _this.getWorkflowprocesskey();
        }
      });
    },
    getDetail(procInstId) {
      let _this = this
      _this.detailLoading = true
      getInfoByBpmnId(procInstId).then(async (res) => {
        let formData = res.data;
        formData.type = _this.formData.type
        _this.formData = formData
        _this.getSettingDocClassTreeseList();
      }).finally(()=>{
        _this.detailLoading = false
      });
    },
    getQueryUserProjectList(){
      let _this = this
      queryUserProjectList().then(res=>{
        _this.projectList = res.data
      })
    },
    rest(){
      let _this = this
      _this.activeName = "info"
      _this.formData= {
        id: undefined,
        applyTitle: undefined,
        docId: undefined,
        create_time: new Date(),
        changeType: undefined,
        docName: undefined,
        deptId: this.userInfo.deptId,
        userName: this.userInfo.userName,
        applyUserName:this.userInfo.nickName,
        changeReason: undefined,
        content: undefined,
        remark: undefined,
        status: undefined,
        versionValue: "A0",
        versionId: undefined,
        editUserName: undefined,
        editDeptId: this.userInfo.deptId,
        standUserName: undefined,
        standDeptName: undefined,
        appendixs: undefined,
        remarkDoc:undefined,
        changeFactor: undefined, //附件
      }
    },
    procInstInfoAndStatus(procInstId){
      let _this = this
      procInstInfoAndStatus(procInstId).then((res) => {
        if (res) {
          _this.pListData = res
        }else {
          _this.pListData = {procInstId:procInstId}
        }
        _this.getExtAttributeModel()
      });
    },
    nodeShow(code){
      let _this = this
      if (_this.nodeDetail) {
        return !!_this.nodeDetail.find(node=>node.code===code)
      }else  {
        return  false
      }
    },
    getWorkflowprocesskey() {
      let _this = this
      _this.loading = true
      _this.pListData = {}
      if (_this.procDefKey) {
        workflowprocesskey(_this.procDefKey).then((data) => {
          getStartActdef(data.data.procDefId).then((res) => {
            _this.pListData = res.data;
            this.getExtAttributeModel()
          });
        });
      }else {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
      }
    },
    getExtAttributeModel(){
      let _this = this
      let procDefId = _this.pListData.procDefId
      let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
      if (procDefId&&curActDefId) {
        getExtAttributeModel(
          procDefId,
          curActDefId
        ).then((res) => {
          console.log("扩展属性====>", res);
          let kuozhanshuju = {}
          res.data.forEach(item=>{
            kuozhanshuju[item.objKey] = item.objValue
          })
          _this.kuozhanshuju = kuozhanshuju;
          _this.editStatus =_this.attributeModel('bianji')&&_this.workflowStatus
          _this.submitLabel = _this.attributeModel('pizhun')?_this.$t(`file_handle.change_approve`):_this.$t(`file_handle.change_auditing`)
        }).finally(()=>{
          _this.loading = false
        });
      }else {
        _this.kuozhanshuju = {}
        _this.loading = false
      }
    },
    attributeModel(val){
      if (this.kuozhanshuju&&this.kuozhanshuju!=={}) {
        let obj = this.kuozhanshuju[val]
        return obj?obj==='true':false
      }else {
        return false
      }
    },

    close() {
      this.viewShow = false;
      this.$emit("close")
    },
    handlePreview(id) {
      this.viewId = id;
      this.$refs.viewRef.handleOpenView(id);
      this.viewShow = true;
    },
    //不需要验证必填的保存
    saveForm() {
      let _this = this
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
        return;
      }
      if (!_this.formData.applyTitle) {
        _this.$message.warning(_this.$t(`doc.this_dept_title_not_null`));
        return;
      }
      _this.loading = true
      let formData = JSON.parse(JSON.stringify(_this.formData))
      formData.recordStatus = "draft";
      if (formData.id) {
        updateChangeApply(formData).then((res) => {
          if (res.code===200) {
            _this.$message({
              message: _this.$t(`file_handle.change_save_succ`),//提示的信息
              type:'success',　　//类型是成功
              duration:1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
              onClose:()=>{
                _this.close();
              }
            });
          }
        });
      } else {
        let wf_receivers = [];
        wf_receivers.push({
          receiveUserId: _this.userInfo.userName,
          receiveUserOrgId: _this.userInfo.deptId
        });
        formData.bpmClientInputModel = {
          model: {
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_procTitle: formData.applyTitle,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_curActDefName: _this.pListData.actDefName,
          },
          type: formData.type
        };
        formData.editStatus = _this.editStatus
        addChangeApply(formData).then((res) => {
          if (res.code===200) {
            _this.formData.id = res.data.id;
            _this.$message({
              message: _this.$t(`file_handle.change_save_succ`),//提示的信息
              type:'success',　　//类型是成功
              duration:1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
              onClose:()=>{
                _this.close();
              }
            });
          }
        });
      }
    },
    // 提交
    async submitForm() {
      let _this = this
      // 首先页签调整为 信息内容
      _this.activeName = 'info'
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
        return;
      }
      let dialogVisible = true
      //审核
      if (_this.attributeModel('shenhe')||_this.attributeModel('pizhun')) {
        if (!_this.formSubmit.pass) {
          _this.$modal.msgError(_this.submitLabel+_this.$t(`file_handle.change_result_not_null`));
          return true;
        }
      }
      if (!!_this.$refs["elForm"]) {
        let valid = await _this.$refs["elForm"].validate()
        if (!valid) {
          dialogVisible = false
        }
      }
      if (!!_this.$refs["validateForm"]) {
        let validateValid = await _this.$refs["validateForm"].validate()
        if (!validateValid) {
          dialogVisible = false
        }
      }
      if (_this.validate()){
        return
      }
      this.loading = true;
      this.dialogVisible = true;
      this.loading = false;
    },
    // 审批结论选择
    commentItemSelect(val) {
      let lang = this.$t(`doc.this_dept_pass`)
      if(val == 'pass') {
        lang = this.$t(`doc.this_dept_pass`)
      } else if(val == 'un_pass') {
        lang = this.$t(`doc.this_dept_not_pass`)
      }
      let summary = this.formSubmit.summary
      // 赋值审核意见 this.formSubmit.summary = lang +' '+summary
      this.formSubmit.summary = lang
    },
    validate(){
      // 验证是否填写了审核意见
      let _this = this
      let validate = false
      if (_this.attributeModel('shenhe')||_this.attributeModel('pizhun')) {
        if(_this.formSubmit.summary.trim() == '') {
          _this.$modal.msgError(_this.$t(`doc.this_dept_pls_fill`)+_this.submitLabel+this.$t(`doc.this_dept_comments`));
          return true;
        }
      }
      return false;
    },

    //提交表单和流程数据
    handleWorkflowSubmit(invokeFrom) {
      let _this = this
      let formData = JSON.parse(JSON.stringify(_this.formData))
      let wf_receivers = [];
      let wf_nextActDefId = null
      if(typeof(invokeFrom) == 'object'  ) {
        if (_this.$refs.prochild.receiveUserList.length < 1 &&_this.$refs.prochild.nextData.actDefType!=='endEvent') {
          if (_this.approvalStatus) {
            _this.$message.warning(_this.$t(`doc.this_dept_directory_select_user_alert`,[prochild.curOptionLabel]));
          }else {
            _this.$message.warning(_this.$t(`doc.this_dept_select_user_alert`));
          }
          return;
        }
        _this.$refs.prochild.receiveUserList.forEach((element) => {
          wf_receivers.push({
            receiveUserId: element.id,
            receiveUserOrgId: element.parentId,
          });
        });
        wf_nextActDefId = _this.$refs.prochild.nextData.actDefId;
      } else if (typeof(invokeFrom) == 'string' && invokeFrom == 'publish') {
        // 来源于按钮【执行发布】
        wf_nextActDefId = 'end'
      }
      // 显示加载中
      _this.flowStepLoading = true
      _this.detailLoading = true
      if (_this.pListData && _this.pListData.procInstId) {
        //流程执行参数
        formData.bpmClientInputModel = {
          model: {
            wf_procDefKey: _this.procDefKey,
            wf_procDefId: _this.pListData.procDefId,
            wf_procTitle: _this.formData.applyTitle,
            wf_curActInstId: _this.pListData.curActInstId,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_nextActDefId: wf_nextActDefId,
            wf_curComment: _this.formSubmit.summary,
            wf_curActDefName: _this.pListData.curActDefName,
          },
          applyStatus: _this.formSubmit.pass || false,
          type: formData.type
        };
      }else{
        //创建流程参数
        formData.bpmClientInputModel = {
          type: formData.type,
          model: {
            wf_procTitle:  _this.formData.applyTitle,
            wf_nextActDefId: wf_nextActDefId,
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_curActDefName: _this.pListData.actDefName,
          },
        };
      }
      if (_this.attributeModel('fabu')&&wf_nextActDefId==='end') {
        //办结
        formData.recordStatus = 'done'
      } else {
        //进行中
        formData.recordStatus = 'doing'
      }
      formData.editStatus = _this.editStatus
      addChangeApply(formData).then((res) => {
        if (res.code===200) {
          _this.$message({
            message: _this.$t(`doc.this_dept_process_sub_succ`),//提示的信息
            type:'success',　　//类型是成功
            duration:1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
            onClose:()=>{
              _this.flowStepLoading = false
              _this.detailLoading = false
              _this.dialogVisible = false;
              _this.close();
            }
          });
        }
      });
    },
    handleSelect(source, index) {
      let _this = this
      _this.$nextTick(()=>{
        _this.$refs.userList.init(source,index,this.userInfo.deptId)
      })
    },
    getSettingDocClassTreeseList() {
      settingDocClassList({ classStatus: "1", dataType:this.formData.dataType }).then(
        (response) => {
          this.classLevelOptions = [];
          // 不展示分类：外来文件以及子类
          let res = response.rows.filter(
            (item) => (item.id != "DEO" && item.parentClassId != "DEO"  &&  item.id != "STDD-R"  &&  item.id != "PROJECT-R"  )
          );
          response.rows.forEach((element, index) => {
            response.rows[index].children = [];
          });
          this.classLevelOptions = this.handleTree(res,"id","parentClassId");
        }
      );
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    // 树形结构数据转化成列表结构数据
    deepTraversal(data) {
      const result = [];
      data.forEach((item) => {
        const loop = (data) => {
          result.push({
            deptId: data.deptId,
            deptName: data.deptName,
            notPrint: data.notPrint,
            nums: data.nums,
          });
          let child = data.children;
          if (child) {
            for (let i = 0; i < child.length; i++) {
              loop(child[i]);
            }
          }
        };
        loop(item);
      });
      return result;
    },
    handleMonitor() {
      this.monitorDrawerVisible = true;

      this.$nextTick(() => {
        this.$refs.monitorDrawer.init(this.pListData.procInstId);
      });
    },
    onProjectChange(val){
      let _this = this
      _this.formData.projectId = val.id
      _this.formData.projectName = val.name
      _this.formData.invokeId =  val.id
    },
    handleSelectFile() {
      this.$nextTick(()=>{
        this.$refs.versionList.init(null,null,false)
      })
    },
    versionSelectHandle(source,index,data){
      selectStatusByDocId({
        docId: data.docId,
        versionId: data.versionId,
      }).then((res) => {
        if (res.data != "0") {
          this.$modal.msg(res.msg);
        } else {
          this.formData.docId = data.docId;
          this.formData.docName = data.docName;
          this.formData.applyId = data.applyId;
          this.formData.versionId = data.versionId;
          this.formData.versionValue = data.versionValue;
          this.formData.docClass = data.docClass;
          this.open = false;
          standardGetDetail({
            docId: data.docId,
            versionId: data.versionId,
          }).then((res) => {
            this.standardDocfileList = [
              {
                name: res.data.preStandardDoc.docName,
                url: res.data.preStandardDoc.fileId,
              },
            ];
            this.appendixsList = res.data.preAppendixes;
          });
        }
      });
    },
    userSelectHandle(source,index,user){
      this.formData.standUserName = user.nickName
      this.formData.standDeptName = user.dept.deptName
      this.formData.editUserName = user.userName
      this.formData.editDeptId = user.deptId
    },
    onChangeType(){
      this.formData.docId = undefined
      this.formData.docName = undefined
      this.formData.applyId = undefined
      this.formData.versionId = undefined
      this.formData.versionValue = 'A0'
      this.formData.docClass = undefined
      this.standardDocfileList = []
      this.appendixsList = []
    }
  },
};
</script>
