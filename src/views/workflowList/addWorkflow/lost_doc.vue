<template>
  <div class="document_change_add"  v-loading="loading||detailLoading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{ $t(`doc.this_dept_file_loss`) }}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button v-if="pListData&&pListData.procInstId" @click="handleMonitor">{{ $t(`doc.this_dept_process_monitor`) }}</el-button>
        <el-button v-if="(editStatus||workflowStatus)" type="primary" @click="submitForm" v-dbClick>{{ $t(`doc.this_dept_annex`) }}</el-button>
        <el-button v-if="editStatus" type="primary" @click="saveForm" v-dbClick>{{ $t(`doc.this_dept_save`) }}</el-button>
        <el-button @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <el-tabs v-if="pListData&&pListData.procInstId" v-model="activeName">
      <el-tab-pane :label="$t(`doc.this_dept_info_content`)" name="info"></el-tab-pane>
      <el-tab-pane :label="$t(`doc.this_dept_approval_records`)" name="log"></el-tab-pane>
    </el-tabs>
    <div class="dialog-body" v-show="activeName==='info'">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_appli_info`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_title`)+`:`" prop="applyTitle">
                  <el-input
                    v-if="editStatus"
                    v-model="formData.applyTitle"
                    :placeholder="$t(`doc.this_dept_insert_name`)"
                    maxlength="50"
                    clearable
                    style="width: 80%; margin-right: 10px"
                  >
                  </el-input>
                  <sapn v-else>{{formData.applyTitle}}</sapn>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_appli_dept`)" prop="deptId">
                  <sapn>{{formData.deptName}}</sapn>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_claimant`)+`:`" prop="userName">
                  <sapn>{{formData.nickName}}</sapn>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_appli_date`)" prop="applyTime">
                  <sapn>{{formData.applyTime}}</sapn>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_names`)" prop="docName">
                  <sapn>{{formData.docName}}</sapn>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_code`)" prop="docId">
                  <sapn>{{formData.docId}}</sapn>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_versions`)" prop="versionValue">
                  <sapn>{{formData.versionValue}}</sapn>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_type`)" prop="docClass">
                  <sapn>{{formatterDocClass(null,null,formData.docClass,null)}}</sapn>
                </el-form-item>
              </el-col>
            </el-row>
            <!--            <el-row>-->
            <!--              <el-col :span="24">-->
            <!--                <el-form-item label="是否外发:" prop="isPuttingOut">-->
            <!--                  <el-radio-group  v-if="editStatus" v-model.trim="formData.isPuttingOut">-->
            <!--                    <el-radio-->
            <!--                      v-for="(item, index) in dict.type.sys_yes_no"-->
            <!--                      :key="index"-->
            <!--                      :label="item.value"-->
            <!--                    >{{ item.label }}</el-radio>-->
            <!--                  </el-radio-group>-->
            <!--                  <dict-tag v-else :options="dict.type.sys_yes_no" :value="formData.isPuttingOut"/>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--            </el-row>-->
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_appli_reason`)+`:`" prop="reason">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    v-model="formData.reason"
                    type="textarea"
                    resize="none"
                    :placeholder="$t(`doc.this_dept_insert_appli_reason`)"
                    :autosize="{minRows:4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_loss_impact`)+`:`" prop="effect">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    v-model.trim="formData.effect"
                    type="textarea"
                    resize="none"
                    :placeholder="$t(`doc.this_dept_insert_appli_reason`)"
                    :autosize="{minRows:4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t(`doc.this_dept_post_precaution`)+`:`" prop="precaution">
                  <el-input
                    :class="editStatus?'':'fujian'"
                    :readonly="!editStatus"
                    v-model.trim="formData.precaution"
                    type="textarea"
                    resize="none"
                    :placeholder="$t(`doc.this_dept_insert_appli_reason`)"
                    :autosize="{minRows:4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_loss_file_info`) }}</div>
          <div class="cell-btn">
            <button
              type="button"
              @click="handleSelectFile()"
              class="el-button blue el-button--default"
              v-if="editStatus"
            >
              <span>{{ $t(`doc.this_dept_select_file`) }}</span>
            </button>
          </div>
        </div>
        <div class="el-card gray-card is-always-shadow">
          <div class="el-card__body">
            <el-table :data="formData.itemList">
              <el-table-column :label="$t(`doc.this_dept_distribute_info`)" align="left" prop="code" :formatter="formatterCode"></el-table-column>
              <el-table-column :label="$t(`doc.this_dept_sign_dept`)" align="left" prop="receiveUserDept"></el-table-column>
              <el-table-column :label="$t(`doc.this_dept_signatory`)" align="left" prop="receiveNickName" />
              <el-table-column :label="$t(`doc.this_dept_sign_date`)" align="left" prop="receiveTime"/>
              <el-table-column :label="$t(`doc.this_dept_status`)" align="left" prop="status" :formatter="formatterStatus"/>
              <el-table-column
                :label="$t(`doc.this_dept_operation`)"
                align="left"
                class-name="small-padding fixed-width"
                v-if="editStatus"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleDelete(formData.itemList,scope.$index)"
                  >{{ $t(`doc.this_dept_delete`) }}</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <div class="news-card" v-if="(attributeModel('shenhe')||attributeModel('pizhun'))&&workflowStatus">
        <div class="card-head">
          <div class="cell-title">{{submitLabel}}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="validateForm"
            :model="formSubmit"
            :rules="rules"
            size="medium"
            label-position="right"
            label-width="200px"
          >
            <el-row gutter="15" >
              <el-col :span="24">
                <el-form-item :label="submitLabel+$t(`file_handle.change_result`)+':'" prop="pass">
                  <el-radio-group v-model.trim="formSubmit.pass"  @input="commentItemSelect">
                    <el-radio
                      v-for="dict in passoptions"
                      :key="dict.value"
                      :label="dict.value"
                    >{{ dict.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="24">
                <el-form-item :label="submitLabel+$t(`doc.this_dept_comments`)+':'">
                  <el-input
                    v-model="formSubmit.summary"
                    type="textarea"
                    :placeholder="$t(`doc.this_dept_insert`)+submitLabel+$t(`doc.this_dept_comments`)"
                    maxlength="200"
                    :autosize="{ minRows: 4, maxRows: 4 }"
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </div>
    <div v-show="activeName==='log'">
      <workflow-logs :procInstId = "pListData.procInstId"></workflow-logs>
    </div>
    <!-- 流程选择下一环节及人员  开始 -->
    <el-dialog
      :title="$t(`doc.this_dept_select_next`)"
      v-if="dialogVisible" :visible.sync="dialogVisible"
      width="60%"
      append-to-body
      v-loading = flowStepLoading
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <processcode
        ref="prochild"
        :selected="attributeModel('default_selected')"
        :userListStatus="attributeModel('user_list')"
        :searchQuery="searchQuery"
        :pListData="pListData"
        :isSummary="isSummary"
      ></processcode>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
        <el-button type="primary" v-dbClick @click="handleWorkflowSubmit"
        >{{ $t(`doc.this_dept_annex`) }}</el-button
        >
      </span>
    </el-dialog>
    <!-- 流程选择下一环节及人员  结束 -->
    <monitor-drawer
      v-if="monitorDrawerVisible"
      ref="monitorDrawer"
    ></monitor-drawer>
    <!-- 文件预览组件 -->
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="close"></as-pre-view>
    <el-drawer
      :visible.sync="drawerShow"
      direction="rtl"
      size="60%"
      :with-header="false"
      :wrapperClosable="false"
      :show-close="false"
      modal-append-to-body
      append-to-body
      :destroy-on-close="true"
    >
      <distribute-list ref="distributeList" :title="$t(`doc.this_dept_select_loss_file`)" @close="handleCloseChange" @submit="handleDistributeList"></distribute-list>
    </el-drawer>
  </div>
</template>
<script>
import { settingDocClassList } from "@/api/file_settings/type_settings";
import processcode from "@/views/workflowList/processcode/index.vue";
import WorkflowLogs from "@views/workflowList/workflowLogs/index.vue";
import {
  workflowprocesskey,
  getStartActdef,
  getExtAttributeModel,
  procInstInfoAndStatus,
} from '@/api/my_business/workflow'
// PDF本地文件预览
import { getWorkflowApplyLog } from '@/api/my_business/workflowApplyLog'
import DistributeList from '@views/workflowList/addWorkflow/add_import/distributeList.vue'
import { addLostApply, getLostApplyByBpmnId, updateLostApply } from '@/api/process/lostApply'
import { listLostApplyItem } from '@/api/process/lostApplyItem'
export default {
  dicts: ['sys_yes_no'],
  components: {
    DistributeList,
    processcode,
    WorkflowLogs
  },
  name: "Additional_doc",
  props: ['data'],
  data() {
    return {
      drawerShow: false,
      searchQuery: {},
      deptList: [],
      companyList: [],
      deptOptions: [],
      docClassList: [],
      submitLabel:undefined,
      passoptions: [
        { value: "pass", label: this.$t(`doc.this_dept_pass`) },
        { value: "un_pass", label: this.$t(`doc.this_dept_not_pass`) },
      ],
      formSubmit: { summary: "", actionType: "", pass: "" },
      isSummary: false,
      activeName: "info",
      nodeDetail: [],
      procDefKey: undefined,
      viewId: "",
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      viewShow: false,
      monitorDrawerVisible:false,
      formData: {
        id: undefined,
        applyTitle: undefined,
        deptId: undefined,
        userName: undefined,
        deptName: undefined,
        nickName: undefined,
        versionId: undefined,
        docId: undefined,
        docName: undefined,
        versionValue: undefined,
        docClass: undefined,
        reason: undefined,
        status: undefined,
        applyTime: undefined,
        isPuttingOut: undefined,
        effect: undefined,
        precaution: undefined,
        itemList: []
      },
      rules: {
        effect: [
          { required: true, message: this.$t(`doc.this_dept_insert_loss_impact`), trigger: "blur" },
        ],
        precaution: [
          { required: true, message: this.$t(`doc.this_dept_insert_post_precaution`), trigger: "blur" },
        ],
        isPuttingOut: [
          { required: true, message: this.$t(`doc.this_dept_select_whether_outgoing`), trigger: "blur" },
        ],
        applyTitle: [
          { required: true, message: this.$t(`doc.this_dept_insert_title`), trigger: "blur" },
        ],
        pass:[
          { required: true, message: this.$t(`doc.this_dept_pls_select`), trigger: "blur" },
        ],
        reason: [
          { required: true, message: this.$t(`doc.this_dept_insert_appli_reason`), trigger: "blur" },
        ],
      },
      kuozhanshuju: {},
      pListData: {},
      editStatus:false,
      workflowStatus: false,
      dialogVisible: false,
      loading: false,
      detailLoading: false,
      flowStepLoading : false
    };
  },
  computed: {},
  watch: {
    data (val) {
      if (val) {
        this.init(val)
      }
    },
  },
  mounted() {
    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    handleExport2() {
      this.$refs.pdfView.init()
    },
    async init(row) {
      let _this = this
      _this.rest()
      _this.loading = true
      _this.procDefKey = row.type+'_mh'
      _this.formData.type = row.type
      if (row.preChangeCode) {
        let res = await getWorkflowApplyLog(row.preChangeCode)
        row.procInstId = res.data.procInstId
      }
      //是否编辑模式
      _this.$nextTick(() => {
        if (row && row.procInstId) {
          let procInstId = row.procInstId
          _this.workflowStatus = row.status == '1'
          _this.procInstInfoAndStatus(procInstId)
          _this.getDetail(procInstId)
        } else {
          _this.workflowStatus = true
          _this.loading = false
          _this.editStatus = true
          _this.formData.docClass = row.docClass
          _this.formData.docId = row.docId
          _this.formData.docName = row.docName
          _this.formData.versionValue = row.versionValue
          _this.formData.versionId = row.versionId
          _this.getSettingDocClassTreeseList();
          _this.getWorkflowprocesskey();
        }
      });
    },
    formatterCode(row, column, cellValue, index){
      if (row.code<10) {
        return '0'+row.code;
      }
      return row.code;
    },
    formatterStatus(row){
      if (row.status==='lost')  {
        return this.$t(`doc.this_dept_loss`)
      }else if (row.status==='recovery') {
        return this.$t(`file_handle.print_recovered`)
      }else {
        return this.$t(`file_handle.print_unrecover`)
      }
    },
    getDetail(procInstId) {
      let _this = this
      _this.detailLoading = true
      getLostApplyByBpmnId(procInstId).then(async (res) => {
        let formData = res.data;
        formData.type = _this.formData.type
        _this.formData = formData
        _this.getSettingDocClassTreeseList();
        _this.getItemList(formData.id)
      }).finally(()=>{
        _this.detailLoading = false
      });
    },
    getItemList(applyId){
      listLostApplyItem({applyId:applyId}).then(res =>{
        this.$set(this.formData,'itemList',res.data)
      })
    },
    rest(){
      let _this = this
      _this.activeName = "info"
      _this.formData= {
        id: undefined,
        applyTitle: undefined,
        deptId: this.userInfo.deptId,
        userName: this.userInfo.userName,
        deptName: this.userInfo.dept.deptName,
        nickName: this.userInfo.nickName,
        versionId: undefined,
        docId: undefined,
        docName: undefined,
        versionValue: undefined,
        docClass: undefined,
        reason: undefined,
        status: undefined,
        applyTime: _this.parseTime(new Date()),
        isPuttingOut: undefined,
        effect: undefined,
        precaution: undefined,
        itemList: []
      }
    },
    procInstInfoAndStatus(procInstId){
      let _this = this
      procInstInfoAndStatus(procInstId).then((res) => {
        if (res) {
          _this.pListData = res
        }else {
          _this.pListData = {procInstId:procInstId}
        }
        _this.getExtAttributeModel()
      });
    },
    nodeShow(code){
      let _this = this
      if (_this.nodeDetail) {
        return !!_this.nodeDetail.find(node=>node.code===code)
      }else  {
        return  false
      }
    },
    getWorkflowprocesskey() {
      let _this = this
      _this.loading = true
      _this.pListData = {}
      if (_this.procDefKey) {
        workflowprocesskey(_this.procDefKey).then((data) => {
          getStartActdef(data.data.procDefId).then((res) => {
            _this.pListData = res.data;
            this.getExtAttributeModel()
          });
        });
      }else {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
      }
    },
    getExtAttributeModel(){
      let _this = this
      let procDefId = _this.pListData.procDefId
      let curActDefId = _this.pListData.curActDefId || _this.pListData.actDefId
      if (procDefId&&curActDefId) {
        getExtAttributeModel(
          procDefId,
          curActDefId
        ).then((res) => {
          console.log("扩展属性====>", res);
          let kuozhanshuju = {}
          res.data.forEach(item=>{
            kuozhanshuju[item.objKey] = item.objValue
          })
          _this.kuozhanshuju = kuozhanshuju;
          _this.editStatus =_this.attributeModel('bianji')&&_this.workflowStatus
          _this.submitLabel = _this.attributeModel('pizhun')?_this.$t(`file_handle.change_approve`):_this.$t(`file_handle.change_auditing`)
        }).finally(()=>{
          _this.loading = false
        });
      }else {
        _this.kuozhanshuju = {}
        _this.loading = false
      }
    },
    attributeModel(val){
      if (this.kuozhanshuju&&this.kuozhanshuju!=={}) {
        let obj = this.kuozhanshuju[val]
        return obj?obj==='true':false
      }else {
        return false
      }
    },

    close() {
      this.viewShow = false;
      this.$emit("close")
    },
    //不需要验证必填的保存
    saveForm() {
      let _this = this
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
        return;
      }
      if (!_this.formData.applyTitle) {
        _this.$message.warning(_this.$t(`doc.this_dept_title_not_null`));
        return;
      }
      _this.loading = true
      let formData = JSON.parse(JSON.stringify(_this.formData))
      formData.recordStatus = "draft";
      formData.editStatus = _this.editStatus
      if (formData.id) {
        updateLostApply(formData).then((res) => {
          if (res.code===200) {
            _this.$message({
              message: _this.$t(`file_handle.change_save_succ`),//提示的信息
              type:'success',　　//类型是成功
              duration:1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
              onClose:()=>{
                _this.close();
              }
            });
          }
        });
      } else {
        let wf_receivers = [];
        wf_receivers.push({
          receiveUserId: _this.userInfo.userName,
          receiveUserOrgId: _this.userInfo.deptId
        });
        formData.bpmClientInputModel = {
          model: {
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_procTitle: formData.applyTitle,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_curActDefName: _this.pListData.actDefName,
            wf_curActDefId: _this.pListData.actDefId,
            wf_nextActDefName: _this.pListData.actDefName,
            wf_nextActDefId: _this.pListData.actDefId,
          },
          type: formData.type,
          review: _this.attributeModel('shenhe'),
        };
        formData.editStatus = _this.editStatus
        addLostApply(formData).then((res) => {
          if (res.code===200) {
            _this.formData.id = res.data.id;
            _this.$message({
              message: _this.$t(`file_handle.change_save_succ`),//提示的信息
              type:'success',　　//类型是成功
              duration:1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
              onClose:()=>{
                _this.close();
              }
            });
          }
        });
      }
    },
    // 提交
    async submitForm() {
      let _this = this
      // 首先页签调整为 信息内容
      _this.activeName = 'info'
      if (!_this.procDefKey) {
        _this.$message.warning(_this.$t(`doc.this_dept_no_process_setting`));
        return;
      }
      let dialogVisible = true
      //审核
      if (_this.attributeModel('shenhe')||_this.attributeModel('pizhun')) {
        if (!_this.formSubmit.pass) {
          _this.$modal.msgError(_this.submitLabel+_this.$t(`file_handle.change_result_not_null`));
          return true;
        }
      }
      if (!!_this.$refs["elForm"]) {
        let valid = await _this.$refs["elForm"].validate()
        if (!valid) {
          dialogVisible = false
        }
      }
      if (!!_this.$refs["validateForm"]) {
        let validateValid = await _this.$refs["validateForm"].validate()
        if (!validateValid) {
          dialogVisible = false
        }
      }
      if (_this.validate()){
        return
      }
      _this.searchQuery.isPuttingOut = _this.formData.isPuttingOut
      _this.loading = true;
      _this.dialogVisible = true;
      _this.loading = false;
    },
    // 审批结论选择
    commentItemSelect(val) {
      let lang = this.$t(`doc.this_dept_pass`)
      if(val == 'pass') {
        lang = this.$t(`doc.this_dept_pass`)
      } else if(val == 'un_pass') {
        lang = this.$t(`doc.this_dept_not_pass`)
      }
      let summary = this.formSubmit.summary
      // 赋值审核意见 this.formSubmit.summary = lang +' '+summary
      this.formSubmit.summary = lang
    },
    validate(){
      // 验证是否填写了审核意见
      let _this = this
      let validate = false
      if(!_this.formData.itemList||_this.formData.itemList.length < 1) {
        _this.$modal.msgError(_this.$t(`doc.this_dept_select_file`));
        return true;
      }
      if (_this.attributeModel('shenhe')||_this.attributeModel('pizhun')) {
        if(_this.formSubmit.summary.trim() == '') {
          _this.$modal.msgError(_this.$t(`doc.this_dept_pls_fill`)+_this.submitLabel+_this.$t(`doc.this_dept_comments`));
          return true;
        }
      }
      if (_this.attributeModel('jielun')) {
        if(_this.formData.itemList.some(item=>!item.reviewAction)) {
          _this.$modal.msgError(_this.$t(`doc.this_dept_pls_select_review_conclusion`));
          return true;
        }
      }
      return false;
    },

    //提交表单和流程数据
    handleWorkflowSubmit(invokeFrom) {
      let _this = this
      let formData = JSON.parse(JSON.stringify(_this.formData))
      let wf_receivers = [];
      let wf_nextActDefId = null
      let wf_nextActDefName = null
      if(typeof(invokeFrom) == 'object'  ) {
        if (_this.$refs.prochild.receiveUserList.length < 1 &&_this.$refs.prochild.nextData.actDefType!=='endEvent') {
          if (_this.approvalStatus) {
            _this.$message.warning(_this.$t(`doc.this_dept_directory_select_user_alert`,[prochild.curOptionLabel]));
          }else {
            _this.$message.warning(_this.$t(`doc.this_dept_select_user_alert`));
          }
          return;
        }
        _this.$refs.prochild.receiveUserList.forEach((element) => {
          wf_receivers.push({
            receiveUserId: element.id,
            receiveUserOrgId: element.parentId,
          });
        });
        wf_nextActDefId = _this.$refs.prochild.nextData.actDefId;
        wf_nextActDefName = _this.$refs.prochild.nextData.actDefName;
      } else if (typeof(invokeFrom) == 'string' && invokeFrom == 'publish') {
        // 来源于按钮【执行发布】
        wf_nextActDefId = 'end'
        wf_nextActDefName = '结束'
      }
      // 显示加载中
      _this.flowStepLoading = true
      _this.detailLoading = true
      if (_this.pListData && _this.pListData.procInstId) {
        //流程执行参数
        formData.bpmClientInputModel = {
          model: {
            wf_procDefKey: _this.procDefKey,
            wf_procDefId: _this.pListData.procDefId,
            wf_procTitle: _this.formData.applyTitle,
            wf_curActInstId: _this.pListData.curActInstId,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_nextActDefId: wf_nextActDefId,
            wf_curComment: _this.formSubmit.summary,
            wf_curActDefId: _this.pListData.curActDefId,
            wf_curActDefName: _this.pListData.curActDefName,
            wf_nextActDefName: wf_nextActDefName,
          },
          applyStatus: _this.formSubmit.pass || false,
          type: formData.type,
          review: _this.attributeModel('shenhe'),
        };
      }else{
        //创建流程参数
        formData.bpmClientInputModel = {
          type: formData.type,
          model: {
            wf_procTitle:  _this.formData.applyTitle,
            wf_nextActDefId: wf_nextActDefId,
            wf_procDefId: _this.pListData.procDefId,
            wf_procDefKey: _this.procDefKey,
            wf_sendUserId: _this.userInfo.userName,
            wf_sendUserOrgId: _this.userInfo.deptId,
            wf_receivers: wf_receivers,
            wf_curActDefName: _this.pListData.actDefName,
          },
          review: _this.attributeModel('shenhe'),
        };
      }
      if (_this.attributeModel('fabu')&&wf_nextActDefId==='end') {
        //办结
        formData.recordStatus = 'done'
      } else {
        //进行中
        formData.recordStatus = 'doing'
      }
      formData.editStatus = _this.editStatus
      addLostApply(formData).then((res) => {
        if (res.code===200) {
          _this.$message({
            message: this.$t(`doc.this_dept_process_sub_succ`),//提示的信息
            type:'success',　　//类型是成功
            duration:1200,　　//显示时间, 毫秒。设为 0 则不会自动关闭，建议1200
            onClose:()=>{
              _this.flowStepLoading = false
              _this.detailLoading = false
              _this.dialogVisible = false;
              _this.close();
            }
          });
        }
      });
    },
    getSettingDocClassTreeseList() {
      settingDocClassList({ classStatus: "1",neClassType:'foreign'}).then(res => {
          this.docClassList = JSON.parse(JSON.stringify(res.rows))
          this.docClassTree = this.handleTree(res.rows, "id", "parentClassId")
        }
      );
    },
    handleMonitor() {
      this.monitorDrawerVisible = true;
      this.$nextTick(() => {
        this.$refs.monitorDrawer.init(this.pListData.procInstId);
      });
    },
    handleSelectFile() {
      this.drawerShow = true
      this.$nextTick(()=>{
        this.$refs.distributeList.init(this.formData.versionId,this.formData.itemList)
      })
    },
    handleCloseChange(){
      this.drawerShow = false
    },
    handleDistributeList(dataList){
      this.handleCloseChange()
      if (dataList) {
        dataList.forEach(item=>{
          this.formData.itemList.push({
            distributeId: item.id,
            code: item.code,
            receiveUserName: item.receiveUserName,
            receiveNickName: item.receiveNickName,
            receiveUserDeptId: item.receiveUserDeptId,
            receiveUserDept: item.receiveUserDept,
            receiveTime:item.receiveTime,
            status:item.status,
          })
        })
      }
    },
    handleDelete(dataList,index){
      dataList.splice(index,1)
    },
    formatterDocClass(row, column, cellValue, index){
      let _this = this
      if (_this.docClassList) {
        let item = _this.docClassList.find(item=>item.id===cellValue)
        return item?item.className:cellValue
      }
      return cellValue
    },
  },
};
</script>
<style scoped>
.document_change_add{
  .fujian .el-textarea__inner {
    border: 0 solid #dcdfe6;
    padding: 0;
  }
}

</style>
