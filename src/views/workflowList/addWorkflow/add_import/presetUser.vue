<template>
 <el-dialog
   :title="$t(`doc.this_dept_select_link_user`)"
   :visible.sync="visible"
   width="50%"
   :close-on-click-modal="false"
   append-to-body>
    <div class="el-card__body">
      <el-card class="gray-card">
        <el-table v-loading="loading" border :data="dataList" max-height="500" :row-class-name="tableRowClassName">
          <el-table-column :label="$t(`doc.this_dept_process_link`)"  prop="nodeName"/>
          <el-table-column :label="$t(`doc.this_dept_yet_select_user`)"  prop="users" :formatter="formatterUsers"/>
          <el-table-column :label="$t(`doc.this_dept_operation`)" v-if="!multiple" width="100px" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="selectHandle(scope.row,scope.$index)"
              >{{ $t(`file_set.type_select`) }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
     <div slot="footer" class="dialog-footer">
       <el-button type="primary" @click="handleSubmit">{{ $t(`file_set.type_confim`) }}</el-button>
       <el-button @click="visible = false">{{ $t(`file_set.type_cancel`) }}</el-button>
     </div>
   <assign-users ref="assignUsers" @selectHandle="userSelectHandle"></assign-users>
 </el-dialog>
</template>

<script>
import { getNodeList } from '@/api/setting/docClassFlowNode'
import AssignUsers from '@views/workflowList/addWorkflow/add_import/assignUsers.vue'

export default {
  name: "PresetUser",
  components: { AssignUsers },
  props: [{
  }],
  data() {
    return {
      nodeCode: undefined,
      presetUserList: [],
      source: undefined,
      index: undefined,
      visible: false,
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      multiple: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      dataList: [],
      // 查询参数
      queryParams: {
        docClass: undefined,
        bizType: undefined,
        code: 'page_oper_preset_link'
      },
    };
  },
  methods: {
    formatterUsers(row, column, cellValue, index){
      if (cellValue) {
        let users=JSON.parse(cellValue)
        return  users&&users.length>0?users.map(item=>item.nickName).join("、"):""
      }
      return ""
    },
    tableRowClassName({row, rowIndex}){
      return row.isShow?'':'isHide'
    },
    //来源标识，来源序号，文件类型，业务类型,环节节点
    init(source,index,docClass,bizType,presetUserList,nodeCode) {
      let _this = this
      _this.visible = true
      _this.source = source
      _this.index = index
      _this.nodeCode = nodeCode
      this.onLoading(docClass,bizType,presetUserList)
    },
    async onLoading(docClass,bizType,presetUserList){
      let _this = this
      _this.queryParams.docClass = docClass
      _this.queryParams.bizType = bizType
      _this.presetUserList = presetUserList?JSON.parse(JSON.stringify(presetUserList)):[]
      await _this.getList()
    },
    /** 查询岗位列表 */
    async getList() {
      let _this = this
      _this.loading = true
      let res = await getNodeList(_this.queryParams)
      let dataList = []
      res.data.forEach(node => {
        let item = _this.presetUserList.find(item => item.nodeCode === node.nodeCode)
        let funCondition = JSON.parse(node.funCondition)
        let isShow = true
        let validate = true
        if (funCondition&&funCondition.nodeCode) {
          isShow = funCondition.nodeCode.includes(_this.nodeCode)
          validate = isShow && funCondition.validate
        }
        if (item) {
          item.nodeName = node.nodeName
          item.isMulti = node.isMulti
          item.isShow = isShow
          item.validate = validate
          dataList.push(item)
        } else {
          let data = {
            nodeCode: node.nodeCode,
            nodeName: node.nodeName,
            isMulti: node.isMulti,
            users: undefined,
            isShow: isShow,
            validate: validate
          }
          dataList.push(data)
        }
      })
      _this.dataList = dataList;
      _this.loading = false;
    },
    selectHandle(row,index){
      let _this = this
      _this.$nextTick(()=>{
        _this.$refs.assignUsers.init(row.isMulti?_this.$t(`doc.select_personnel_multiple`):_this.$t(`doc.select_personnel_single`),null,index,row.users?JSON.parse(row.users):[],row.isMulti)
      })
    },
    userSelectHandle(source,index,data){
      let _this = this
      _this.$set(_this.dataList[index],"users",JSON.stringify(data))
    },
    handleSubmit(){
      let _this = this
      _this.$emit("selectHandle",_this.source,_this.index,_this.dataList);
      this.visible = false
    },
    async validate(docClass, bizType, presetUserList,nodeCode) {
      let _this = this
      _this.nodeCode = nodeCode
      await this.onLoading(docClass, bizType, presetUserList)
      let bool = false
      if (_this.dataList&&_this.dataList.length>0){
          _this.dataList.forEach(item=>{
            if (item.isShow&&item.validate) {
              if (!item.users || (item.users&&item.users.length<=2)){
                bool = true
              }
            }
          })
      }
      return bool
    }
  }
};
</script>
