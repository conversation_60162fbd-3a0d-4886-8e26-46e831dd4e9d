<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
      class="top"
    >
      <el-row type="flex">
        <el-col :span="24" style="text-align: right" v-if="editStatus&&!disuseStatus">
          <el-button
            @click="handleAdd()"
            plain
          >{{ $t(`doc.this_dept_new_add`) }}</el-button
          >
        </el-col>
      </el-row>
    </el-form>
    <el-card class="gray-card">
      <el-table
        v-loading="loading"
        :key="isshow"
        :data="dataList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column :label="$t(`myItem.borrow_file_type`)" align="left" prop="docClass" :formatter="formatterDocClass">
        </el-table-column>
        <el-table-column :label="$t(`myItem.borrow_file_name`)" align="left" prop="docName">
          <template slot-scope="scope">
            <span
              @click="handlePreview(scope.row.fileId)"
              style="color: #0144bb; cursor: pointer"
            >{{ scope.row.docName }}</span
            >
          </template>
        </el-table-column>
        <el-table-column :label="$t(`myItem.borrow_file_id`)" align="left" prop="docId" />
        <el-table-column :label="$t(`myItem.borrow_file_ver`)" align="left" prop="versionValue" />
<!--        <el-table-column :label="$t(`doc.this_dept_current_file_status`)" align="left" prop="isDeleted" v-if="status">-->
<!--          <template slot-scope="scope">-->
<!--            <span v-if="scope.row.isDeleted == 0">{{ $t(`doc.this_dept_pending`) }}</span>-->
<!--            <span v-if="scope.row.isDeleted == 1">{{ $t(`doc.this_dept_in_effect`) }}</span>-->
<!--            <span v-if="scope.row.isDeleted == 2">{{ $t(`doc.this_dept_lost_effect`) }}</span>-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--        <el-table-column :label="$t(`doc.this_dept_post_release_status`)" align="left" prop="status" v-if="status">-->
<!--          <template slot-scope="scope">-->
<!--            <span v-if="scope.row.status == 1">{{ $t(`doc.this_dept_take_effect`) }}</span>-->
<!--            <span v-if="scope.row.status == 2">{{ $t(`doc.this_dept_Invalid`) }}</span>-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column :label="$t(`doc.this_dept_effective_date`)" align="left" prop="startDate" v-if="!status">
          <template slot-scope="scope">
            <span>{{parseTime(scope.row.startDate,"{y}-{m}-{d}")}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t(`doc.this_dept_release_date`)" align="left" prop="releaseTime" v-if="!status">
          <template slot-scope="scope">
            <span>{{parseTime(scope.row.releaseTime,"{y}-{m}-{d}")}}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t(`doc.this_dept_operation`)"
          align="left"
          class-name="small-padding fixed-width"
          v-if="editStatus"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="handleUpdate(dataList,scope.$index)"
              v-if="scope.row.status==1&&scope.row.isDeleted==1&&!disuseStatus"
            >{{ $t(`doc.this_dept_revision`) }}</el-button>
            <el-button
              type="text"
              @click="handlezuofei(dataList,scope.$index)"
              v-if="scope.row.status==1&&scope.row.isDeleted==1"
            >{{ $t(`doc.this_dept_cancel`) }}</el-button
            >
            <el-button
              type="text"
              @click="handlecexiao(dataList,scope.$index)"
              v-if="scope.row.isDeleted!=1||scope.row.status!=1"
            >{{ $t(`file_handle.change_revoke`) }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增文件数据来源：文件台账列表-->
      <el-dialog
        destroy-on-close
        :title="title"
        :visible.sync="open"
        v-loading="dialogLoading"
        :close-on-click-modal="false"
        width="800px"
        append-to-body
      >
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item :label="$t(`myItem.borrow_file_name`)+`:`" prop="docName">
            <div style="display: flex">
              <el-input
                :disabled="!ruleForm.fileId"
                v-model.trim="ruleForm.docName"
                :placeholder="$t(`doc.this_dept_select_or_upload`)"
              >
              </el-input>
              <el-upload
                :file-list="standardDocfileList"
                action=""
                :http-request="standardDocBeforeUpload"
              >
                <el-button size="small" type="primary" icon="el-icon-upload"
                >{{ $t(`doc.this_dept_click_to_upload`) }}
                </el-button>
              </el-upload>
            </div>
          </el-form-item>
          <el-form-item :label="$t(`myItem.borrow_file_ver`)+`:`" prop="versionValue">
            <el-input v-model.trim="ruleForm.versionValue"></el-input>
          </el-form-item>
          <el-form-item :label="$t(`myItem.borrow_file_type`)+`:`" prop="docClass">
            <treeselect
              v-model.trim="ruleForm.docClass"
              :options="docClassTree"
              :normalizer="normalizerFile"
              :disable-branch-nodes="true"
              :searchable="false"
              :show-count="true"
              :placeholder="$t(`doc.this_dept_select_type`)"
            />
          </el-form-item>
          <el-form-item :label="$t(`myItem.borrow_file_id`)+`:`">
            <el-input v-model.trim="ruleForm.docId" :disabled="true"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button @click="cancel">{{ $t(`file_set.type_cancel`) }}</el-button>
          <el-button type="primary" @click="getList()">{{ $t(`file_set.type_confim`) }}</el-button>
        </div>
      </el-dialog>
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="close">
    </as-pre-view>
  </div>
</template>

<script>
import { settingDocClassList } from '@/api/file_settings/type_settings'
import Treeselect from '@riophae/vue-treeselect'
import { processFileLocalUpload } from '@/api/commmon/file'

export default {
  name: "LinkRecord",
  components: {
    Treeselect,
  },
  props:{
    dataType: {
      type: String,
    },
    dataList:{
      type: Array,
      default: ()=>[]
    },
    editStatus:{
      type: Boolean,
      default: true,
    },
    status:{
      type: Boolean,
      default: true,
    },
    disuseStatus:{
      type: Boolean,
      default: false,
    },
  },
  data() {
    let validateVersion = (rule, val, callback) =>{
      if (this.dataList.some(item=>!!item.docId&&item.docId===this.ruleForm.docId&&item.versionValue===val)){
        callback(new Error(this.$t(`doc.this_dept_file_version_exist`)));
      }else {
        callback();
      }
    };
    let validateDocName = (rule, val, callback) =>{
      if(this.dataList.some(item=>{
        if (!this.ruleForm.docId) {
          return item.docName===this.ruleForm.docName
        }else {
          return item.docId!==this.ruleForm.docId&&item.docName===this.ruleForm.docName
        }
      })){
        callback(new Error(this.$t(`doc.this_dept_file_exist`)));
      }else {
        callback();
      }
    };
    return {
      dialogLoading: false,
      viewShow: undefined,
      viewId: undefined,
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 已选择数据，需在新增文件列表中排除的
      selectDatas: [],

      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        postCode: undefined,
        postName: undefined,
        status: undefined,
      },
      docClassList: [],
      docClassTree: [],
      ruleForm: {
        docName: undefined,
        versionValue: undefined,
        docId: undefined,
        isDeleted: undefined,
        status: undefined,
        docClass: undefined,
      },
      // 表单校验
      rules: {
        versionValue: [
          { validator: validateVersion, trigger: "blur" },
          { required: true, trigger: "blur", message: this.$t(`doc.this_dept_insert`) }
        ],
        docName: [
          { validator: validateDocName, trigger: "blur" },
          { required: true, trigger: "blur", message: this.$t(`doc.this_dept_insert`) }
        ],
        docClass: [{ required: true, trigger: "blur", message: this.$t(`doc.this_dept_pls_select`) }],
      },
      standardDocfileList: [],

      classLevelOptions: [],
      disabled: true,
      isshow: true,
      selection: [],
    };
  },
  mounted() {
    if (this.docClassList) {
      this.getDocClassList()
    }
  },
  watch:{
    dataType(){
        this.getDocClassList()
    }
  },
  methods: {
    init(docLinks){
      console.log("关联记录",docLinks)
      this.$set(this, 'dataList', docLinks)
    },
    normalizerFile(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    getDocClassList(){
      // 获取文件分类集合
      settingDocClassList({classStatus: "1", dataType:this.dataType,classType:'RECORD'}).then(res => {
        this.docClassList = JSON.parse(JSON.stringify(res.rows))
        this.docClassTree = this.handleTree(res.rows, "id", "parentClassId")
      });
    },
    formatterDocClass(row, column, cellValue, index){
      let _this = this
      let item = _this.docClassList.find(item=>item.id===cellValue)
      return item?item.className:cellValue
    },
    handleDelete(data,index){
      data.splice(index,1);
    },
    handlePreview(id, source, linkType, mode) {
      console.log('预览文件id', id);
      this.viewId = id;
      this.$refs.viewRef.handleOpenView(id, source, linkType, mode);
      this.viewShow = true;
    },
    getList() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          if (this.dataList == null) {
            this.dataList = [];
          }
          let ruleForm = JSON.parse(JSON.stringify(this.ruleForm))
          if (!!ruleForm.docId) {
            //修订
            this.dataList.forEach(item=>{
              if (item.docId===ruleForm.docId) {
                item.status = 2
              }
            })
          }
          this.dataList.push(ruleForm)
          this.open = false;
        }
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 表单重置
    reset() {
      this.ruleForm = {
        docName: undefined,
        versionValue: "A0",
        isDeleted:'0',
        status: "1",
        docId: undefined,
        fileId: undefined,
        docClass: undefined,
      };
      this.standardDocfileList = [];
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selection = selection;
    },
    handleUpdate(dataList,index) {
      let data = dataList[index]
      if (data.isDeleted != 1) {
        this.$modal.msg("关联记录未生效不可修订或者作废");
      } else if(data.status == 2) {
        this.$modal.msg(this.$t(`doc.this_dept_record_yet_cancel`));
      }else {
        this.open = true;
        this.title = this.$t(`doc.this_dept_revision`);
        this.ruleForm = {
          docName: undefined,
          versionValue: data.versionValue,
          status: 1,
          isDeleted: 0,
          docId: data.docId,
          fileId: undefined,
          docClass: data.docClass,
        };
      }
    },
    handlezuofei(dataList,index) {
      let data = dataList[index]
      if (data.isDeleted != 1) {
        this.$modal.msg(this.$t(`doc.this_dept_record_not_update_or_cancel`));
      } else if (data.status == 2) {
        this.$modal.msg(this.$t(`doc.this_dept_record_yet_cancel`));
      } else {
        data.status = 2
      }
    },
    handlecexiao(dataList,index) {
      let data = dataList[index]
      if (!data.docId) {
        //新增的没有docId 撤销直接删
        dataList.splice(index,1)
      }else {
        //修订、作废的
        for (let i=0;i<this.dataList.length;i++) {
          if (this.dataList[i].docId===data.docId) {
            if (this.dataList[i].isDeleted==1) {
              //生效中的改回原来的状态
              this.dataList[i].status = 1
            } else {
              this.dataList.splice(i,1)
            }
          }
        }
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t(`doc.this_dept_new_add`);
    },
    standardDocBeforeUpload(params) {
      const isLt1M = params.file.size / 1024 / 1024 < 10;
      if (!isLt1M) {
        this.$message.error(this.$t(`file_handle.change_upload_limit`));
        return;
      }
      const drop = params.file.name.lastIndexOf(".")
      const suffix = params.file.name.substring(drop + 1);
      const fileName =  params.file.name.substring(0,drop);
      if (
        suffix != "docx" &&
        suffix != "doc" &&
        // suffix != "pdf" &&
        suffix != "ppt" &&
        suffix != "pptx" &&
        suffix != "xls" &&
        suffix != "xlsx"
      ) {
        // this.$message.error("只能上传docx,doc,xls,xlsx,ppt,pptx文档");
        this.$message.error(this.$t(`doc.this_dept_upload_only`)+"docx,doc,xls,xlsx" + this.$t(`doc.this_dept_doc_file`));
        this.standardDocfileList = [];
        return;
      }
      this.standardDocfileList = [];
      let standardDoc = new FormData();
      standardDoc.append("file", params.file); //传文件
      // fd.append('srid',this.aqForm.srid);//传其他参数
      this.dialogLoading = true
      processFileLocalUpload(standardDoc).then((res) => {
          this.ruleForm.docName = fileName;
          this.ruleForm.fileId = res.data.fileId;
      }).catch(e=>{
        this.standardDocfileList = [];
      }).finally(()=>{
        this.dialogLoading = false
      });
      this.disabled = false;
    },
  },
};
</script>
<style lang="scss">
.top {
  margin-bottom: 10px;

  .switch {
    color: #409eff;
  }
}
</style>
