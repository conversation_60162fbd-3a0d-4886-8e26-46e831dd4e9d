<template>
  <el-dialog :title="drawerTitle" :visible.sync="showSelect" width="80%" append-to-body :close-on-click-modal="false" :destroy-on-close="true">
    <el-row>
      <el-col :span="24">
        <el-form :model="queryTableParams" ref="queryForm" :inline="true" label-width="80px">
          <el-form-item :label="$t(`login.username`)" prop="userName">
            <el-input
              v-model="queryTableParams.userName"
              :placeholder="$t(`doc.this_dept_pls_ins_account`)"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item :label="$t(`doc.this_dept_name`)" prop="nickName">
            <el-input
              v-model="queryTableParams.nickName"
              :placeholder="$t(`file_set.number_fill_name`)"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item :label="$t(`doc.this_dept_dept`)">
            <treeselect
              v-model.trim="queryTableParams.deptId"
              :options="deptOptions"
              :disable-branch-nodes="false"
              :normalizer="normalizer"
              :placeholder="$t(`doc.this_dept_select_dept`)"
              style="width: 200px"
              :searchable="true"
              size="mini"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t(`doc.this_dept_search`) }}</el-button>
          </el-form-item>
          <el-form-item style="float: right;margin-right: 20px">
            <el-button  type="primary" @click="submit">{{ $t(`doc.this_dept_annex`) }}</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <across-pages-selected
      rowKey="userName"
      ref="selected"
      :defaultSelected="selected"
      :queryTableParams="queryTableParams"
      :listData="listData"
      :tableTotal="tableTotal"
      @getListData = "getListData"
    >
      <template slot="list">
        <el-table-column type="selection" :selectable="selectable"  width="50"/>
        <el-table-column
          :label="$t(`login.username`)"
          key="userName"
          prop="userName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          :label="$t(`doc.this_dept_name`)"
          key="nickName"
          prop="nickName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          :label="$t(`doc.this_dept_dept`)"
          key="deptId"
          prop="dept.deptName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          :label="$t(`myItem.msg_operation`)"
          v-if="!isMulti"
          width="50px"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="selectHandle(scope.row,scope.$index)"
            >{{ $t(`file_set.type_select`) }}
            </el-button>
          </template>
        </el-table-column>
      </template>
      <template slot="selected">
        <el-table-column
          :label="$t(`login.username`)"
          key="userName"
          prop="userName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          :label="$t(`doc.this_dept_name`)"
          key="nickName"
          prop="nickName"
          :show-overflow-tooltip="true"
        />
      </template>
    </across-pages-selected>
  </el-dialog>
</template>

<script>
    import acrossPagesSelected from "@/components/acrossPagesSelected/index"
    import Treeselect from '@riophae/vue-treeselect'
    import { listUser } from "@/api/system/user";
    import is from 'rzelement-ui/src/locale/lang/is'
    import {listDept} from "../../../../api/system/dept";
    export default {
        name: "AssignUsers",
      computed: {
        is() {
          return is
        }
      },
        components: {acrossPagesSelected,Treeselect},
        data() {
            return {
                isMulti: true,
                drawerTitle: '',
                showSelect: false,
                selected: [],
                listData: [],
                deleteList: [],
                roleId: undefined,
                queryTableParams: {
                    pageNum: 1,
                    pageSize: 10,
                    userName: undefined,
                    nickName: undefined,
                    deptId: undefined,
                    status: 0,
                },
                tableTotal: 0,
                deptOptions: [],
            };
        },
      watch: {
        "queryTableParams.userName"(val) {
          this.handleQuery();
        },
        "queryTableParams.nickName"(val) {
          this.handleQuery();
        },
        "queryTableParams.deptId"(val) {
          this.handleQuery();
        }
      },
      created() {
        this.getDeptList()
      },
      methods: {
            init (title,source,index,selected,isMulti) {
                let _this = this
                _this.source = source
                _this.index = index
                _this.selected = selected?JSON.parse(JSON.stringify(selected)):[]
                _this.rest()
                _this.getListData()
                _this.drawerTitle = title
                _this.isMulti = !!isMulti
                _this.showSelect = true
            },
            rest(){
                this.queryTableParams= {
                    pageNum: 1,
                    pageSize: 10,
                    userName: undefined,
                    nickName: undefined,
                    status: 0,
                }
            },
            handleQuery(){
                this.queryTableParams.pageNum = 1
                this.getListData()
            },
            selectHandle(row,index){
              this.selected = [{userName: row.userName, nickName: row.nickName,deptId: row.dept.deptId, deptName: row.dept.deptName}]
            },
            selectable(){
                return this.isMulti
            },
            getListData() {
                let _this = this
                listUser(this.queryTableParams).then(res=>{
                    _this.listData = res.rows
                    _this.tableTotal = res.total
                })
            },
            submit(){
                let _this = this
                let selected = []
                _this.selected.forEach(item=>{
                  let deptId = item.deptId?item.deptId:item.dept?item.dept.deptId:''
                  let deptName = item.deptName?item.deptName:item.dept?item.dept.deptName:''
                    selected.push({
                        userName: item.userName,
                        nickName: item.nickName,
                        deptId: deptId,
                        deptName: deptName,
                    })
                })
              _this.$emit("selectHandle",_this.source,_this.index,JSON.parse(JSON.stringify(selected)));
              _this.showSelect = false
            },
          normalizer(node) {
            if (node.children && !node.children.length) {
              delete node.children;
            }
            return {
              id: node.deptId,
              label: node.deptName,
              children: node.children,
            };
          },
          getDeptList(){
            listDept({ status: 0}).then((response) => {
              this.deptOptions = this.handleTree(response.data, "deptId");
            });
          },
        }
    }
</script>

<style scoped>

</style>
