<template>
  <div class="pre-select-users-table">
    <el-card class="gray-card table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        :empty-text="$t('workflow.no_logs') "
      >
        <el-table-column
          :label="$t('workflow.process_node') "
          prop="nodeName"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          :label="$t('workflow.executor')"
          prop="nickName"
          align="center"
          show-overflow-tooltip
        />
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { getPreSelectUsers } from '@/api/file_processing/modifiyApply'

export default {
  name: 'PreSelectUsersTable',
  props: {
    applyId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      tableData: []
    }
  },
  watch: {
    applyId: {
      handler(newVal) {
        if (newVal) {
          this.fetchData()
        }
      },
      immediate: true
    }
  },
  methods: {
    async fetchData() {
      if (!this.applyId) return

      this.loading = true
      try {
        const response = await getPreSelectUsers(this.applyId)
        if (response.code === 200) {
          this.tableData = response.data || []
        } else {
          this.$message.error(response.msg || this.$t('workflow.fetch_error'))
          this.tableData = []
        }
      } catch (error) {
        console.error('Failed to fetch pre-select users:', error)
        this.$message.error(this.$t('workflow.fetch_error'))
        this.tableData = []
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.pre-select-users-table {
  margin-top: 20px;
}

.gray-card {
  background-color: #f8f9fa;
}

.table-card .el-card__header {
  padding: 15px 20px;
  border-bottom: 1px solid #e6ebf5;
  background-color: #fafafa;
}

.table-card .el-card__body {
  padding: 0;
}

.el-table {
  border-radius: 0;
}

.el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}
</style>
