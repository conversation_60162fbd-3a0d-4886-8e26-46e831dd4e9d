<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="4" style="border: 1px" v-if="dataType === 'project'">
        <div>{{ $t('sys_mgr.dispose_project_file_type') }}</div>
        <el-tree ref="tree" :data="treeList" :props="treeProps" loadOptions="" node-key="id" @node-click="nodeClick"
                 style="border: 0px solid rgb(230, 235, 245); min-height: 282px">
        </el-tree>
      </el-col>
      <el-col :span="dataType === 'project' ? 20 : 24">
        <div class="el-card__header" v-if="dataType === 'project'">
          <div slot="header" class="clearfix">
            <span>{{ currNodeDesc == null ? '' : currNodeDesc }}</span>
          </div>
        </div>
        <el-form :model="queryParams" ref="queryForm" label-width="68px">
          <div class="global-ser" id="add">
            <div class="ser-top">
              <treeselect v-model="queryParams.docClass" :options="classLevelOptions" :normalizer="normalizer" :default-expand-level="1"  :disable-branch-nodes="true"
                          :show-count="true" :searchable="false" :placeholder="$t('doc.this_dept_select_type')" :disabled="disabled"
                          style="width: 200px; float: left; margin-right: 10px;font-size:14px;" v-if="dataType !== 'project'" />
              <el-input v-model.trim="queryParams.searchValue" :placeholder="$t('doc.this_dept_select_type')" :title="$t('doc.this_dept_select_type')"
                        @keyup.enter.native="handleQuery" class="input-with-select" style="width: 200px">
              </el-input>
              <el-select :title="$t('doc.this_dept_category_data')" v-model="queryParams.classType" :placeholder="$t('doc.this_dept_category_data')" style="width: 100px" @change="handleQuery">
                <el-option :label="$t('doc.this_dept_all')" value=""></el-option>
                <!--                <el-option label="体系文件" value="DOC"></el-option>-->
                <!--                <el-option label="记录文件" value="RECORD"></el-option>-->
                <!--                <el-option label="外来文件" value="FOREIGN"></el-option>-->
                <el-option
                  v-for="(item,index) in dict.type.classify_data"
                  :key="index"
                  :label="dictLanguage(item)"
                  :value="item.value"
                  :disabled="item.disabled"
                ></el-option>
              </el-select>
              <el-select :title="$t('myItem.borrow_file_status')" v-model="queryParams.status" :placeholder="$t('myItem.borrow_file_status')" style="width: 100px" @change="handleQuery">
                <el-option :label="$t('doc.this_dept_all')" value=""></el-option>
                <!--                <el-option label="草稿" value="0"></el-option>-->
                <!--                <el-option label="有效" value="1"></el-option>-->
                <!--                <el-option label="失效" value="2"></el-option>-->
                <el-option
                  v-for="(item,index) in dict.type.standard_status"
                  :key="index"
                  :label="dictLanguage(item)"
                  :value="item.value"
                  :disabled="item.disabled"
                ></el-option>
              </el-select>
              <el-button slot="append" @click="handleQuery"> {{ $t('doc.this_dept_query') }} </el-button>

              <el-button v-hasPermi="['history:import:fileDir']" @click="handleuploadFile">
                {{ $t('doc.this_dept_upload_folder') }}
              </el-button>
              <el-button v-hasPermi="['history:import:fileDir']" @click="handleExport">
                {{ $t('doc.this_dept_export_temp') }}
              </el-button>
              <el-button v-hasPermi="['history:import:fileDir']" @click="handlestandardimport">
                {{ $t('doc.this_dept_batch_edit_upload') }}
              </el-button>
              <el-button v-hasPermi="['history:import:fileDir']" @click="handlestandardUpdateStatus" :disabled="single && multiple" v-loading="updateStatusLoading">
                {{ $t('doc.this_dept_batch_effc') }}
              </el-button>
              <el-button v-hasPermi="['history:import:fileDir']" @click="handlestandardDelete" :disabled="single && multiple">
                {{ $t('doc.this_dept_batch_delete') }}
              </el-button>
              <!--    上传-->
              <input v-show="false" ref="fileRef" id="fileFolder" type="file" @change="fileChange" webkitdirectory
                     multiple="multiple" />
              <!--批量修改上传-->
              <input v-show="false" ref="filetandardimport" type="file" @change="handlestandardimportfileChange" />
              <!--替换文件-->
              <input v-show="false" ref="replaceFile" type="file" @change="handlestandardReplaceFileChange" />
            </div>
          </div>
        </el-form>
        <el-alert
          :title="$t('doc.this_dept_rule_tip')"
          type="warning"
          :description="$t('doc.this_dept_file_text')"
          show-icon>
        </el-alert>
        <el-card class="gray-card">
          <el-table v-loading="loading" ref="singleTable" :data="postList" @selection-change="handleSelectionChange"
                    highlight-current-row @current-change="handleCurrentChange" header-align="left">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="ID" align="left" prop="id" :show-overflow-tooltip="true" />
            <el-table-column :label="$t('doc.this_dept_category_data')" align="left" prop="classType">
              <template slot-scope="scope">
                <span v-if="scope.row.classType == 'DOC' ">{{ $t('doc.this_dept_sys_file') }}</span>
                <span v-else-if="scope.row.classType == 'RECORD' ">{{ $t('doc.this_dept_record_file') }}</span>
                <span v-else-if="scope.row.classType == 'FOREIGN' ">{{ $t('doc.external_file') }}</span>
                <span v-else >{{ $t('file_set.number_null') }}</span>
              </template>
            </el-table-column>

            <el-table-column :label="$t('myItem.borrow_file_name')" align="left" prop="docName" width="200" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <span class="wenjcolor">{{ scope.row.docName }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('doc.this_dept_file_extension')" align="left" prop="fileType">
            </el-table-column>
            <el-table-column :label="$t('myItem.borrow_file_id')" align="left" prop="docId">
            </el-table-column>

            <el-table-column :label="$t('myItem.borrow_file_ver')" align="left" prop="versionValue" />

            <el-table-column :label="$t('myItem.borrow_file_type')" align="left" prop="docClass">
              <template slot-scope="scope">
                {{ handleFileType(scope.row.docClass) }}
              </template>
            </el-table-column>

            <el-table-column :label="$t('doc.this_dept_record_time')" :show-overflow-tooltip="true" align="left" prop="startDate">
              <template slot-scope="scope">
                <span>{{
                  parseTime(scope.row.applyTime, "{y}-{m}-{d}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('doc.this_dept_effective_date')" align="left" prop="startDate">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.startDate, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('myItem.borrow_file_status')" align="left" prop="status">
              <template slot-scope="scope">
                <span v-show="scope.row.versionValue != null && scope.row.status == 0">{{ $t('doc.this_dept_draft') }}</span>
                <span v-show="scope.row.versionValue != null && scope.row.status == 1">{{ $t('doc.this_dept_validity') }}</span>
                <span v-show="scope.row.versionValue != null && scope.row.status == 2">{{ $t('doc.this_dept_Invalid') }}</span>
                <span v-show="scope.row.versionValue == null ">{{ $t('file_set.number_null') }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('myItem.msg_operation')" align="left" width="200" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="handleDetails(scope.row, $t('doc.this_dept_detail'))">{{ $t('doc.this_dept_detail') }}
                </el-button>
                <!-- 排除有效和作废的 -->
                <el-button v-if="scope.row.versionValue != null" size="mini" type="text" @click="handleDetails(scope.row, $t('doc.this_dept_edit'))" v-hasPermi="['history:import:update']">{{ $t('doc.this_dept_edit') }}
                </el-button>
                <!-- v-if="scope.row.status !== '1' && scope.row.status !== '2'" -->
                <el-button size="mini" type="text" @click="handleDelete(scope.row)" v-hasPermi="['history:import:delete']">{{ $t('doc.this_dept_delete') }}
                </el-button>
                <!-- v-if="scope.row.versionValue != null" -->
                <el-button size="mini" type="text" @click="handlestandardReplaceFile(scope.row)" v-hasPermi="['history:import:replace']">{{ $t('doc.this_dept_replace_file') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
          :page-sizes="[5, 10, 30, 40, 50, 100, 1000]" @pagination="getList" />

        <div style="display: flex">
          <div :style="activeName1 === 'first' ? 'width: 50%; padding-right: 5px' : 'width: 100%; padding-right: 5px'">
            <el-tabs v-model.trim="activeName1" class="out-tabs" @tab-click="linkstate">
              <!-- 关联记录 -->
              <el-tab-pane v-if="linkRecordTabPaneFlag" name="second">
                <span slot="label">{{ $t('doc.this_dept_related_record') }}</span>
                <div class="global-ser" id="add">
                  <div class="ser-top">
                  </div>
                </div>
                <el-card class="gray-card">
                  <el-table style="height:350px;" v-loading="linkloading" :data="linkpostList" header-align="left">
                    <el-table-column :label="$t('myItem.borrow_file_name')" align="left" width="200" :show-overflow-tooltip="true">
                      <template slot-scope="scope">
                        <span class="wenjcolor">{{ scope.row.fileName }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('myItem.borrow_file_id')" align="left" prop="linkCode" :show-overflow-tooltip="true">
                    </el-table-column>
                    <el-table-column :label="$t('myItem.borrow_file_ver')" align="left" prop="versionValue" />
                    <el-table-column :label="$t('myItem.borrow_file_type')" align="left" prop="docClass">
                      <template slot-scope="scope">
                        {{ handleFileType(scope.row.docClass) }}
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('myItem.msg_operation')" align="left" class-name="small-padding fixed-width" fixed="right">
                      <template slot-scope="scope">
                        <el-button size="mini" type="text" @click="handlelinkLogDelete(scope.row)"  v-hasPermi="['history:import:link:delete']">{{ $t('doc.this_dept_delete') }}
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-card>
                <pagination v-show="linktotal > 0" :total="linktotal" :page.sync="linkqueryParams.pageNum"
                  :limit.sync="linkqueryParams.pageSize" :page-sizes="[5, 10, 30, 40, 50, 100, 1000]"
                  @pagination="getlinkLogList" />
              </el-tab-pane>
              <!-- 关联文件 -->
              <el-tab-pane v-if="linkDocTabPaneFlag" name="first" >
                <span slot="label">{{ $t('doc.this_dept_related_file') }}</span>
                <el-card class="gray-card">
                  <el-table style="height:350px;" v-loading="linkloading" :data="linkpostList" header-align="left">
                    <el-table-column :label="$t('myItem.borrow_file_name')" align="left" width="200" :show-overflow-tooltip="true">
                      <template slot-scope="scope">
                        <span class="wenjcolor">{{ scope.row.fileName }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('myItem.borrow_file_id')" align="left" prop="linkCode" :show-overflow-tooltip="true">
                    </el-table-column>
                    <el-table-column :label="$t('myItem.borrow_file_ver')" align="left" prop="versionValue" />
                    <el-table-column :label="$t('myItem.borrow_file_type')" align="left" prop="docClass">
                      <template slot-scope="scope">
                        {{ handleFileType(scope.row.docClass) }}
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('myItem.msg_operation')" align="left" class-name="small-padding fixed-width" fixed="right">
                      <template slot-scope="scope">
                        <el-button size="mini" type="text" @click="handlelinkLogDelete(scope.row)"  v-hasPermi="['history:import:link:delete']">{{ $t('doc.this_dept_delete') }}
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-card>
                <pagination v-show="linktotal > 0" :total="linktotal" :page.sync="linkqueryParams.pageNum"
                            :limit.sync="linkqueryParams.pageSize" :page-sizes="[5, 10, 30, 40, 50, 100, 1000]"
                            @pagination="getlinkLogList" />
              </el-tab-pane>
            </el-tabs>
          </div>
          <div style="width: 50%;padding-left: 5px" >
            <el-tabs v-model.trim="activeName2" class="out-tabs">
              <el-tab-pane v-if="linkRecordTabPaneFlag || linkDocTabPaneFlag" name="0">
                <span slot="label">{{ $t('sys_mgr.dispose_double_click') }}</span>
                <el-form :model="sjiParams" ref="queryForm" label-width="68px">
                  <div class="global-ser" id="add">
                    <div class="ser-top">
                      <treeselect v-model="sjiParams.docClass"
                                  :options="linkClassLevelOptions"
                                  :normalizer="normalizer"
                                  :disable-branch-nodes="true"
                                  :show-count="true"
                                  :appendToBody="true"
                                  z-index="99999"
                                  :searchable="false"
                                  :placeholder="$t('doc.this_dept_select_type')"
                                  :disabled="disabled"
                                  style="width: 200px; float: left; margin-right: 10px;font-size:14px;"
                                  v-if="dataType !== 'project'" />
                      <el-input v-model.trim="sjiParams.searchValue" :placeholder="$t('doc.this_dept_select_type')"
                                @keyup.enter.native="sjihandleQuery" class="input-with-select" style="width: 250px">
                        <el-button slot="append" icon="el-icon-search" @click="sjihandleQuery"></el-button>
                      </el-input>
                    </div>
                  </div>
                </el-form>
                <el-card class="gray-card">
                  <el-table style="height:300px;" v-if="postList.length > 0" v-loading="sjiloading" :data="sjipostList" header-align="left" @row-dblclick="rowDblclick">
                    <el-table-column :label="$t('myItem.borrow_file_name')" align="left" width="200" :show-overflow-tooltip="true">
                      <template slot-scope="scope">
                        <span class="wenjcolor">{{ scope.row.docName }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('myItem.borrow_file_id')" align="left" prop="docId" :show-overflow-tooltip="true">
                    </el-table-column>

                    <el-table-column :label="$t('myItem.borrow_file_ver')" align="left" prop="versionValue" />

                    <el-table-column :label="$t('myItem.borrow_file_type')" align="left" prop="deptName">
                      <template slot-scope="scope">
                        {{ handleFileType(scope.row.docClass) }}
                      </template>
                    </el-table-column>
                  </el-table>
                </el-card>
                <pagination v-show="sjitotal > 0" :total="sjitotal" :page.sync="sjiParams.pageNum"
                  :limit.sync="sjiParams.pageSize" :page-sizes="[5, 10, 30, 40, 50, 100, 1000]" @pagination="getsjiLogList" />
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-col>
    </el-row>
    <div v-if="progressShow" class="mask"></div>
    <div v-if="progressShow" class="maskprogress">
      <div style="
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;
        ">
        <el-progress type="dashboard" :percentage="percentage" :color="colors"></el-progress>
        <br />
        <div style="
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
          ">
          <div style="color: #ffff">{{ $t('sys_mgr.dispose_uploading') }}...</div>
          <br />
          <div style="color: #ffff">
            （{{ $t('sys_mgr.dispose_already_upload') }}{{ filesnumber }}/{{ $t('sys_mgr.dispose_total') }}{{ fileslength }}）
          </div>
        </div>
      </div>
    </div>

    <el-dialog :title="title" :visible.sync="dialogFormVisible" append-to-body width="80%"  :close-on-click-modal="false">
      <el-card class="gray-card table-card no-padding">
        <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-position="right" label-width="150px"
                 :disabled="title == $t('doc.this_dept_detail')">
          <el-row>
            <el-col :span="24">
              <el-form-item :label="getLabelWithColon($t('myItem.borrow_file_type'))" prop="docClass">
                <treeselect v-model.trim="formData.docClass" :options="classLevelOptions" :normalizer="normalizer"
                            :searchable="false" :show-count="true" :disabled="title == $t('doc.this_dept_detail')" :placeholder="$t('doc.this_dept_select_type')" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="getLabelWithColon($t('doc.this_dept_change_type'))" prop="changeType">
                <el-select v-model="formData.changeType" :placeholder="$t('doc.this_dept_pls_select')">
                  <!--                  <el-option v-for="item in changeTypeoptions" :key="item.value" :label="item.label" :value="item.value">-->
                  <!--                  </el-option>-->
                  <el-option
                    v-for="(item,index) in dict.type.change_type_options"
                    :key="index"
                    :label="dictLanguage(item)"
                    :value="item.value"
                    :disabled="item.disabled"
                  ></el-option>

                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item :label="getLabelWithColon($t('myItem.borrow_file_name'))" prop="docName">
                <div>
                  <el-input v-model.trim="formData.docName" :placeholder="$t('doc.this_dept_insert_name')" clearable>
                  </el-input>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="getLabelWithColon($t('myItem.borrow_file_id'))" prop="docId">
                <el-input v-model.trim="formData.docId" :placeholder="$t('sys_mgr.doc_input_num')" maxlength="20"
                          show-word-limit clearable>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <!-- 主文件 -->
              <el-form-item :label="getLabelWithColon($t('doc.this_dept_version_file'))">
                <div style="display: flex; justify-content: space-between">
                  <span style="color: #385bb4; margin-left: 10px; cursor: pointer" :key="i" v-if="formData.fileName != ''"
                    @click="handlePreview(formData.fileId)">{{ formData.fileName }}
                  </span>

                  <fileUpload  v-if="title == $t('doc.this_dept_edit')" v-hasPermi="['history:import:replace']" v-model.trim="docNamefileList" limit="1" :fileType="['docx', 'doc', 'xls', 'xlsx', 'pdf','ppt','pptx','ppts']"
                               :isfileType="true" :fileSize="100" :showTransition="false" :title="$t('doc.this_dept_replace_file')" />
                  <span v-hasPermi="['history:import:main_download']" v-if="title == $t('doc.this_dept_detail')" style="color: #385bb4; cursor: pointer; margin-left: 10px" @click="handelefileLocalDownload(formData.fileId, formData.fileName)">{{ $t('doc.this_dept_download') }}</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- 相关附件 -->
              <el-form-item :label="getLabelWithColon($t('doc.this_dept_annexes_ver'))">
                <div v-if="title == $t('doc.this_dept_detail')">
                  <div v-for="(item, i) in formData.preAppendixes" class="link-box bzlink-box" :key="i">
                    <span style="color: #385bb4; margin-left: 10px; cursor: pointer" :key="i"
                      @click="handlePreview(item.fileId)">{{ item.fileName }}
                    </span>
                    <span v-hasPermi="['history:import:other_download']" :key="i" style="color: #385bb4; cursor: pointer; margin-left: 10px" @click="
                      handelefileLocalDownload(item.fileId, item.fileName)
                      ">{{ $t('doc.this_dept_download') }}
                    </span>
                  </div>
                </div>
                <fileUpload :limit="100" v-else v-model.trim="appendixesfileList" :isShowTip="false" />
              </el-form-item>
            </el-col>

          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="getLabelWithColon($t('myItem.borrow_file_ver'))" prop="currentVersion">
                <el-input v-model.trim="formData.currentVersion" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="getLabelWithColon($t('doc.this_dept_staffing'))" prop="nickName">
                <div>
                  <el-input style="width: 80%; margin-right: 10px" readonly v-model.trim="formData.nickName" :placeholder="$t('sys_mgr.doc_select_staffing')">
                    <el-button slot="append" icon="el-icon-search" @click="handleSelect"></el-button>
                  </el-input>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="getLabelWithColon($t('doc.this_dept_effective_date'))" prop="startDate">
                <el-date-picker v-model="formData.startDate" type="date" value-format="yyyy-MM-dd HH:mm:ss"
                                :placeholder="$t('doc.this_dept_select_date')" :picker-options="pickerOptionsstartDate">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="getLabelWithColon($t('myItem.borrow_preparation_dept'))" prop="deptName">
                <el-input style="width: 80%; margin-right: 10px" readonly v-model.trim="formData.deptName" :placeholder="$t('sys_mgr.dispose_select_org_dept')">
                  <el-button slot="append" icon="el-icon-search" @click="selectionBoxInit('deptDataList',[formData],deptOptions,{single:true,title:$t('doc.this_dept_select_dept'),id:'deptId',label:'deptName',valueId:'deptId',valueLabel:'deptName',valueModel:{deptId:'',deptName:''}})"></el-button>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="getLabelWithColon($t('doc.this_dept_release_date'))" prop="releaseTime">
                <el-date-picker v-model="formData.releaseTime" type="date" value-format="yyyy-MM-dd HH:mm:ss"
                                :placeholder="$t('doc.this_dept_select_date')" :picker-options="pickerOptionsstartDate">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="getLabelWithColon($t('doc.this_dept_preparation_time'))" prop="applyTime">
                <el-date-picker v-model="formData.applyTime" type="date" value-format="yyyy-MM-dd HH:mm:ss"
                                :placeholder="$t('doc.this_dept_select_date')">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item :label="getLabelWithColon($t('myItem.borrow_file_status'))" prop="status">
                <el-select v-model="formData.status" :placeholder="$t('doc.this_dept_pls_select')">
                  <el-option
                    v-for="(item,index) in dict.type.standard_status"
                    :key="index"
                    :label="dictLanguage(item)"
                    :value="item.value"
                    :disabled="item.disabled"
                  ></el-option>
                </el-select>
                <span v-for="(itme, i) in appendixsList" :key="i">
                  {{ itme.docName }}
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="ID:" prop="id">
                {{ formData.id }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item :label="getLabelWithColon($t('doc.this_dept_file_lifespan'))">
                <el-date-picker v-model="formData.endDate" align="right" type="date" value-format="yyyy-MM-dd HH:mm:ss"
                                :placeholder="$t('doc.this_dept_select_date')" :picker-options="pickerOptions0">
                </el-date-picker>
                <span style="margin: 0px 5px">{{ $t('doc.this_dept_whether_permanent') }}</span>
                <el-radio-group v-model="formData.forever">
                  <!--                  <el-radio :label="'1'">{{ $t('doc.this_dept_yes') }}</el-radio>-->
                  <!--                  <el-radio :label="'0'">{{ $t('doc.this_dept_no') }}</el-radio>-->
                  <el-radio
                    v-for="(item, index) in dict.type.ledger_whether_origin"
                    :key="index"
                    :label="item.value"
                    :disabled="item.disabled"
                  >{{ dictLanguage(item) }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="getLabelWithColon($t('doc.this_dept_change_reason'))" prop="content">
                <el-input v-model.trim="formData.changeReason" type="textarea" :placeholder="$t('doc.this_dept_insert_change_reason')"
                          :autosize="{ minRows: 4, maxRows: 4 }" :style="{ width: '100%' }" maxlength="500"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="getLabelWithColon($t('doc.this_dept_changes'))" prop="content">
                <el-input v-model.trim="formData.content" type="textarea" :placeholder="$t('doc.this_dept_insert_change_content')"
                          :autosize="{ minRows: 4, maxRows: 4 }" :style="{ width: '100%' }" maxlength="500"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
      <div slot="footer" class="dialog-footer" v-if="title == $t('doc.this_dept_edit')">
        <el-button @click="dialogFormVisible = false">{{ $t('sys_mgr_log.operlog_close') }}</el-button>
        <el-button type="primary" v-loading="updateFileLoading"  @click="submitForm()">{{ $t('sys_mgr.dispose_save') }}</el-button>
      </div>
      <div slot="footer" class="dialog-footer" v-if="title == $t('doc.this_dept_detail')">
        <el-button @click="dialogFormVisible = false">{{ $t('sys_mgr_log.operlog_close') }}</el-button>
      </div>
    </el-dialog>

    <user-list ref="userList" @selectHandle="handleSubmitUser"></user-list>

    <el-dialog :title="$t('doc.this_dept_new_add')" :visible.sync="open2" width="800px" append-to-body>
      <el-form :model="recordForm" :rules="rules" ref="recordRules" label-width="100px" class="demo-ruleForm">
        <el-form-item :label="getLabelWithColon($t('myItem.borrow_file_name'))" prop="docName">
          <div style="display: flex">
            <el-input :disabled="!recordForm.fileId" v-model.trim="recordForm.docName" :placeholder="$t('doc.this_dept_select_or_upload')">
            </el-input>
            <el-upload :file-list="standardDocfileList" action="" :http-request="standardDocBeforeUpload">
              <el-button size="small" type="primary" icon="el-icon-upload">{{ $t('doc.this_dept_click_to_upload') }}
              </el-button>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
      <div class="rz-transfer">
        <el-button @click="open2 = false">{{ $t('file_set.type_cancel') }}</el-button>
        <el-button type="primary">{{ $t('file_set.type_confim') }}</el-button>
      </div>
    </el-dialog>
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="close">
    </as-pre-view>
    <selection-box ref="selectionBox" @selectHandle="selectBoxHandle"></selection-box>
  </div>
</template>
<script>
import {
  standardList,
  standardUpload,
  linkLogList,
  processstandard,
  standardUpdate,
  standarddelete,
  standardDeleteBatch,
  linkLogDelete,
  standardimport,
  standardUpdateStatus,
  standardReplaceFile,
  standardRecordFile,
  standardJoin,
  checkNoIsExist,
  linkLogUpdate,
  refreshDocAccount,
  testLocalUpload,
  testLocalDown, standardUpdateByParam
} from "@/api/system/standard";
import { listUser } from "@/api/system/user";
import { listDept } from '@/api/system/dept'
import { processFileLocalUpload } from "@/api/commmon/file";
import { settingDocClassList } from "@/api/file_settings/type_settings";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { treeSelect } from "@/api/system/project";
import mixin from "@/layout/mixin/Commmon.js";
import UserList from '@views/workflowList/addWorkflow/add_import/userList.vue'
import SelectionBox from '@viewscomponents/selectionBox/index.vue'
import cache from '@/plugins/cache'

export default {
  components: {
    UserList,
    Treeselect,
    SelectionBox
  },
  props: {
    'dataType': String,
    'docClass': String,
    'projectVal': String,
    'projectDesc': String,
  },
  dicts: ["change_factor","standard_status","classify_data","ledger_whether_origin","change_type_options"],
  mixins: [mixin],
  data() {
    return {
      userListqueryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: "",
      },
      // statusoptions: [
      //   {
      //     value: "0",
      //     label: "草稿",
      //   },
      //   {
      //     value: "1",
      //     label: "有效",
      //   },
      //   {
      //     value: "2",
      //     label: "失效",
      //   },
      // ],
      // changeTypeoptions: [
      //   {
      //     value: "ADD",
      //     label: "新增",
      //   },
      //   {
      //     value: "UPDATE",
      //     label: "修订",
      //   },
      // ],
      treeProps: {
        children: "children",
        label: "label",
      },
      treeList: [],
      dialogFormVisible: false,
      userListloading: false,
      userOpen: false,
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      percentage: 0,
      fileslength: 0,
      filesnumber: 0,
      changeFactor: [],
      docNamefileList: [],
      colors: [
        { color: "#007dff", percentage: 20 },
        { color: "#007dff", percentage: 40 },
        { color: "#007dff", percentage: 60 },
        { color: "#007dff", percentage: 80 },
        { color: "#007dff", percentage: 100 },
      ],
      nowDate: new Date(),
      // 遮罩层
      loading: true,
      progressShow: false,
      postList: [],
      // 文件台账分类查询
      classLevelOptions: [],
      // 关联文件分类查询
      linkClassLevelOptions: [],
      activeName: "1",
      // 默认展示关联记录页签
      activeName1: "second",
      // 是否显示 关联记录页签
      linkRecordTabPaneFlag : false,
      // 是否显示 关联文件页签
      linkDocTabPaneFlag : false,
      activeName2: 0,
      userListtotal: 0,
      formData: {
        fileName: "",
        forever: undefined,
        appendixes: []
      },
      rules: {
        docClass: [{ required: true, message: this.$t('doc.this_dept_pls_select'), trigger: "blur" }],
        docName: [{ required: true, message: this.$t('doc.this_dept_pls_fill'), trigger: "blur" }],
        currentVersion: [
          { required: true, message: this.$t('doc.this_dept_pls_fill'), trigger: "blur" },
        ],
        changeReason: [{ required: true, message: this.$t('doc.this_dept_pls_fill'), trigger: "blur" }],
        docId: [{ required: true, message: this.$t('doc.this_dept_pls_fill'), trigger: "blur" }],
        content: [{ required: true, message: this.$t('doc.this_dept_pls_fill'), trigger: "blur" }],
        versionValue: [{ required: true, message: this.$t('doc.this_dept_pls_fill'), trigger: "blur" }],
        changeFactor: [{ required: true, message: this.$t('doc.this_dept_pls_select'), trigger: "blur" }],
        changeType: [
          {
            required: true,
            message: this.$t('doc.this_dept_pls_select'),
            trigger: "blur",
          },
        ],
        startDate: [
          {
            required: true,
            message: this.$t('doc.this_dept_pls_select'),
            trigger: "blur",
          },
        ],
        releaseTime: [
          {
            required: true,
            message: this.$t('doc.this_dept_pls_select'),
            trigger: "blur",
          },
        ],

        reviewTime: [
          {
            required: true,
            message: this.$t('doc.this_dept_pls_select'),
            trigger: "blur",
          },
        ],
        field108: [
          {
            required: true,
            message: this.$t('sys_mgr.dispose_radio_not_null'),
            trigger: "blur",
          },
        ],
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 5,
        initFile: 1,
        status: "",
        searchValue: undefined,
        docClass: undefined,
        classType: "",
      },
      // 查询参数
      linkqueryParams: {
        pageNum: 1,
        pageSize: 5,
        linkType: "RECORD",
        standardId: "-1", // 默认查不出关联数据
      },
      // 双击选择列表参数
      sjiParams: {
        pageSize: 5,
        pageNum: 1,
        docClass: undefined,
        searchValue: "",
        status: "1",
      },
      linkloading: false,
      linktotal: 0,
      linkpostList: [],
      sjiloading: false,
      sjitotal: 0,
      // 待关联文件清单
      sjipostList: [],
      title: "",
      selectRadio: "",
      selectionUser: {},
      selection: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      replaceFileId: undefined,
      currentRow: null,
      pickerOptions0: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7; //如果没有后面的-8.64e7就是不可以选择今天的
        },
      },
      pickerOptionsstartDate: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6; //如果没有后面的-8.64e6就是不可以选择今天的
        },
      },
      cgnum: 0,
      sbnum: 0,
      sbnummsg: "",
      currNodeDesc: '',
      appendixesfileList: [],
      open2: false,
      recordForm: {
        docName: undefined,
        versionValue: undefined,
        docId: undefined,
        linkClass: undefined,
        linkId: undefined,
        docClass: undefined,
      },
      // 表单校验
      recordRules: {
        docName: [{ required: true, trigger: "blur", message: this.$t('doc.this_dept_insert') }],
        docClass: [{ required: true, trigger: "blur", message: this.$t('doc.this_dept_pls_select') }],
      },
      standardDocfileList: [],
      recordLoading: false,
      updateStatusLoading:false,
      updateFileLoading:false,
      deptOptions: [],
      deptList: [],
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  computed: {},
  watch: {
    docNamefileList(val) {
      if (val != "") {
        this.formData.fileId = val[0].url;
        this.formData.fileName = val[0].name;
        this.docNamefileList = [];
      }
    },
    "formData.forever"(val) {
      if (val == 1) {
        this.formData.endDate = "";
      }
    },
    changeFactor(val) {
      this.formData.changeFactor = val.join(",");
    },
    "queryParams.docClass"(val) {
      // 体系文件-文件类型选择，触发主列表查询
      this.handleQuery()
    },
    /* */
    "sjiParams.docClass"(val) {
      // 待关联文件清单的文件类型选择，触发查询
      this.sjihandleQuery()
    },
  },
  created() {
    this.initPage();
    this.getDeptList();
  },
  mounted() {
    this.queryParams.docClass = this.docClass
    if (this.projectVal) {
      this.initPriject(this.projectVal)
      this.currNodeDesc = this.projectDesc
    } else {
      this.getList();
      this.getsjiLogList();
    }
  },
  methods: {
    selectionBoxInit(label,selectedList,dataList,settings) {
      this.$refs.selectionBox.init(label,undefined,selectedList,dataList,settings,this.deptList)
    },
    // 部门选择组件-结果提交回调函数
    selectBoxHandle(label,index,selectedList,valueList) {
      this.$set(this.formData,"deptId", valueList[0].deptId)
      this.$set(this.formData,"deptName", valueList[0].deptName)
    },
    // 部门选择组件-数据源
    getDeptList(){
      // deptLevel = 2 只显示组织层级2级以内的节点
      listDept({ status: 0 }).then((response) => {
        this.deptList = JSON.parse(JSON.stringify(response.data))
        this.deptOptions = this.handleTree(response.data, "deptId");
      });
    },
    initPage() {
      this.loadTreeList()
      this.getclassLevelOptions()
    },
    // 刷新文件台账数据
    refreshDocAccount(ids) {
      this.loading = true;
      refreshDocAccount(ids).then(response => {
        console.log(response)
        this.loading = false;
      });
    },
    handleAdd() {
      if (this.linkpostList.length > 19) {
        this.$modal.msg(this.$t('sys_mgr.dispose_text1'));
      } else {
        this.open2 = true;
      }
    },
    initPriject(val) {
      let arr = val.split("_")
      let projectId = arr[0]
      let classId = arr[1]
      // 设置参数：项目文件类型
      this.queryParams.docClass = classId;
      this.queryParams.projectId = projectId;
      // this.sjiParams.docClass = classId;
      // this.sjiParams.projectId = projectId;
      this.getList();
      this.getsjiLogList();
    },
    //初始化项目
    loadTreeList() {
      // 加载左侧项目文件类型树
      this.loading = true;
      treeSelect().then(response => {
        if(response.data) {
          response.data.forEach(project => {
            project.children = project.children.filter(
              (item) => (item.classId != "PROJECT-R")
            );
          });
        }
        this.treeList = response.data;
        this.loading = false;
      });
    },
    //项目点击事件
    nodeClick(val) {
      this.$refs.tree.setCurrentKey(val)
      const selected = this.$refs.tree.getCurrentNode()
      let currNode = this.$refs.tree.getNode(selected)
      if (currNode != undefined && currNode.parent.data.label != undefined) {
        // 点击项目文件类型
        this.currNodeDesc = currNode.parent.data.label + '>' + currNode.data.label
      } else if (currNode != undefined) {
        // 点击项目
        this.currNodeDesc = currNode.data.label
      }
      this.initPriject(val.id)
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      this.queryParams.dataType = this.dataType
      this.postList = []
      standardList(this.queryParams).then((response) => {
        this.postList = response.rows;
        if (this.currentRow == null) {
          this.setCurrent(this.postList[0]);
        }
        this.total = response.total;
        this.loading = false;
        // 获取列表后默认选择第一条，同时需要更新 关联记录和关联文件 页签
        if(this.currentRow != null && this.currentRow.classType == "DOC") {
          // 默认展示 关联记录页签
          this.activeName1 = "second"
          this.linkstate({index:0})
        }
      });
    },
    setCurrent(row) {
      this.$refs.singleTable.setCurrentRow(row);
    },
    handleCurrentChange(val) {
      // 行点击事件
      // 默认不显示关联页签、待关联文件查询条件置空
      this.linkRecordTabPaneFlag = false
      this.linkDocTabPaneFlag = false
      this.linkClassLevelOptions = []
      this.link
      if (val != null) {
        this.currentRow = val;
        this.linkqueryParams.standardId = val.id;

        if(val.status == 0) {
          // 草稿不允许关联
          return false;
        }
        if (this.currentRow.classType == 'DOC') {
          // 行记录的分类是 体系文件台账
          // 默认展示 关联记录、关联文件页签
          this.linkRecordTabPaneFlag = true
          this.linkDocTabPaneFlag = true
          // 优先显示 关联记录
          this.activeName1 = "second"
          this.linkstate({index:0})
        }
      }
    },
    /** 查询关联列表 */
    getlinkLogList() {
      this.linkloading = true;
      linkLogList(this.linkqueryParams).then((response) => {
        this.linkpostList = response.rows;
        this.linktotal = response.total;
        this.linkloading = false;
        // 查询待关联文件列表
        this.getsjiLogList();
      });
    },
    /** 待关联文件列表 */
    getsjiLogList() {
      this.sjiloading = true;
      if (this.activeName1 == "first") {
        this.sjiParams.linkType = this.linkqueryParams.linkType;
        this.sjiParams.standardId = this.linkqueryParams.standardId;
        this.sjiParams.isHasUpVersionId = this.sjiParams.linkType == "RECORD" ? "N" : ""
      }

      this.sjiParams.dataType = this.dataType
      // 关联记录页签， 待关联记录清单需要筛选没有被关联过的记录台账；
      if (this.activeName1 == "second") {
        this.sjiParams.isHasUpVersionId = "N";
        this.sjiParams.linkType = '';
        this.sjiParams.standardId = '';
      }
      // this.sjiParams.classType = "RECORD"
      standardList(this.sjiParams).then((response) => {
        this.sjipostList = this.arrayRepeat(this.linkpostList, response.rows);
        this.sjitotal = response.total;
        this.sjiloading = false;
      });
    },
    /**
     * 两个数组对象去复
     * @param {*} array1
     * @param {*} array2
     */
    arrayRepeat(array1, array2) {
      var result = [];
      for (var i = 0; i < array2.length; i++) {
        var obj = array2[i];
        var num = obj.id;
        var isExist = false;
        for (var j = 0; j < array1.length; j++) {
          var aj = array1[j];
          var n = aj.linkCode;
          if (n === num) {
            isExist = true;
            break;
          }
        }
        if (!isExist) {
          result.push(obj);
        }
      }
      return result;
    },

    handleuploadFile() {
      if (this.queryParams.docClass == undefined) {
        this.$modal.msg(this.dataType === 'project' ? this.$t('sys_mgr.dispose_select_file_type') : this.$t('doc.this_dept_select_type'));
      } else {
        document.getElementById("fileFolder").value = null;
        this.$refs.fileRef.dispatchEvent(new MouseEvent("click"));
      }
    },
    async fileChange(e) {
      this.cgnum = 0;
      this.sbnum = 0;
      this.percentage = 0;
      this.sbnummsg = "";
      let files = e.target.files;
      this.fileslength = files.length;
      this.filesnumber = 0;
      let filesper = Math.floor( 100 / files.length * 100)/100;
      this.progressShow = true;
      if (this.fileslength == 0) {
        this.progressShow = false;
        this.$modal.msg(this.$t('sys_mgr.dispose_upload_text',[0,0]));
      }
      /*
      if (this.fileslength > 30) {
        this.progressShow = false;
        this.$modal.msg("单次上传文件不能超过30个,目前文件个数="+this.fileslength);
        return false
      }*/
      for (const file of files) {
        await this.doAsyncOperation(file, filesper);
      }
      this.$refs.fileRef.value = null;
    },
    async doAsyncOperation(file, filesper) {
      return new Promise((resolve, reject) => {
        // 异步操作代码
        var testmsg = file.name.substring(
          file.name.lastIndexOf(".") + 1
        ).toLowerCase();
        if (
          testmsg != "docx" &&
          testmsg != "doc" &&
          // testmsg != "pdf" &&
          testmsg != "xls" &&
          testmsg != "xlsx" &&
          testmsg != "ppt" &&
          testmsg != "pptx" &&
          testmsg != "pdf"
        ) {
          this.sbnum = this.sbnum + 1;
          this.sbnummsg = this.sbnummsg + "【"+file.name+"】,"+this.$t('sys_mgr.dispose_rule_text')+"<br/>";
          this.progressShowClose(filesper)
          resolve()
          return
        }
        let formDatafile = new FormData();
        formDatafile.append("file", file);
        formDatafile.append("docClass", this.queryParams.docClass);
        formDatafile.append("dataType", this.dataType);
        if (this.dataType === 'project') {
          formDatafile.append("projectId", this.queryParams.projectId);
        }
        standardUpload(formDatafile)
          .then((res) => {
            if (res.data != null && res.data == true) {
              this.cgnum = this.cgnum + 1;
            }
            this.progressShowClose(filesper)
            resolve()
          })
          .catch((error) => {
            this.sbnum = this.sbnum + 1;
            this.sbnummsg = this.sbnummsg + error + "<br/>";
            this.progressShowClose(filesper)
            resolve()
          });
      });
    },
    handleUploadRecord() {
      document.getElementById("fileFolderRecord").value = null;
      this.$refs.fileRefRecord.dispatchEvent(new MouseEvent("click"));
    },
    async recordChange(e){
      this.cgnum = 0;
      this.sbnum = 0;
      this.percentage = 0;
      this.sbnummsg = "";
      let files = e.target.files;
      this.fileslength = files.length;
      this.filesnumber = 0;
      let filesper = Math.floor( 100 / files.length * 100)/100;
      this.progressShow = true;
      if (this.fileslength == 0) {
        this.progressShow = false;
        this.$modal.msg(this.$t('sys_mgr.dispose_upload_text',[0,0]));
      }
      for (const file of files) {
        await this.doAsyncRecordOperation(file, filesper);
      }
      this.getlinkLogList();
      this.$refs.fileRefRecord.value = null;
    },
    async doAsyncRecordOperation(file, filesper) {
      return new Promise((resolve, reject) => {
        // 异步操作代码
        var testmsg = file.name.substring(
          file.name.lastIndexOf(".") + 1
        ).toLowerCase();
        if (
          testmsg != "docx" &&
          testmsg != "doc" &&
          // testmsg != "pdf" &&
          testmsg != "xls" &&
          testmsg != "xlsx" &&
          testmsg != "ppt" &&
          testmsg != "pptx" &&
          testmsg != "pdf"
        ) {
          this.sbnum = this.sbnum + 1;
          this.sbnummsg = this.sbnummsg + "【"+file.name+"】,"+this.$t('sys_mgr.dispose_rule_text')+"<br/>";
          this.progressShowClose(filesper)
          resolve()
        }
        let standardDoc = new FormData();
        standardDoc.append("file", file); //传文件
        standardDoc.append("id", this.currentRow.id);
        standardDoc.append("versionId", this.currentRow.versionId);
        standardRecordFile(standardDoc) .then((res) => {
          if (res.data != null && res.data == true) {
            this.cgnum = this.cgnum + 1;
          }
          this.progressShowClose(filesper)
          resolve()
        }).catch((error) => {
          this.sbnum = this.sbnum + 1;
          this.sbnummsg = this.sbnummsg + error + "<br/>";
          this.progressShowClose(filesper)
          resolve()
        });
      });
    },
    fileChangeOld(e) {
      this.cgnum = 0;
      this.sbnum = 0;
      this.sbnummsg = "";
      let _this = this;
      let files = e.target.files;
      this.fileslength = files.length;

      this.filesnumber = 0;
      let filesper = 100 / (files.length + 1);

      this.progressShow = true;
      if (this.fileslength == 0) {
        this.progressShow = false;
        this.$modal.msg(this.$t('sys_mgr.dispose_upload_text'));
      }
      for (var i = 0; i < files.length; i++) {
        let file = e.target.files[i];
        var testmsg = file.name.substring(
          file.name.lastIndexOf(".") + 1
        ).toLowerCase();
        if (
          testmsg != "docx" &&
          testmsg != "doc" &&
          // testmsg != "pdf" &&
          testmsg != "ppt" &&
          testmsg != "pptx" &&
          testmsg != "xls" &&
          testmsg != "xlsx"
        ) {
          this.sbnum = this.sbnum + 1;
          this.sbnummsg = this.sbnummsg + "【"+file.name+"】,"+this.$t('sys_mgr.dispose_rule_text')+"<br/>";
          this.filesnumber = this.filesnumber + 1;
          _this.progressShowClose(filesper)
          continue;
        }

        let index = i;
        let formDatafile = new FormData();
        formDatafile.append("file", file);
        formDatafile.append("docClass", this.queryParams.docClass);
        formDatafile.append("dataType", this.dataType);
        if (_this.dataType === 'project') {
          formDatafile.append("projectId", this.queryParams.projectId);
        }
        standardUpload(formDatafile)
          .then((res) => {
            if (res.data != null && res.data == true) {
              this.cgnum = this.cgnum + 1;
            }
            this.filesnumber = this.filesnumber + 1;
            _this.progressShowClose(filesper)
            this.percentage = this.percentage + filesper * (index + 1);
            if (this.percentage > 100) {
              this.percentage = 100;
            }
          })
          .catch((error) => {
            this.sbnum = this.sbnum + 1;
            this.sbnummsg = this.sbnummsg + error + "<br/>";
            this.filesnumber = this.filesnumber + 1;
            _this.progressShowClose(filesper)
          });
      }
      this.$refs.fileRef.value = null;
    },
    progressShowClose(filesper) {
      this.filesnumber = this.filesnumber + 1;
      this.percentage =Math.floor((this.percentage + filesper)*100)/100;
      if (this.percentage > 100) {
        this.percentage = 100;
      }
      if (this.filesnumber == this.fileslength) {
        this.progressShow = false;
        if (this.sbnum == 0) {
          this.$message.success(this.$t('sys_mgr.dispose_upload_complete') +
            this.cgnum +
            "个");
        } else {
          this.$message({
            showClose: true,
            dangerouslyUseHTMLString: true,
            duration: 0,
            message:
              this.$t('sys_mgr.dispose_upload_complete') +
              this.cgnum +
              "个，未完成" +
              this.sbnum +
              "个<br/>" +
              this.sbnummsg,
            type: "warning",
          });
        }
        document.getElementById("fileFolder").value = null;
        this.cgnum = 0;
        this.sbnum = 0;
        this.sbnummsg = "";
        this.getList();
        this.progressShow = false;
      }
    },

    handlestandardimport() {
      this.$refs.filetandardimport.dispatchEvent(new MouseEvent("click"));
    },
    handlestandardReplaceFile(e) {
      this.replaceFileId = e;
      this.$modal
        .confirm(this.$t('doc.this_dept_replace_text'))

        .then(() => {
          this.loading = true
          this.$refs.replaceFile.dispatchEvent(new MouseEvent("click"));
        })
        .catch(() => {
          this.loading = false
        });
    },
    handlestandardimportfileChange(e) {
      let file = e.target.files[0];
      const formImportFile = new FormData();
      formImportFile.append("file", file);
      formImportFile.append("tenantId", cache.session.get('tenantId'));
      if (file != undefined) {
        standardimport(formImportFile).then((res) => {
          if (res.data.initFileMsg.length > 0 || res.data.initRecordMsg.length > 0 || res.data.linkFileMsg.length > 0) {
            this.$message({
              showClose: true,
              dangerouslyUseHTMLString: true,
              duration: 0,
              message:
                res.data.initFileMsg +
                "<br/>" +
                res.data.initRecordMsg +
                "<br/>" +
                res.data.linkFileMsg,
              type: "warning",
            });
          } else {
            this.$message.success(this.$t('doc.this_dept_operation_succ'));
          }
          this.getList();
          this.$refs.filetandardimport.value = null;
        });
      }
    },
    handlestandardReplaceFileChange(e) {
      let file = e.target.files[0];
      //校验文件类型
      let fileType = ["doc", "docx", "xls", "xlsx", "pdf", "ppt", "pptx"];
      let fileExtension = "";

      if (file.name.lastIndexOf(".") > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
      }
      const isTypeOk = fileType.some((type) => {
        if (fileExtension && fileExtension.indexOf(type) > -1) return true;
        return false;
      });
      if (!isTypeOk) {
        this.$modal.msg(this.$t('sys_mgr.dispose_file_format_not_support'));
        return false;
      }

      // 校检文件大小

      const isLt = file.size / 1024 / 1024 < 100;
      if (!isLt) {
        this.$modal.msgError(this.$t('sys_mgr.dispose_file_too_big'));
        return false;
      }
      this.formimportfile = new FormData();
      this.formimportfile.append("file", file);
      this.formimportfile.append("id", this.replaceFileId.id);
      this.formimportfile.append("docId", this.replaceFileId.docId);
      this.formimportfile.append("versionId", this.replaceFileId.versionId);
      standardReplaceFile(this.formimportfile).then((res) => {
        this.$message.success(this.$t('doc.this_dept_operation_succ'));
        this.loading = false
        this.getList();
        this.$refs.replaceFile.value = null;
      }).catch(() => {this.loading = false });
    },
    // 切换 关联记录和关联文件
    async linkstate(tab) {
      // 重置待关联清单查询的文件分类
      if (tab.index == 0) {
          // 关联记录
          this.linkqueryParams.linkType = "RECORD";
          await this.getlinkClassLevelOptions("RECORD")
      } else if (tab.index == 1) {
          // 关联文件
          this.linkqueryParams.linkType = "REF_DOC";
          await this.getlinkClassLevelOptions("DOC")
      }
      // 待关联清单，默认查询第一个分类
      this.sjiParams.docClass = this.linkClassLevelOptions[0].id
      this.getlinkLogList();
    },
    // 修改对话框-保存按钮
    submitForm() {
      let _this = this
      _this.$refs["elForm"].validate((valid) => {
        if (!valid) return;
        // TODO 提交表单
        _this.$modal.confirm(this.$t('doc.this_dept_whether_conf_save')).then(function () {
          _this.formData.applyTime = _this.parseTime(
            _this.nowDate,
            "{y}-{m}-{d} {h}:{i}:{s}"
          );
          let appendixes = []
          _this.appendixesfileList.forEach((element) => {
            appendixes.push({
              fileId: element.url
            });
          });
          _this.formData.appendixes = appendixes
          _this.updateFileLoading = true
          standardUpdate(_this.formData).then((res) => {
            // 保存成功
            _this.$message.success(_this.$t('doc.this_dept_operation_succ'));
            _this.getList();
            _this.dialogFormVisible = false;
            _this.updateFileLoading = false
          }).catch(() => {
            _this.updateFileLoading = false
          });
        }).then(() => {
        }).catch((e) => {
          console.log(e)
        });
      });
    },
    increase() {
      this.percentage += 10;
      if (this.percentage > 100) {
        this.percentage = 100;
      }
    },
    decrease() {
      this.percentage -= 10;
      if (this.percentage < 0) {
        this.percentage = 0;
      }
    },
    getclassLevelOptions() {
      let self = this
      //alert(self.dataType )
      settingDocClassList({ pageNum: 1, classStatus: "1", dataType: self.dataType }).then(
        (response) => {
          self.classLevelOptions = [];
          response.rows.forEach((element, index) => {
            response.rows[index].children = [];
          });
          /*不排除外来文件了*/
          let res = response.rows
          self.classLevelOptions = self.handleTree(
            res,
            "id",
            "parentClassId"
          );
        }
      );
    },
    // 右侧关联的文件类型树结构
    async getlinkClassLevelOptions(classTypeVal) {
      let self = this
      self.linkClassLevelOptions = [];
      let response = await settingDocClassList({ pageNum: 1, pageSize: 100, classStatus: "1", classType: classTypeVal, dataType: self.dataType })
      self.linkClassLevelOptions = [];
      response.rows.forEach((element, index) => {
        response.rows[index].children = [];
      });
      self.linkClassLevelOptions = self.handleTree(
        response.rows,
        "id",
        "parentClassId"
      );
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // 当前行记录置空
      this.currentRow = null
      this.linkqueryParams.standardId = "-1"
      this.queryParams.pageNum = 1;
      this.getList();

    },
    /** 搜索按钮操作 */
    sjihandleQuery() {
      if(this.sjiParams.docClass == undefined) {
        this.$modal.msgError(this.$t('sys_mgr.dispose_select_con_flie_type'))
        return false
      }
      this.sjiParams.pageNum = 1;
      this.getsjiLogList();
    },
    resetForm() {
      this.$refs["elForm"].resetFields();
    },
    /** 详情、操作按钮操作 */
    handleDetails(e, title) {
      this.title = title;
      // 版本相关附件
      this.appendixesfileList = []
      processstandard(e.id).then((res) => {
        res.data.forever = String(res.data.forever);
        this.formData = res.data;
        if (res.data.changeFactor) {
          this.changeFactor = res.data.changeFactor.split(",");
        }else {
          this.changeFactor = [];
        }
        this.formData.editUserName = res.data.userName;
        if (res.data.nickName != null) {
          this.formData.nickName = res.data.nickName;
        }
        //  this.formData.userName = this.selectionUser.userName;
        if (res.data.userName != null) {
          this.formData.userName = res.data.userName;
        } else {
          this.formData.userName = this.userInfo.userName;
        }
        if (res.data.deptId != null) {
          this.formData.deptId = res.data.deptId;
        } else {
          this.formData.deptId = this.userInfo.deptId;
        }
        if (res.data.deptName != null) {
          this.formData.deptName = res.data.deptName;
        } else {
          this.formData.deptName = this.userInfo.dept.deptName;
        }
        if (res.data.applyTime != null) {
          this.formData.applyTime = res.data.applyTime;
        } else {
          this.formData.applyTime = this.nowDate;
        }

        if (res.data.fileName != null) {
          this.formData.fileName = res.data.fileName;
        }

        if (res.data.preAppendixes) {
          // 版本相关附件
          res.data.preAppendixes.forEach((element) => {
            this.appendixesfileList.push({
              name: element.fileName,
              url: element.fileId,
            });
          });
        }
      });
      if (this.title == this.$t('doc.this_dept_edit') && e.status != 0) {
        this.$modal
          .confirm(
            this.$t('sys_mgr.dispose_text2')
          )
          .then(() => {
            this.dialogFormVisible = true;
          })
          .catch(() => { });
      } else {
        this.dialogFormVisible = true;
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      let ids = this.selection.map((item) => item.id);
      if (ids.length > 0) {
        this.queryParams.ids = ids
      }
      this.download(
        "/process/standard/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
      this.queryParams.ids = undefined
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selection = selection;
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    handleSelect() {
      let _this = this
      _this.$nextTick(()=>{
        _this.$refs.userList.init(null,null,null)
      })
    },

    getTemplateRow(item) {
      this.selectionUser = item;
      // ... 拿到选中的表格数据
    },
    handleSubmitUser(source,index,user) {
      this.formData.editUserName = user.userName;
      this.formData.nickName = user.nickName;
      this.formData.userName = user.userName;
      this.formData.deptId = user.dept.deptId;
      this.formData.deptName = user.dept.deptName;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm(
          this.$t('doc.this_dept_delete_text')
        )
        .then(function () {
          return standarddelete({
            id: row.id,
            docId: row.docId,
            versionId: row.versionId,
          });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
        })
        .catch(() => { });
    },
    /** 删除按钮操作 */
    handlelinkLogDelete(row) {
      this.$modal
        .confirm(this.$t('sys_mgr.dispose_text3'))
        .then(function () {
          return linkLogDelete(row.id);
        })
        .then(() => {
          this.getlinkLogList();
          this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
        })
        .catch(() => { });
    },
    handlestandardUpdateStatus() {
      let _this = this
      _this.$modal.confirm(this.$t('sys_mgr.doc_whether_effc')).then(function () {
        _this.updateStatusLoading=true
        for (const index in _this.selection) {
          const item = _this.selection[index]
          if (item.docId == null) {
            _this.$modal.msgError(this.$t('sys_mgr.dispose_text4'));
            _this.updateStatusLoading=false
            return
          }
        }
        let ids = _this.selection.map((item) => item.id);
        standardUpdateByParam("null",ids).then((res) => {
          _this.$modal.msgSuccess(this.$t('doc.this_dept_operation_succ'));
          _this.currentRow = null
          _this.getList();
          _this.updateStatusLoading=false
          // 刷新文件台账数据
          _this.refreshDocAccount(ids)
        }).catch(() => {
          _this.updateStatusLoading=false
        });
      }).then(() => {

      }).catch(() => { });
    },
    handleTestUpload() {
      let data = {
        filePath: this.queryParams.searchValue
      }
      testLocalUpload(data).then((res) => {
         console.log('测试上传', res)
      })
    },
    handleDown() {
      let data = {
        filePath: this.queryParams.searchValue
      }
      testLocalDown(data).then((res) => {
        console.log('测试下载', res)
      })
    },
    handlestandardDelete() {
      let statusList = this.selection.map((item) => item.status);
      if (statusList.indexOf('1') !== -1) {
        this.$modal.msgError(this.$t('sys_mgr.dispose_text5'));
        return
      }
      let ids = this.selection.map((item) => item.id);
      this.$modal
        .confirm(
          this.$t('doc.this_dept_delete_text')
        )
        .then(function () {
          return standardDeleteBatch(ids)
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
        })
        .catch(() => { });
    },
    // 双击关联文件或记录
    rowDblclick(e) {
      if (this.currentRow == null) {
        this.$modal.msg(this.$t('sys_mgr.dispose_select_main_file'));
        return;
      }
      /*
      if (this.currentRow.status != 0) {
        this.$modal.msg("请选择文件状态为'草稿'的文件");
        return;
      }*/
      e.id = this.currentRow.id;
      e.linkType = this.linkqueryParams.linkType;
      standardJoin(e).then((res) => {
        this.$modal.msgSuccess(this.$t('doc.this_dept_operation_succ'));
        this.getlinkLogList();
        this.getsjiLogList();
      });
    },
    updateLinkLog(row){
      linkLogUpdate(row).then((res) => {
        this.getlinkLogList();
        this.$modal.msgSuccess(this.$t('doc.this_dept_operation_succ'));
      }).catch(() => {
        this.getlinkLogList();
      });
    },
    standardDocBeforeUpload(params) {
      this.recordLoading = true
      const isLt1M = params.file.size / 1024 / 1024 < 10;
      var testmsg = params.file.name.substring(
        params.file.name.lastIndexOf(".") + 1
      );
      if (
        testmsg != "docx" &&
        testmsg != "doc" &&
        testmsg != "ppt" &&
        testmsg != "pptx" &&
        testmsg != "pdf" &&
        testmsg != "xls" &&
        testmsg != "xlsx"
      ) {
        this.$message.error(this.$t('sys_mgr.dispose_text6'));
        this.standardDocfileList = [];
        this.recordLoading = false
        return;
      }
      if (!isLt1M) {
        this.$message.error(this.$t('file_handle.change_upload_limit'));
        this.standardDocfileList = [];
        this.recordLoading = false
        return;
      }
      this.standardDocfileList = [];
      let standardDoc = new FormData();
      standardDoc.append("file", params.file); //传文件
      standardDoc.append("id", this.currentRow.id);
      standardDoc.append("versionId", this.currentRow.versionId);
      standardRecordFile(standardDoc).then((res) => {
        this.$modal.msgSuccess(this.$t('doc.this_dept_operation_succ'));
        this.recordLoading = false
        this.getlinkLogList();
      }).catch(() => {
        this.recordLoading = false
      });
    },
  },
};
</script>
<style lang="scss">
.vue-treeselect {
  height: 34px;
}

.vue-treeselect .vue-treeselect__control {
  height: 34px !important;
}

.vue-treeselect__placeholder {
  line-height: 34px;
  font-size: 14px;
}

.vue-treeselect input {
  font-size: 14px;
}

.mask {
  background-color: rgb(0, 0, 0);
  opacity: 0.6;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999999;
}

.maskprogress {
  position: fixed;
  top: 50%;
  /*距顶部50%*/
  left: 50%;
  margin: -100px 0 0 -100px;
  /*设定这个div的margin-top的负值为自身的高度的一半,margin-left的值也是自身的宽度的一半的负值.
  // width: 300px; /*宽为400,那么margin-top为-200px*/
  // height: 200px; /*高为200那么margin-left为-100px;*/
  z-index: 9999999;

  /*浮动在最上层 */
  .el-progress__text {
    color: #ffff;
  }
}
</style>
