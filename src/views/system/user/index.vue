<template>
  <div class="app-container document_changeindex el-card is-always-shadow">
    <div class="el-card__body">

      <el-row :gutter="20">
        <!--部门数据-->
        <el-col :span="6" :xs="24">

          <div class="head-container">
            <el-input
              v-model.trim="deptName"
              :placeholder="$t('sys_mgr.user_input_dept_name')"
              clearable
              size="small"
              prefix-icon="el-icon-search"
              style="margin-bottom: 20px"
            />
          </div>
          <div class="head-container">
            <el-tree
              :data="deptOptions"
              :props="defaultProps"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              ref="tree"
              default-expand-all
              @node-click="handleNodeClick"
            />
          </div>
        </el-col>
        <!--用户数据-->
        <el-col :span="18" :xs="24">

          <el-form :model="queryParams" ref="queryForm"  v-show="showSearch" :label-width="columnLangSizeFlag ? '88px' : '68px'" :inline="true">
            <el-form-item :label="$t('sys_mgr.user_name')" prop="userName">
              <el-input
                v-model.trim="queryParams.userName"
                :placeholder="$t('sys_mgr.user_input_name')"
                clearable
                size="small"
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item :label="$t('sys_mgr.user_nickname')" prop="nickName" :label-width="columnLangSizeFlag ? '128px' : '68px'">
              <el-input
                v-model.trim="queryParams.nickName"
                :placeholder="$t('sys_mgr.user_input_nickname')"
                clearable
                size="small"
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item :label="$t('sys_mgr.user_phone_num')" prop="phonenumber" :label-width="columnLangSizeFlag ? '128px' : '68px'">
              <el-input
                v-model.trim="queryParams.phonenumber"
                :placeholder="$t('sys_mgr.user_input_phone')"
                clearable
                size="small"
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item :label="$t('doc.this_dept_status')" prop="status">
              <el-select
                v-model.trim="queryParams.status"
                :placeholder="$t('sys_mgr.user_status')"
                clearable
                size="small"
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dictLanguage(dict)"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('file_set.version_create_time')" :label-width="columnLangSizeFlag ? '128px' : '68px'">
              <el-date-picker
                v-model.trim="dateRange"
                size="small"
                style="width: 240px"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="-"
                :start-placeholder="$t('doc.this_dept_start_date')"
                :end-placeholder="$t('doc.this_dept_end_date')"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('doc.this_dept_search') }}</el-button>
              <el-button icon="el-icon-refresh"  @click="resetQuery">{{ $t('myItem.handle_reset') }}</el-button>
              <el-button type="primary" icon="el-icon-refresh"  @click="handleSyncOrgAndUser">{{ $t('sys_mgr.user_sync') }}</el-button>
            </el-form-item>
          </el-form>

          <el-row :gutter="10" class="mb8">
            <!--
            <el-col :span="1.5">
              <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['system:user:add']"
              >新增</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button

                plain
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdate"
                v-hasPermi="['system:user:edit']"
              >修改</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button

                plain
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['system:user:remove']"
              >删除</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button

                plain
                icon="el-icon-upload2"
                size="mini"
                @click="handleImport"
                v-hasPermi="['system:user:import']"
              >导入</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button

                plain
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
                v-hasPermi="['system:user:export']"
              >导出</el-button>
            </el-col> -->
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
          </el-row>
          <el-card class="gray-card">
            <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="50" align="center" />
              <!--
              <el-table-column label="用户编号" align="center" key="userId" prop="userId" v-if="columns[0].visible" />-->
              <el-table-column :label="$t('sys_mgr.user_name')" align="center" key="userName" prop="userName" v-if="columns[1].visible" :show-overflow-tooltip="true" />
              <el-table-column :label="$t('sys_mgr.user_nickname')" align="center" key="nickName" prop="nickName" v-if="columns[2].visible" :show-overflow-tooltip="true" />
              <el-table-column :label="$t('doc.this_dept_dept')" align="center" key="deptName" prop="dept.deptName" v-if="columns[3].visible" :show-overflow-tooltip="true" />
              <!--
              <el-table-column label="手机号码" align="center" key="phonenumber" prop="phonenumber" v-if="columns[4].visible" width="120" />-->
              <el-table-column :label="$t('doc.this_dept_status')" align="center" key="status" v-if="columns[5].visible">
                <template slot-scope="scope">
                  <el-switch
                    v-model.trim="scope.row.status"
                    active-value="0"
                    inactive-value="1"
                    @change="handleStatusChange(scope.row)"
                  ></el-switch>
                </template>
              </el-table-column>
              <el-table-column :label="$t('file_set.version_create_time')" align="center" prop="createTime" v-if="columns[6].visible" width="160"
              >
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('myItem.msg_operation')"
                align="center"
                width="300"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope" v-if="scope.row.userId !== 1">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['system:user:edit']"
                  >{{ $t('doc.this_dept_edit') }}</el-button>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['system:user:remove']"
                  >{{ $t('doc.this_dept_delete') }}</el-button>

                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-refresh-right"
                    @click="handleResetPwd(scope.row)"
                    v-hasPermi="['system:user:resetPwd']"
                  >{{ $t('sys_mgr.user_reset_pwd') }}</el-button>

                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-s-custom"
                    @click="handleAuthRole(scope.row)"
                    v-hasPermi="['system:user:distributeRole']"
                  >{{ $t('sys_mgr.user_distri_role') }}</el-button>

                  <!--
                  <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['system:user:resetPwd', 'system:user:distributeRole']">
                    <span class="el-dropdown-link">
                      <i class="el-icon-d-arrow-right el-icon--right"></i>更多
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="handleResetPwd" icon="el-icon-key"
                        v-hasPermi="['system:user:resetPwd']">重置密码</el-dropdown-item>
                      <el-dropdown-item command="handleAuthRole" icon="el-icon-circle-check"
                        v-hasPermi="['system:user:distributeRole']">分配角色</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>-->
                </template>
              </el-table-column>
            </el-table>
          </el-card>

          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.user_nickname')" prop="nickName">
                <el-input v-model.trim="form.nickName" :placeholder="$t('sys_mgr.user_input_nickname')" maxlength="30" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.user_belong_dept')" prop="deptId">
                <treeselect v-model.trim="form.deptId" :options="deptOptions" :show-count="true" :placeholder="$t('sys_mgr.user_select_belong_dept ')" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.user_phone_num')" prop="phonenumber">
                <el-input v-model.trim="form.phonenumber" :placeholder="$t('sys_mgr.user_input_phone')" maxlength="11" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.user_email')" prop="email">
                <el-input v-model.trim="form.email" :placeholder="$t('sys_mgr.user_input_emal')" maxlength="50" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item v-if="form.userId == undefined" :label="$t('sys_mgr.user_name')" prop="userName">
                <el-input v-model.trim="form.userName" :placeholder="$t('sys_mgr.user_input_name')" maxlength="30" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item v-if="form.userId == undefined" :label="$t('sys_mgr.user_pwd')" prop="password">
                <el-input v-model.trim="form.password" :placeholder="$t('sys_mgr.user_input_pwd')" type="password" maxlength="20" show-password/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.user_gender')">
                <el-select v-model.trim="form.sex" :placeholder="$t('doc.this_dept_pls_select')">
                  <el-option
                    v-for="dict in dict.type.sys_user_sex"
                    :key="dict.value"
                    :label="dictLanguage(dict)"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('doc.this_dept_status')">
                <el-radio-group v-model.trim="form.status">
                  <el-radio
                    v-for="dict in dict.type.sys_normal_disable"
                    :key="dict.value"
                    :label="dict.value"
                  >{{dictLanguage(dict)}}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.position')">
                <el-select v-model.trim="form.postIds" multiple :placeholder="$t('doc.this_dept_pls_select')">
                  <el-option
                    v-for="item in postOptions"
                    :key="item.postId"
                    :label="item.postName"
                    :value="item.postId"
                    :disabled="item.status == 1"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('file_set.type_role')">
                <el-select v-model.trim="form.roleIds" multiple :placeholder="$t('doc.this_dept_pls_select')">
                  <el-option
                    v-for="item in roleOptions"
                    :key="item.roleId"
                    :label="item.roleName"
                    :value="item.roleId"
                    :disabled="item.status == 1"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('sys_mgr.user_tenant')">
                <el-select v-model.trim="form.tenantIds" multiple :placeholder="$t('doc.this_dept_pls_select')">
                  <el-option
                    v-for="item in tenantOptions"
                    :key="item.id"
                    :label="item.tenantName"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item :label="$t('sys_mgr.user_remark')">
                <el-input v-model.trim="form.remark" type="textarea" :placeholder="$t('file_set.version_fill_content')"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('file_set.type_confim') }}</el-button>
          <el-button @click="cancel">{{ $t('file_set.type_cancel') }}</el-button>
        </div>
      </el-dialog>

      <!-- 用户导入对话框 -->
      <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
        <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">{{ $t('sys_mgr.user_text') }}<em>{{ $t('doc.this_dept_click_to_upload') }}</em></div>
          <div class="el-upload__tip text-center" slot="tip">
            <div class="el-upload__tip" slot="tip">
              <el-checkbox v-model.trim="upload.updateSupport" /> {{ $t('sys_mgr.user_text1') }}
            </div>
            <span>{{ $t('sys_mgr.user_only_excel') }}</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">{{ $t('sys_mgr.user_download_temp') }}</el-link>
          </div>
        </el-upload>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">{{ $t('file_set.type_confim') }}</el-button>
          <el-button @click="upload.open = false">{{ $t('file_set.type_cancel') }}</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { syncOrgAndUser } from "@/api/as/asUser";
import { listUser, getUser, delUser, addUser, updateUser, resetUserPwd, changeUserStatus } from "@/api/system/user";
import { getToken } from "@/utils/auth";
import { treeselect } from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "User",
  dicts: ['sys_normal_disable', 'sys_user_sex'],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 租户选项
      tenantOptions: [
        // {
        //   "id":"dc41618350206272c0b3271ccb9c3c76",
        //   "tenantName":"长沙睿展数据科技有限公司"
        // },
        // {
        //   "id":"d1b7545122483b58875e6fbb244e206b",
        //   "tenantName":"长沙睿展数据科技有限公司-Test"
        // }
      ],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        nickName: undefined,
        phonenumber: undefined,
        status: undefined,
        deptId: undefined
      },
      // 列信息
      columns: [
        { key: 0, label: this.$t('sys_mgr.user_num'), visible: true },
        { key: 1, label: this.$t('sys_mgr.user_name'), visible: true },
        { key: 2, label: this.$t('sys_mgr.user_nickname'), visible: true },
        { key: 3, label: this.$t('doc.this_dept_dept'), visible: true },
        { key: 4, label: this.$t('sys_mgr.user_phone_num'), visible: true },
        { key: 5, label: this.$t('doc.this_dept_status'), visible: true },
        { key: 6, label: this.$t('file_set.version_create_time'), visible: true }
      ],
      // 表单校验
      rules: {
        userName: [
          { required: true, message: this.$t('sys_mgr.user_name_not_null'), trigger: "blur" },
          { min: 2, max: 20, message: this.$t('sys_mgr.user_name_text'), trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: this.$t('sys_mgr.user_nickname_not_null'), trigger: "blur" }
        ],
        password: [
          { required: true, message: this.$t('sys_mgr.user_pwd_not_null'), trigger: "blur" },
          { min: 5, max: 20, message: this.$t('sys_mgr.user_pwd_text'), trigger: 'blur' }
        ],
        email: [
          {
            type: "email",
            message: this.$t('sys_mgr.user_input_right_email'),
            trigger: ["blur", "change"]
          }
        ],
        phonenumber: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: this.$t('sys_mgr.user_input_right_phone'),
            trigger: "blur"
          }
        ],
      },
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    this.getList();
    this.getTreeselect();
    this.getConfigKey("sys.user.initPassword").then(response => {
      this.initPassword = response.msg;
    });
  },
  methods: {
    // 排序节点
    // 排序节点
    sortDataById(data) {
      // 按id排序函数
      function sortById(a, b) {
        return parseInt(a.weight) - parseInt(b.weight)
      }
      // 递归排序子级
      function sortChildren(node) {
        if (node.children && node.children.length > 0) {
          node.children.sort(sortById);
          node.children.forEach(child => {
            sortChildren(child);
          });
        }
      }
      // 初始排序
      data.sort(sortById);
      // 对每个节点递归排序子级
      data.forEach(node => {
        sortChildren(node);
      });
      return data;
    },
    // 增量同步爱数的组织和用户数据
    handleSyncOrgAndUser() {
      let self = this
      self.$modal.alert(this.$t('sys_mgr.user_sync_text'));
      self.loading = true
      syncOrgAndUser().then(response => {
        if(response.code === 200) {
          self.$modal.alert(this.$t('sys_mgr.user_sync_succ'));
        } else {
          self.$modal.alert(this.$t('sys_mgr.user_sync_err')+response.msg);
        }
        self.loading = false
      });
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          this.userList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      let self = this
      treeselect({status: 0}).then(response => {
        let data = self.sortDataById(response.data);
        this.deptOptions = data
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.getList();
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? this.$t('file_set.number_enable') : this.$t('sys_mgr.user_stop');
      this.$modal.confirm(this.$t('sys_mgr.user_text2') + text + '""' + row.userName + this.$t('sys_mgr.user_text3')).then(function() {
        return changeUserStatus(row.userId, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + this.$t('file_handle.change_succ'));
      }).catch(function() {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: undefined,
        deptId: undefined,
        userName: undefined,
        nickName: undefined,
        password: undefined,
        phonenumber: undefined,
        email: undefined,
        sex: undefined,
        status: "0",
        remark: undefined,
        postIds: [],
        roleIds: [],
        tenantIds: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.userId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleResetPwd":
          this.handleResetPwd(row);
          break;
        case "handleAuthRole":
          this.handleAuthRole(row);
          break;
        default:
          break;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getTreeselect();
      getUser().then(response => {
        this.postOptions = response.data.posts;
        this.roleOptions = response.data.roles;
        this.tenantOptions = response.data.tenants;
        this.open = true;
        this.title = this.$t('sys_mgr.user_add');
        this.form.password = this.initPassword;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect();
      const userId = row.userId || this.ids;
      getUser(userId).then(response => {
        this.form = response.data.user;
        this.postOptions = response.data.posts;
        this.roleOptions = response.data.roles;
        this.form.postIds = response.data.postIds;
        this.form.roleIds = response.data.roleIds;
        this.form.tenantIds = response.data.tenantIds;
        this.open = true;
        this.title = this.$t('sys_mgr.user_edit');
        this.form.password = "";
      });
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$prompt(this.$t('sys_mgr.user_pls_input') + row.userName + this.$t('sys_mgr.user_new_pwd'), this.$t('file_handle.change_tip'), {
        confirmButtonText: this.$t('file_handle.change_confirm'),
        cancelButtonText: this.$t('doc.this_dept_abolish'),
        closeOnClickModal: false,
        inputPattern: /^.{5,20}$/,
        inputErrorMessage: this.$t('sys_mgr.user_pwd_text'),
      }).then(({ value }) => {
        resetUserPwd(row.userId, value).then(response => {
          this.$modal.msgSuccess(this.$t('sys_mgr.user_text4') + value);
        });
      }).catch(() => {});
    },
    /** 分配角色操作 */
    handleAuthRole: function(row) {
      const userId = row.userId;
      this.$router.push("/system/user-auth/role/" + userId);
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.userId != undefined) {
            updateUser(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
              this.open = false;
              this.getList();
            });
          } else {
            addUser(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('file_set.number_field'));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.userId || this.ids;
      this.$modal.confirm(this.$t('sys_mgr.user_text5')).then(function() {
        return delUser(userIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(this.$t('sys_mgr.user_text5'));
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/user/export', {
        ...this.queryParams
      }, `user_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = this.$t('sys_mgr.user_import');
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {
        ...this.queryParams
      }, `user_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, this.$t('sys_mgr.user_import_result'), { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
