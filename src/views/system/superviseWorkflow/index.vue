<template>
  <div class="app-container el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span> {{ $t('sys_mgr_log.supervise_manage') }} </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        :label-width="columnLangSizeFlag ? '128px' : '98px'"
      >
        <div class="global-ser" :class="!boxClass ? '' : 'open'" id="add">
          <div class="ser-top">
            <div class="cell-left">
              <el-form-item :label="$t('sys_mgr.proc_def_name')">
                <el-input
                  v-model="queryParams.procDefName"
                  :placeholder="$t('sys_mgr.proc_def_name_input')"
                  clearable
                  @keyup.enter.native="handleQuery"
                >
                </el-input>
              </el-form-item>
              <el-form-item :label="$t('sys_mgr.proc_def_key')" :label-width="columnLangSizeFlag ? '128px' : '98px'">
                <el-input
                  v-model="queryParams.procDefKey"
                  :placeholder="$t('sys_mgr.proc_def_key_input')"
                  clearable
                  @keyup.enter.native="handleQuery"
                >
                </el-input>
              </el-form-item>
              <el-form-item :label="$t('sys_mgr.supervise_status')">
                <el-select
                  :placeholder="$t('sys_mgr.supervise_status_select')"
                  v-model="queryParams.supervise"
                >
                  <el-option
                    v-for="dict in dict.type.sys_normal_disable"
                    :key="dict.value"
                    :label="dictLanguage(dict)"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search"  type="primary" @click="handleQuery">{{ $t('doc.this_dept_query') }}</el-button>
                <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('myItem.handle_reset') }}</el-button>
              </el-form-item>
            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleSync()">{{ $t('sys_mgr.proc_def_sync') }}</el-button>
            </div>
          </div>
        </div>
      </el-form>
      <el-card class="gray-card">
        <el-table
          v-loading="loading"
          :data="postList"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          row-key="id"
        >
          <el-table-column
            :label="$t('sys_mgr.proc_def_name')"
            align="left"
            prop="procDefName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('sys_mgr.proc_def_key')"
            align="left"
            prop="procDefKey"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('sys_mgr.proc_def_id')"
            align="left"
            prop="procDefId"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('sys_mgr.supervise_status')"
            align="center"
            prop="status"
            width="150px"
          >
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.supervise"
                active-value="0"
                inactive-value="1"
                @change="handleStatusChange(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('myItem.msg_operation')"
            align="left"
            class-name="small-padding fixed-width"
            width="100px"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
              >{{ $t('doc.this_dept_delete') }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script>
import {
  listWorkflowSupervise,
  syncWorkflowSupervise,
  updateWorkflowSupervise,
  delWorkflowSupervise,
} from "@/api/process/workflowSupervise";
import mixin from "@/layout/mixin/Commmon.js";
export default {
  name: "SuperviseWorkflow",
  dicts: ["sys_normal_disable"],
  mixins: [mixin],
  data() {
    return {
      boxClass: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        procDefName: '',
        procDefKey: '',
        supervise: ''
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      id: "",
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      listWorkflowSupervise(this.queryParams).then((response) => {
        this.postList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 表单重置
    reset() {
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.procDefName = ''
      this.queryParams.procDefKey = ''
      this.queryParams.supervise = ''
      this.handleQuery();
    },
    /** 同步按钮操作 */
    handleSync() {
      this.reset();
      syncWorkflowSupervise().then((response) => {
        this.$modal.msgSuccess(this.$t('sys_mgr.proc_def_sync_succ'));
        this.getList();
      }).catch(() => {
        this.$modal.msgError(this.$t('sys_mgr.proc_def_sync_error'));
      });
    },
    /** 督办状态修改 */
    handleStatusChange(row) {
      updateWorkflowSupervise({id: row.id, supervise: row.supervise}).then((response) => {
        this.$modal.msgSuccess(this.$t('file_set.version_edit_succ'));
        this.getList();
      }).catch(() => {
        row.supervise = row.supervise === "0" ? "1" : "0";
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(this.$t('file_set.signature_text') + row.procDefName + this.$t('file_set.signature_text1'))
        .then(function () {
          return delWorkflowSupervise(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t('file_set.signature_delete_succ'));
        })
        .catch(() => {});
    }
  },
};
</script>
<style lang="scss">
.upload-rz {
  display: inline-block;
  margin: 0px 5px;
  .el-upload {
    border: 0px solid #013288;
    background: #fff0;
  }
  .el-upload:hover {
    background-color: #fff0 !important;
  }
}
@import "../../../../public/css/poctstyle.css";
</style>
