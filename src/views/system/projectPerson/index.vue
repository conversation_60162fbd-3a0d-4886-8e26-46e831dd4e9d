<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" :label-width="columnLangSizeFlag ? '88px' : '68px'">
      <el-form-item :label="$t('project_person.project_code')" prop="projectCode">
        <el-select
          filterable
          :placeholder="$t('project_person.project_code_placeholder')"
          v-model.trim="queryParams.projectCode"
          style="width: 200px"
          clearable
        >
          <el-option
            v-for="dict in dict.type.project_code_list"
            :key="dict.value"
            :label="dictLanguage(dict)"
            :value="dict.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('doc.this_dept_search') }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ $t('myItem.handle_reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['setting:projectPerson:add']"
        >{{ $t('project_person.add') }}</el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="projectPersonList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="$t('project_person.project_code')" align="center" prop="projectCode" />
      <el-table-column :label="$t('project_person.users')" align="center" prop="users" :show-overflow-tooltip="true" />
      <el-table-column :label="$t('myItem.msg_operation')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['setting:projectPerson:edit']"
          >{{ $t('project_person.edit') }}</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['setting:projectPerson:remove']"
          >{{ $t('project_person.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改项目人员配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="50%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item :label="$t('project_person.project_code')" prop="projectCode">
          <el-select
            filterable
            :placeholder="$t('project_person.project_code_placeholder')"
            v-model.trim="form.projectCode"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="dict in dict.type.project_code_list"
              :key="dict.value"
              :label="dictLanguage(dict)"
              :value="dict.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('project_person.users')" prop="users">
          <div style="border: 1px solid #dcdfe6; border-radius: 4px; padding: 10px; min-height: 200px;">
            <div style="margin-bottom: 10px;">
              <el-button type="primary" size="mini" @click="handleSelectUsers">
                <i class="el-icon-plus"></i> {{ $t('project_person.select_users') }}
              </el-button>
            </div>
            <el-table :data="userList" border size="mini" style="width: 100%">
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column :label="$t('project_person.user_account')" prop="userAccount" min-width="150">
              </el-table-column>
              <el-table-column :label="$t('project_person.user_name')" prop="userName" min-width="120">
              </el-table-column>
              <el-table-column :label="$t('project_person.user_dept')" prop="userDept" min-width="120">
              </el-table-column>
              <el-table-column :label="$t('myItem.msg_operation')" width="80" align="center">
                <template slot-scope="scope">
                  <el-button
                    type="danger"
                    size="mini"
                    icon="el-icon-delete"
                    @click="handleRemoveUser(scope.$index)"
                  ></el-button>
                </template>
              </el-table-column>
            </el-table>
            <div v-if="userList.length === 0" style="text-align: center; color: #909399; padding: 20px;">
              {{ $t('project_person.no_users') }}
            </div>
          </div>
        </el-form-item>

    <!-- 用户选择组件 -->
    <user-list ref="userList" @selectHandle="userSelectHandle"></user-list>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">{{ $t('file_set.type_confim') }}</el-button>
        <el-button @click="cancel">{{ $t('file_set.type_cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listProjectPerson, getProjectPerson, removeProjectPerson, addProjectPerson, updateProjectPerson, exportProjectPerson } from "@/api/setting/projectPerson";
import { parseTime } from "@/utils/ruoyi";
import UserList from '@/views/workflowList/addWorkflow/add_import/userList.vue';

export default {
  name: "ProjectPerson",
  components: { UserList },
  dicts: ["project_code_list"],
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目人员配置表格数据
      projectPersonList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectCode: undefined,
        orderByColumn: 'create_time',
        isAsc: 'desc'
      },
      // 表单参数
      form: {},
      // 用户列表数据
      userList: [],
      // 表单校验
      rules: {
        projectCode: [
          { required: true, message: this.$t('project_person.project_code_required'), trigger: "change" }
        ],
        users: [
          { required: true, message: this.$t('project_person.users_required'), trigger: "blur" }
        ]
      },
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询项目人员配置列表 */
    getList() {
      this.loading = true;
      listProjectPerson(this.queryParams).then(response => {
        this.projectPersonList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectCode: null,
        users: null
      };
      this.userList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t('project_person.add_title');
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids[0]
      getProjectPerson(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.parseUsersString(this.form.users);
        this.open = true;
        this.title = this.$t('project_person.edit_title');
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 检查是否有用户数据
          if (this.userList.length === 0) {
            this.$modal.msgError(this.$t('project_person.users_required'));
            return;
          }
          this.buttonLoading = true;
          // 确保users字段是最新的
          this.updateUsersString();
          if (this.form.id) {
            updateProjectPerson(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('project_person.update_success'));
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addProjectPerson(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('project_person.add_success'));
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm(this.$t('project_person.delete_confirm')).then(() => {
        this.loading = true;
        return removeProjectPerson(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess(this.$t('project_person.delete_success'));
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('setting/projectPerson/export', {
        ...this.queryParams
      }, `projectPerson_${new Date().getTime()}.xlsx`)
    },
    parseTime,
    /** 选择用户 */
    handleSelectUsers() {
      let _this = this;
      _this.$nextTick(() => {
        // 获取已选用户的用户名列表，用于禁用已选择的用户
        const selectedUserNames = _this.userList.map(item => item.userName);
        _this.$refs.userList.init('userList', null, null, null, true, selectedUserNames);
      });
    },
    /** 用户选择处理 */
    userSelectHandle(source, index, userList) {
      if (userList && userList.length > 0) {
        for (let i = 0; i < userList.length; i++) {
          let user = userList[i];
          // 检查是否已存在，避免重复添加
          const exists = this.userList.some(existUser => existUser.userName === user.userName);
          if (!exists) {
            this.userList.push({
              userAccount: user.userName, // 使用userName作为账号
              userName: user.nickName,    // 使用nickName作为姓名
              userDept: user.dept ? user.dept.deptName : ''
            });
          }
        }
        this.updateUsersString();
      }
    },
    /** 删除用户 */
    handleRemoveUser(index) {
      this.userList.splice(index, 1);
      this.updateUsersString();
    },
    /** 更新users字符串 */
    updateUsersString() {
      // 将用户列表转换为JSON字符串存储
      this.form.users = JSON.stringify(this.userList.filter(user =>
        user.userAccount || user.userName || user.userDept
      ));
    },
    /** 解析users字符串 */
    parseUsersString(usersStr) {
      if (usersStr) {
        try {
          this.userList = JSON.parse(usersStr);
        } catch (e) {
          // 如果不是JSON格式，尝试按行分割处理旧数据
          this.userList = usersStr.split('\n').filter(line => line.trim()).map(line => ({
            userAccount: line.trim(),
            userName: line.trim(),
            userDept: ''
          }));
        }
      } else {
        this.userList = [];
      }
    }
  }
};
</script>
