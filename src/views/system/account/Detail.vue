<template>
  <el-dialog
    :title="title"
    :visible="true"
    append-to-body
    width="80%"
    :close-on-click-modal="false"
    @close="$emit('close')"
  >
    <el-card class="gray-card table-card no-padding">
      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        size="medium"
        label-position="right"
        label-width="150px"
        :disabled="title == '详情'"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="文件类型:" prop="docClass">
              <treeselect
                v-model.trim="formData.docClass"
                :options="classLevelOptions"
                :normalizer="normalizer"
                :searchable="false"
                :show-count="true"
                :disabled="title == '详情'"
                placeholder="选择文件类型"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="变更类型:" prop="changeType">
              <el-select v-model="formData.changeType" placeholder="请选择">
                <el-option
                  v-for="item in changeTypeoptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="文件名称:" prop="docName">
              <div>
                <el-input
                  v-model.trim="formData.docName"
                  placeholder="请输入文件名称"
                  clearable
                >
                </el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="变更原因:" prop="changeReason">
              <el-input
                v-model.trim="formData.changeReason"
                placeholder="请输入变更原因"
                :style="{ width: '100%' }"
                maxlength="500"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="当前版本文件:">
              <div style="display: flex; justify-content: space-between">
                <span
                  style="color: #385bb4; margin-left: 10px; cursor: pointer"
                  :key="i"
                  v-if="formData.fileName != ''"
                  @click="handlePreview(formData.fileId)"
                  >{{ formData.fileName }}
                </span>
                <!-- 
                <fileUpload
                  v-if="title == '修改'"
                  v-model.trim="docNamefileList"
                  limit="1"
                  :fileType="['docx', 'doc', 'xls', 'xlsx', 'pdf']"
                  :isfileType="true"
                  :fileSize="100"
                  :showTransition="false"
                  title="替换文件"
                />-->
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文件编号:" prop="docId">
              <el-input
                v-model.trim="formData.docId"
                placeholder="请输入文件编号"
                maxlength="20"
                show-word-limit
                clearable
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="文件版本:" prop="currentVersion">
              <el-input v-model.trim="formData.currentVersion" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="编制部门:" prop="deptName">
              <span>{{ formData.deptName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="生效日期:" prop="startDate">
              <el-date-picker
                v-model="formData.startDate"
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期"
                :picker-options="pickerOptionsstartDate"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="编制人员:" prop="nickName">
              <div>
                <el-input
                  style="width: 80%; margin-right: 10px"
                  readonly
                  v-model.trim="formData.nickName"
                  placeholder="请选择编制人员"
                >
                  <el-button
                    slot="append"
                    icon="el-icon-search"
                    @click="handleSelect"
                  ></el-button>
                </el-input>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="文件有效期:">
              <el-date-picker
                v-model="formData.endDate"
                align="right"
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期"
                :picker-options="pickerOptions0"
              >
              </el-date-picker>
              <span style="margin: 0px 5px">是否永久</span>
              <el-radio-group v-model="formData.forever">
                <el-radio :label="'1'">是</el-radio>
                <el-radio :label="'0'">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="编制时间:" prop="applyTime">
              <el-date-picker
                v-model="formData.applyTime"
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="文件状态:" prop="status">
              <el-select v-model="formData.status" placeholder="请选择">
                <el-option
                  v-for="item in statusoptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <span v-for="(itme, i) in appendixsList" :key="i">
                {{ itme.docName }}
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="ID:" prop="id">
              {{ formData.id }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="发布日期:" prop="startDate">
              <el-date-picker
                v-model="formData.releaseTime"
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期"
                :picker-options="pickerOptionsstartDate"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="变更版本附件:">
              <!-- </div> -->
              <div v-if="title == '详情'">
                <div
                  v-for="(item, i) in formData.preAppendixes"
                  class="link-box bzlink-box"
                  :key="i"
                >
                  <span
                    style="color: #385bb4; margin-left: 10px; cursor: pointer"
                    :key="i"
                    @click="handlePreview(item.fileId)"
                    >{{ item.fileName }}
                  </span>
                  <span
                    :key="i"
                    style="color: #385bb4; cursor: pointer; margin-left: 10px"
                    @click="
                      handelefileLocalDownload(item.fileId, item.fileName)
                    "
                    >下载
                  </span>
                </div>
              </div>
              <fileUpload
                v-else
                v-model.trim="appendixesfileList"
                :isShowTip="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24"> </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="变更内容:" prop="content">
              <el-input
                v-model.trim="formData.content"
                type="textarea"
                placeholder="请输入变更内容"
                :autosize="{ minRows: 4, maxRows: 4 }"
                :style="{ width: '100%' }"
                maxlength="500"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <div slot="footer" class="dialog-footer" v-if="title == '修改'">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button
        type="primary"
        v-loading="updateFileLoading"
        @click="submitForm()"
        >确 定</el-button
      >
    </div>
    <as-pre-view 
      :visible="!!viewId" 
      :id="viewId" 
      ref="viewRef" 
      @close="viewId = undefined"
    />
  </el-dialog>
</template>

<script>
import mixin from "@/layout/mixin/Commmon.js";
import { processstandard } from '@/api/system/standard'

export default {
  mixins: [mixin],
  props: {
    id: {
      type: String,
    },
    title: {
      type: String,
    },
  },
  data: () => ({
    formData: {},
    viewId: undefined,
  }),
  methods: {
    getDetail() {
      // 获取当前登录用户
      const userInfo = JSON.parse(sessionStorage.getItem("USER_INFO"))
      processstandard(this.id)
        .then(({ data }) => data)
        .then(({ changeFactor, nickName, userName, deptId, deptName, applyTime, preAppendixes, ...rest }) => {
          this.formData = {
            ...rest,
            nickName: nickName || userInfo.nickName,
            userName: userName || userInfo.userName,
            deptId: deptId || userInfo.deptId,
            deptName: deptName || userInfo.dept.deptName,
            applyTime: applyTime || new Date(),
          }
          this.changeFactor = changeFactor ? changeFactor.split(',') : []
          this.appendixesfileList = preAppendixes.map(({ fileName, fileId }) => ({ name: fileName, url: fileId }))
        })
    },
    submitForm() {
      this.$emit('close', true)
    },
  },
  mounted() {
    this.getDetail()
  },
};
</script>
