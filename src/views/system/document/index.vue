<template>
  <div>
    <el-card>
      <div>
        <el-form inline>
          <el-form-item :label="$t('sys_mgr.account_data_type')">
            <el-select :placeholder="$t('sys_mgr.account_data_type')" v-model="filter.dataType" style="width: 150px;">
              <!--              <el-option value="stdd" label="体系" />-->
              <!--              <el-option value="project" label="项目" />-->
              <el-option
                v-for="(item,index) in dict.type.ledger_data_type"
                :key="index"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('myItem.borrow_file_type')">
            <treeselect
              v-model="filter.docClass"
              :options="docClassTree"
              :normalizer="normalizer"
              :show-count="true"
              :searchable="false"
              :placeholder="$t('doc.this_dept_select_type')"
              style="width: 150px;"
            />
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.account_fuzzy_matching')">
            <el-input
              v-model="filter.searchValue"
              :placeholder="$t('sys_mgr.account_input_text')"
              @keyup.enter.native="handleQuery"
              class="input-with-select"
              style="width: 200px;"
            ></el-input>
          </el-form-item>
          <el-form-item :label="$t('doc.this_dept_status')">
            <el-select v-model="filter.status" :placeholder="$t('doc.this_dept_pls_select')" style="width: 100px;" @change="handleQuery()">
              <el-option :label="$t('sys_mgr.account_all')" value=""></el-option>
              <!--              <el-option label="草稿" value="0"></el-option>-->
              <!--              <el-option label="有效" value="1"></el-option>-->
              <!--              <el-option label="失效" value="2"></el-option>-->
              <el-option
                v-for="(item,index) in dict.type.ledger_status"
                :key="index"
                :label="dictLanguage(item)"
                :value="item.value"
                :disabled="item.disabled"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('sys_mgr.account_whether_origin')">
            <el-select v-model="filter.initFile" :placeholder="$t('doc.this_dept_pls_select')" style="width: 100px;" @change="handleQuery()">
              <el-option :label="$t('sys_mgr.account_all')" value=""></el-option>
              <!--              <el-option label="是" value="1"></el-option>-->
              <!--              <el-option label="否" value="0"></el-option>-->
              <el-option
                v-for="(item,index) in dict.type.ledger_whether_origin"
                :key="index"
                :label="dictLanguage(item)"
                :value="item.value"
                :disabled="item.disabled"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="handleQuery">{{ $t('doc.this_dept_query') }}</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('myItem.handle_reset') }}</el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-card class="gray-card">
        <el-table
          ref="singleTable"
          :data="postList"
          v-loading="loading"
          highlight-current-row
          header-align="left"
          @selection-change="handleSelectionChange"
          @current-change="handleCurrentChange"
        >
          <el-table-column type="selection" align="center" />
          <!--
          <el-table-column prop="id" label="ID" show-overflow-tooltip align="left" />
          -->
          <el-table-column prop="dataType" :label="$t('sys_mgr.account_data_type')" align="left" min-width="50">
            <template slot-scope="scope">
              <span v-if="scope.row.dataType == 'stdd'">{{ $t('file_handle.change_sys') }}</span>
              <span v-else-if="scope.row.dataType == 'project'">{{ $t('file_handle.change_project') }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="docName"
            :label="$t('myItem.borrow_file_name')"
            :show-overflow-tooltip="true"
            align="left"
          >
            <template slot-scope="scope">
              <span class="wenjcolor">{{ scope.row.docName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="docClass" :label="$t('file_set.type_type')" align="left" min-width="50">
            <template slot-scope="scope">{{ handleFileType(scope.row.docClass) }}</template>
          </el-table-column>
          <el-table-column prop="fileType" :label="$t('sys_mgr.doc_extension')" align="left" min-width="50"></el-table-column>
          <el-table-column prop="docId" :label="$t('sys_mgr.account_num')" align="left"></el-table-column>
          <el-table-column prop="versionValue" :label="$t('doc.this_dept_ver')" align="left" min-width="50"/>
          <el-table-column prop="applyTime" :label="$t('doc.this_dept_record_time')" show-overflow-tooltip align="left">
            <template slot-scope="scope">
              <span>
                {{
                  parseTime(scope.row.applyTime, "{y}-{m}-{d} {h}:{i}:{s}")
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="startDate" :label="$t('doc.this_dept_effective_date')" show-overflow-tooltip align="left">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.startDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="releaseTime" :label="$t('doc.this_dept_release_date')" show-overflow-tooltip align="left">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.releaseTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" :label="$t('doc.this_dept_status')" align="left" min-width="50">
            <template slot-scope="scope">
              <span v-show="scope.row.versionValue != null && scope.row.status == 0">{{ $t('doc.this_dept_draft') }}</span>
              <span v-show="scope.row.versionValue != null && scope.row.status == 1">{{ $t('doc.this_dept_validity') }}</span>
              <span v-show="scope.row.versionValue != null && scope.row.status == 2">{{ $t('doc.this_dept_Invalid') }}</span>
              <span v-show="scope.row.versionValue == null ">{{ $t('file_set.number_null') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="initFile" :label="$t('sys_mgr.account_whether_origin')" align="left" min-width="50">
            <template slot-scope="scope">
              <span v-show="scope.row.initFile == 0">{{ $t('doc.this_dept_no') }}</span>
              <span v-show="scope.row.initFile == 1">{{ $t('doc.this_dept_yes') }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('myItem.msg_operation')"
            align="left"
            class-name="small-padding fixed-width"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleDetails(scope.row, $t('doc.this_dept_detail'))"
              >{{ $t('doc.this_dept_detail') }}</el-button>
              <el-button
                v-show="false"
                size="mini"
                type="text"
                @click="handleDetails(scope.row, $t('doc.this_dept_edit'))"
                v-hasPermi="['system:document:update']"
              >{{ $t('doc.this_dept_edit') }}</el-button>
              <el-button
                v-show="false"
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:document:delete']"
              >{{ $t('doc.this_dept_delete') }}</el-button>
              <el-button
                v-if="scope.row.versionValue != null && ['0', '1'].includes(scope.row.status)"
                size="mini"
                type="text"
                @click="handleStandardReplaceFile(scope.row)"
                v-hasPermi="['system:document:replace']"
              >{{ $t('doc.this_dept_replace_file') }}</el-button>
              <el-button
                v-show="scope.row.versionValue != null && scope.row.status === '0'"
                size="mini"
                type="text"
                @click="handleStandardUpdateStatus(scope.row, '1')"
                v-hasPermi="['system:document:effect']"
              >{{ $t('doc.this_dept_take_effect') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="pagination.total > 0"
        :total="pagination.total"
        :page.sync="pagination.pageNum"
        :limit.sync="pagination.pageSize"
        :page-sizes="[5, 10, 30, 40, 50, 100]"
        @pagination="getList"
      />
    </el-card>
    <Detail
      v-if="!!detailId"
      :id="detailId"
      :title="detailTitle"
      @close="closeDetail"
    />
  </div>
</template>

<script>
import mixin from "@/layout/mixin/Commmon.js";
import Treeselect from "@riophae/vue-treeselect";
import Detail from './Detail.vue'
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { standardList, standardReplaceFile, standardUpdateStatus } from '@/api/system/standard'
import { settingDocClassList } from "@/api/file_settings/type_settings";

export default {
  dicts: ["ledger_status","ledger_whether_origin","ledger_data_type"],
  name: 'SystemDocumentPage',
  components: {
    Treeselect,
    Detail,
  },
  mixins: [mixin],
  data: () => ({
    /**
     * 列表数据
     */
    postList: [],
    /**
     * 分页查询条件
     */
    pagination: {
      pageNum: 1,
      pageSize: 10,
      total: 0,
    },
    /**
     * 筛选查询条件
     */
    filter: {
      // 默认为体系文件范围
      dataType: 'stdd',
      docClass: null,
      searchValue: null,
      status: null,
      initFile: null
    },
    /**
     * 加载状态
     */
    loading: false,
    /**
     * 文件类型（树形结构）
     */
    docClassTree: [],
    /**
     * 详情框标题
     */
    detailTitle: '',
    /**
     * 当前文件ID
     */
    detailId: undefined,
  }),
  computed: {
    queryParams() {
      return {
        ...this.filter,
        ...this.pagination,
      };
    },
  },
  watch: {
    "filter.dataType"(val) {
      this.getclassLevelOptions()
    },
    "filter.docClass"(val) {
      this.handleQuery()
    },
  },
  mounted() {
    // 加载文件类型树结构
    this.getclassLevelOptions()
    // 执行查询
    this.handleQuery()
  },
  methods: {
    treeSelectChange() {
      // 执行查询
      this.handleQuery()
    },
    /**
     * 获取文档列表
     */
    getList() {
      this.loading = true;
      standardList(this.queryParams)
        .then(({ rows, total }) => {
          this.postList = rows;
          this.pagination.total = total;
        })
        .finally(() => {
          this.loading = false
        })
    },
    /**
     * 查询按钮事件
     */
    handleQuery() {
      this.pagination.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.filter = {dataType:'stdd'}
      this.handleQuery();
    },
    /**
     * 查询文件类型
     */
    getclassLevelOptions() {
      let self = this
      self.loading = true
      self.docClassTree = [];
      settingDocClassList({  classStatus: "1", dataType: self.filter.dataType }).then(
        (response) => {
          response.rows.forEach((element, index) => {
            response.rows[index].children = [];
          });
          self.docClassTree = this.handleTree(
            response.rows,
            "id",
            "parentClassId"
          );
          self.loading = false
        }
      );
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    /**
     * 打开详情窗
     */
    handleDetails({ id }, title) {
      this.detailTitle = title
      this.detailId = id
    },
    /**
     * 关闭详情窗
     */
    closeDetail(refresh = false) {
      this.detailId = undefined
      if (refresh) {
        this.handleDetails()
      }
    },
    /**
     * 替换文件
     */
    handleStandardReplaceFile({ id, docId, versionId, }) {
      this.$modal
        .confirm(this.$t('sys_mgr.doc_text'))
        .then(() => {
          const input = document.createElement('input')
          input.type = 'file'
          input.accept = '.doc,.docx,.xls,.xlsx,.pdf'
          input.multiple = false
          input.addEventListener('change', (e) => {
            const file = e.target.files[0]
            // TODO: 校验文件类型
            // TODO: 校验文件大小
            const formData = new FormData()
            formData.append('file', file)
            formData.append('id', id)
            formData.append('docId', docId)
            formData.append('versionId', versionId)
            this.loading = true
            standardReplaceFile(formData)
              .then(() => {
                this.getList()
              })
              .finally(() => {
                this.loading = false
              })
          })
          input.click()
        })
    },
    /**
     * 变更文件状态（生效）
     */
    handleStandardUpdateStatus(row, newStatus) {
      let _this = this
      _this.$modal.confirm(this.$t('sys_mgr.doc_whether_effc')).then(function () {
        _this.loading = true
        // 只针对有效的版本进行变更
        standardUpdateStatus("1",[row.id]).then(() => {
          _this.$modal.msgSuccess(this.$t('sys_mgr.doc_succ_effc'));
          _this.getList()
        }).finally(() => {
          _this.loading = false
        })
      }).then(() => {

      }).catch(() => { });
    },
    // 检验行记录必要信息
    checkForm(row) {
      if(row.docClass.trim().length = 0) {
        this.$modal.alert('类型不能为空')
        return false
      } else if(row.docId.trim().length = 0) {
        this.$modal.alert('编号不能为空')
        return false
      } else if(row.versionValue.trim().length = 0) {
        this.$modal.alert('版本不能为空')
        return false
      } else if(row.startDate.trim().length = 0) {
        this.$modal.alert('生效日期不能为空')
        return false
      } else if(row.releaseTime.trim().length = 0) {
        this.$modal.alert('发布日期不能为空')
        return false
      }
      return true
    }
  },

};
</script>
