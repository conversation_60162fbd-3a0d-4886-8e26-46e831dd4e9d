<template>
  <div class="login-page" v-loading="loading">
    <div class="login-conts">
      <div class="login-left"><img src="../assets/images/log-bg.png" style="width: 452px;"/></div>
      <div class="login-right">
        <p class="big-title">
          <img src="../assets/images/logo1.png" class="logo" /><span class="tit">{{ $t('login.tit') }}</span>
        </p>
        <p class="title">{{ $t('login.title') }}</p>
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules">
          <el-form-item prop="username">
            <el-input v-model="loginForm.username" type="text" auto-complete="off" :placeholder="$t('login.username')"
                      prefix-icon="el-icon-user"></el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input v-model="loginForm.password" type="password" auto-complete="off" :placeholder="$t('login.password')"
                      @keyup.enter.native="handleLogin" prefix-icon="el-icon-lock"></el-input>
          </el-form-item>
          <el-form-item prop="code" v-if="captchaOnOff">
            <el-input v-model="loginForm.code" auto-complete="off" :placeholder="$t('login.code')"
                      style="width: calc(100% - 110px)" @keyup.enter.native="handleLogin"
                      prefix-icon="icon iconfont icon-yanzhengma">
            </el-input>
            <div class="login-code">
              <img :src="codeUrl" @click="getCode" class="login-code-img" />
            </div>
          </el-form-item>
          <!-- <el-form-item>
            <el-checkbox v-model.trim="loginForm.rememberMe"
              >记住密码
            </el-checkbox>
          </el-form-item> -->
          <el-form-item>
            <el-button size="medium" type="primary" style="width: 100%" @click.native.prevent="handleLogin">
              <span v-if="!loading">{{ $t('login.login') }}</span>
              <span v-else> {{$t('login.loggingIn')}} </span>
            </el-button>
            <div style="float: right" v-if="register">
              <router-link class="link-type" :to="'/register'">{{ $t('login.register') }}
              </router-link>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login";
export default {
  name: "Login",
  data() {
    return {
      codeUrl: "",
      cookiePassword: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        tenant: "rz",
        code: "",
        uuid: "",
        type: "PC",
      },
      // usernameTip: this.$t('login.usernameTip'),
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: this.$t('login.usernameTip') },
        ],
        password: [
          { required: true, trigger: "blur", message: this.$t('login.passwordTip') },
        ],
        code: [{ required: true, trigger: "change", message: this.$t('login.codeTip') }],
      },
      loading: false,
      // 验证码开关
      captchaOnOff: true,
      // 注册开关
      register: false,
      redirect: undefined,
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
        //console.log('redirect',this.redirect);
      },
      immediate: true,
    },
    "loginForm.tenant"(val) {
      console.log(val);
      console.log(process.env);
    },
  },
  created() {
    this.getCode();
    this.getCookie();
  },
  methods: {
    getCode() {
      getCodeImg().then((res) => {
        this.captchaOnOff =
          res.data.captchaOnOff === undefined ? true : res.data.captchaOnOff;
        if (this.captchaOnOff) {
          this.codeUrl = "data:image/gif;base64," + res.data.img;
          this.loginForm.uuid = res.data.uuid;
        }
      });
    },
    getCookie() {
      const username = this.$cache.local.get("username");
      const password = this.$cache.local.get("password");
      const rememberMe = this.$cache.local.get("rememberMe");
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        // password: password === undefined ? this.loginForm.password : decrypt(password),
        password: null,
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
        tenant: "rz",
      };
    },
    handleLogin() {
      // console.log("this.$route.query.TenantId", this.$route.query.TenantId);
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            this.$cache.local.set("username", this.loginForm.username);
            this.$cache.local.set("rememberMe", this.loginForm.rememberMe);
            this.$cache.local.set("TenantId", this.$route.query.TenantId);
          } else {
            this.$cache.local.remove("username");
            this.$cache.local.remove("password");
            this.$cache.local.remove("rememberMe");
            this.$cache.local.remove("TenantId");
          }

          this.loginForm.type = "PC";
          this.loginForm.TenantId = this.$route.query.TenantId;
          if (this.loginForm.tenant == "sk") {
            this.loginForm.longinUrl = process.env.VUE_APP_SK_LOGIN;
          } else if (this.loginForm.tenant == "ivd") {
            this.loginForm.longinUrl = process.env.VUE_APP_IVD_LOGIN;
          } else if (this.loginForm.tenant == "rz") {
            this.loginForm.longinUrl = process.env.VUE_APP_RZ_LOGIN;
          } else {
            this.loginForm.longinUrl = "/login";
          }
          this.$store
            .dispatch("Login", this.loginForm)
            .then(() => {
              // cache.session.set('client', 'as7')
              this.$router.push({ path: this.redirect || "/" }).catch(() => {});

              /* fetchLocaleMessages('en').then(() => {
                 this.$router.push({ path: this.redirect || "/" }).catch(() => { });
               }).catch(() => {
                 this.$message.error('语言包数据获取失败')
               })
               */

            })
            .catch(() => {
              this.loading = false;
              if (this.captchaOnOff) {
                this.getCode();
              }
            });
        }
      });
    },
  },
  mounted() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.loginRules.username[0].message = this.$t('login.usernameTip')
        this.loginRules.password[0].message = this.$t('login.passwordTip')
        this.loginRules.code[0].message = this.$t('login.codeTip')
      },500)
    })
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login-code {
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.login-code-img {
  height: 38px;
}
</style>
