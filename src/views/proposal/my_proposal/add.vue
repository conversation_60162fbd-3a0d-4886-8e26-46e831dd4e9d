<template>
  <div>
    <el-row :gutter="15">
      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        size="medium"
        label-width="100px"
      >
        <el-col :span="24">
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item :label="$t(`doc.this_dept_file_name`)+`:`" prop="docName">
                <div>
                  <el-input
                    style="width: 80%; margin-right: 10px"
                    v-model.trim="formData.docName"
                    :placeholder="$t(`doc.this_dept_select_file1`)"
                  ></el-input>
                  <el-button type="primary" @click="handleSelect"
                    >{{ $t(`file_set.type_select`) }}</el-button
                  >
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>

        <el-col :span="24">
          <el-row gutter="15">
            <el-col :span="12">
              <el-form-item :label="$t(`doc.this_dept_file_code`)+`:`">
                {{ formData.docId }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t(`doc.this_dept_file_type`)+`:`">
                <span>{{ formData.docTypeName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>

        <el-col :span="12">
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item :label="$t(`doc.this_dept_file_versions`)">
                <span>{{ formData.versionValue }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>

        <el-col :span="12">
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item :label="$t(`doc.this_dept_put_proposal`)+`:`">
                <span>{{ userInfo.nickName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>

        <el-col :span="12">
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item :label="$t(`myItem.borrow_preparation_dept`)+`:`">
                <span>{{ formData.docDeptName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12">
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item :label="$t(`doc.this_dept_put_time`)+`:`">
                <span>{{ parseTime(nowDate) }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="24">
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item :label="$t(`doc.this_dept_summary_remark`)+`:`" prop="summary">
                <el-input
                  v-model.trim="formData.summary"
                  type="textarea"
                  :placeholder="$t(`doc.this_dept_ins_summary_remark`)"
                  :autosize="{ minRows: 4, maxRows: 4 }"
                  :style="{ width: '100%' }"
                  maxlength="500"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="24">
          <el-form-item size="large">
            <el-button type="primary" @click="submitForm">{{ $t(`doc.this_dept_annex`) }}</el-button>
            <el-button @click="handleClose">{{ $t(`doc.this_dept_close`) }}</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>

    <el-dialog
      :title="$t(`file_handle.change_file`)"
      :visible.sync="open"
      width="60%"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form :model="query" :inline="true" label-width="68px">
        <el-row type="flex">
          <el-col :span="12">
            <el-form-item label="" prop="postCode">
              <el-input
                v-model.trim="query.searchValue"
                :placeholder="$t(`doc.this_dept_insert_keyword`)"
                clearable
                size="small"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="getFileList"
                >{{ $t(`doc.this_dept_search`) }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table
        v-loading="openLoading"
        :data="openList"
        @selection-change="handleSelectionFileChange"
        ref="multipleTable"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          :label="$t(`myItem.borrow_file_name`)"
          align="center"
          width="200"
          prop="docName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          :label="$t(`myItem.borrow_file_id`)"
          align="center"
          prop="docId"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          :label="$t(`doc.this_dept_file_type`)"
          align="center"
          prop="docClass"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span>{{ handleFileType(scope.row.docClass) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t(`myItem.borrow_file_ver`)"
          align="center"
          prop="versionValue"
          :show-overflow-tooltip="true"
        />

        <el-table-column
          :label="$t(`myItem.borrow_preparation_dept`)"
          align="center"
          prop="deptName"
          :show-overflow-tooltip="true"
        />
      </el-table>
      <pagination
        v-show="openTotal > 0"
        :total="openTotal"
        :page.sync="query.pageNum"
        :limit.sync="query.pageSize"
        @pagination="getFileList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleSubmitFile">{{ $t(`file_set.type_confim`) }}</el-button>
        <el-button @click="open = false">{{ $t(`file_set.type_cancel`) }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listVersion } from "@/api/document_account/version";
import { settingDocClassList } from "@/api/file_settings/type_settings";
import { addFileAdvise } from "@/api/file_processing/fileAdvise";

export default {
  components: {},
  props: [],
  data() {
    return {
      open: false,
      openLoading: false,
      openList: [],
      openTotal: 0,
      fileTypeList: [],
      // 查询参数
      query: {
        pageNum: 1,
        pageSize: 10,
        status: "1",
        searchValue: "",
      },
      selectInfo: [],
      nowDate: new Date(),
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      formData: {
        docId: "",
        docName: "",
        docTypeCode: "",
        docTypeName: "",
        versionId: "",
        versionValue: "",
        docDeptId: "",
        docDeptName: "",
        summary: "",
      },
      rules: {
        summary: [
          {
            required: true,
            message: this.$t(`doc.this_dept_ins_summary_remark`),
            trigger: "blur",
          },
          {
            max: 500,
            message: this.$t(`doc.this_dept_summary_remark_more_than500`),
          },
        ],
        // docName: [
        //   {
        //     required: true,
        //     message: "请选择",
        //     trigger: "blur",
        //   },
        // ],
      },
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getFileTyp();
  },
  mounted() {},
  methods: {
    submitForm() {
      this.$refs["elForm"].validate((valid) => {
        if (!valid) return;
        // TODO 提交表单
        addFileAdvise(this.formData).then((res) => {
          this.$message.success(this.$t(`file_handle.change_succ`));
          this.handleClose();
        });
      });
    },
    handleSubmitFile() {
      if (this.selectInfo.length > 1) {
        this.$message.error(this.$t(`doc.this_dept_pls_one_file`));
        return;
      }
      this.formData.docDeptId = this.selectInfo[0].deptId;
      this.formData.docId = this.selectInfo[0].docId;
      this.formData.docName = this.selectInfo[0].docName;
      this.formData.docDeptName = this.selectInfo[0].deptName;
      this.formData.docTypeCode = this.selectInfo[0].docClass;
      this.formData.versionId = this.selectInfo[0].id;
      this.formData.versionValue = this.selectInfo[0].versionValue;
      this.formData.docTypeName = this.handleFileType(
        this.selectInfo[0].docClass
      );
      this.open = false;
    },
    handleSelectionFileChange(select) {
      this.selectInfo = select;
    },
    handleSelect() {
      this.open = true;
      this.getFileList();
    },
    getFileTyp() {
      settingDocClassList({ classStatus: "1" }).then(
        (response) => {
          this.fileTypeList = response.rows;
        }
      );
    },
    handleFileType(obj) {
      if (obj && this.fileTypeList) {
        let arr = this.fileTypeList.filter((x) => x.id === obj)[0];
        if (arr) {
          return arr.className;
        }
        return obj;
      }
    },
    getFileList() {
      this.openLoading = true;
      listVersion(this.query).then((res) => {
        this.openLoading = false;
        this.openList = res.rows;
        this.openTotal = res.total;
      });
    },
    handleClose() {
      this.formData.docId = "";
      this.formData.docName = "";
      this.formData.docDeptName = "";
      this.formData.docTypeCode = "";
      this.formData.versionId = "";
      this.formData.versionValue = "";
      this.formData.docTypeName = "";
      this.formData.summary = "";
      this.$emit("close", false);
    },
  },
};
</script>
<style>
</style>
