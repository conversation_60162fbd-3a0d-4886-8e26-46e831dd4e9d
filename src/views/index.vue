
	<template>
  <div id="app"  v-loading="loading">
  <el-row :gutter="20">
    <el-col :span="12">
        <el-card class="index-card">
            <div class="el-card__header">
              <div class="clearfix"><span>{{ $t(`home.quick_action`) }}</span>
                <el-button style="float:right" circle icon="el-icon-refresh" @click="refresh()"></el-button>
              </div>
            </div>
            <div class="">
            <div class="kjcz-box">
              <div class="list">
                <router-link :to="{ name: 'Msgs'}">
                  <img src="../assets/images/icon2.png" />
                  <p class="title">{{ $t(`home.quick_action_my_msg`) }}</p></router-link>
              </div>
              <div class="list">
                 <router-link :to="{ name: 'Company/index', query: {'dataType':'stdd','status':'1'} }">
              <img src="../assets/images/icon3.png" />
              <p class="title">{{ $t(`home.quick_action_file_ledger`) }}</p></router-link>
              </div>
              <div
                class="list"
              >
                <router-link :to="{ name: 'Company/index', query: {'dataType':'stdd','status':'1','openFunc':'add'} }">
                <img src="../assets/images/icon4.png" />
                <p class="title">{{ $t(`home.quick_action_file_add`) }}</p></router-link>
              </div>
              <div
                class="list"
              >
                <router-link v-hasPermi="['process:standard:batch:add']" :to="{ name: 'Company/index', query: {'dataType':'stdd','status':'1','openFunc':'add_batch'} }">
                  <img src="../assets/images/addBatch.png" />
                  <p class="title">{{$t(`doc.batch_title_text_4`)}}{{ $t(`home.quick_action_file_add`) }}</p></router-link>
              </div>
              <div class="list">
                <router-link :to="{ name: 'Company/invalid_index', query: {'dataType':'stdd','status':'2'}}">
                  <img src="../assets/images/icon5.png" />
                  <p class="title">{{ $t(`home.quick_action_invalid_ledger`) }}</p></router-link>
              </div>
              <div class="list">
                <router-link :to="{ name: '/personal/index'}">
                  <img src="../assets/images/icon1.png" />
                  <p class="title">{{ $t(`home.quick_file_statistic`) }}</p></router-link>
              </div>
            </div>

        </div>
        <div class="el-card index-card is-always-shadow">
          <div class="el-card__header">
            <div class="clearfix"><span>{{ $t(`home.guide`) }}</span></div>
          </div>
          <div class="el-card__body">
            <div class="xszy-box">
              <div class="head-list">
                <div class="list">
                  <img src="../assets/images/apart1.png" />{{ $t(`home.guide_org_maint`) }}
                </div>
                <div class="list">
                  <img src="../assets/images/apart2.png" />{{ $t(`home.guide_maint_file_type`) }}
                </div>
                <div class="list">
                  <img src="../assets/images/apart3.png" />{{ $t(`home.guide_file_change`) }}
                </div>
                <div class="list">
                  <img src="../assets/images/apart4.png" />{{ $t(`home.guide_file_process`) }}
                </div>
              </div>
              <div class="body-list">
                <div class="list">
                  <div class="lab lab-4">{{ $t(`home.guide_maint_company_org`) }}</div>
                  <div class="lab lab-4">{{ $t(`home.guide_maint_user_auth`) }}</div>
                  <div class="lab lab-4">{{ $t(`home.guide_add_sys_user`) }}</div>
                </div>
                <div class="list">
                  <div class="lab lab-2">{{ $t(`home.guide_add_file_type`) }}</div>
                  <div class="lab lab-2">{{ $t(`home.guide_set_file_type`) }}</div>
                </div>
                <div class="list">
                  <div class="lab lab-2">{{ $t(`home.guide_change_appl`) }}</div>
                  <div class="lab lab-3">{{ $t(`home.guide_add_edit_void_file`) }}</div>
                  <div class="lab lab-1">{{ $t(`home.guide_file_review`) }}</div>
                  <div class="lab lab-1">{{ $t(`home.guide_file_sgn_appr`) }}</div>
                  <div class="lab lab-3">{{ $t(`home.guide_file_train`) }}</div>
                </div>
                <div class="list">
                  <div class="lab lab-5">{{ $t(`home.guide_file_preview`) }}</div>
                  <div class="lab lab-3">{{ $t(`home.guide_file_hand_out`) }}</div>
                  <div class="lab lab-3">{{ $t(`home.guide_file_receipt`) }}</div>
                  <div class="lab lab-3">{{ $t(`home.guide_file_print`) }}</div>
                  <div class="lab lab-3">{{ $t(`home.guide_file_recycle`) }}</div>
                </div>
              </div>
              <!--
              <div class="annot">
                <div class="text strator1">领导</div>
                <div class="text strator2">公司文件管理员</div>
                <div class="text strator3">文控</div>
                <div class="text strator4">编制人</div>
                <div class="text strator5">公司员工个人</div>
              </div>
              -->
            </div>
          </div>
        </div>
        </el-card>
        <el-card class="index-card">
            <div slot="header" class="clearfix"><span>{{ $t(`home.recent_view`) }}</span></div>
            <div class="file_list ck_list">
                <div class="no-cont" v-if="lastViewList.length == 0">
                  <div class="img"><img src="../assets/images/bg3.png" /></div>
                  <p class="text">{{ $t(`doc.this_dept_data_is_null`) }}~</p>
                </div>
                <div class="list" v-for="(item, index) in lastViewList" :key="index">
                    <div class="title-s">
                        <div class="tit-ico"><img src="images/frame1.png" /></div>
                        <div class="tit-txt">{{item.docName}}</div>
                        <div class="tit-time">{{parseTime(item.previewTime)}}</div>
                    </div>
                    <div class="text-s">
                        <span class="txt"><img class="ico" src="images/frame4.png" />{{item.versionValue}}</span>
                        <span class="txt"><img class="ico" src="images/frame3.png" />{{item.deptName}}</span>
                    </div>
                </div><!--list--->
            </div><!-----file_list---->
        </el-card>
    </el-col>
    <el-col :span="12">
        <el-card class="index-card">
            <div slot="header" class="clearfix"><span>{{ $t(`home.my_to_do`) }}（{{todoList.length}}）</span></div>
            <div class="file_list db_list">
                <div class="no-cont" v-if="todoList.length == 0">
                  <div class="img"><img src="../assets/images/bg3.png" /></div>
                  <p class="text">{{ $t(`doc.this_dept_data_is_null`) }}~</p>
                </div>
                <!-- 待办列表项 -->
                <div class="list" v-for="(item, index) in todoList" :key="index">
                    <div class="title-s">
                        <div class="tit-point"><span class="point"></span></div>
                        <div class="tit-txt">
                           <span @click="handleDeal(item)" class="text-name" >{{item.title}}</span>
                        </div>
                        <div class="tit-time">{{parseTime(item.sendTime)}}</div>
                    </div>
                    <div class="text-s">
                        <span class="txt"><img class="ico" src="images/frame5.png" />{{item.procDefName}}</span>
                        <!--
                        <span class="txt"><img class="ico" src="images/frame6.png" />***********-001</span> -->
                    </div>
                </div><!--list--->

            </div><!-----file_list---->
        </el-card>
        <el-card class="index-card">
            <div slot="header" class="clearfix"><span>{{ $t(`home.newsflash`) }}（{{messageList.length}}）</span></div>
            <div class="file_list">
                <div class="no-cont" v-if="messageList.length == 0">
                  <div class="img"><img src="../assets/images/bg3.png" /></div>
                  <p class="text">{{ $t(`doc.this_dept_data_is_null`) }}~</p>
                </div>
                <div class="list" v-for="(item, index) in messageList" :key="index" >
                    <div class="title-s">
                        <div class="tit-txt">{{item.msgInfo}}</div>
                        <div class="tit-time">{{parseTime(item.createTime)}}</div>
                    </div>
                    <div class="text-s">
                      <!--
                        <span class="txt"><img class="ico" src="images/frame3.png" />{{item.deptName}}</span> -->
						            <span class="txt"><img class="ico" src="images/frame7.png" />{{item.recoveryUser}}</span>
                    </div>
                </div><!--list--->
            </div><!-----file_list---->
        </el-card>
    </el-col>
</el-row>
<!-- 流程处理抽屉组件 -->
<DealDrawer v-if="dealDrawerShow" ref="dealDrawer" @closeDrawer="handleCloseChange"></DealDrawer>
  </div>
  <!--app-->
</template>

<script>
import mixin from "@/layout/mixin/Commmon.js";
import {
  indexSelectReadList,
  indexSelectMessage,
} from "@/api/index/select";
import { workflowToDoList } from "@/api/my_business/workflow";
import DealDrawer from "@/components/DealDrawer";
export default {
  name: "Index",
  components: {
    DealDrawer
  },
  mixins: [mixin],
  data() {
    return {
      bomPath:process.env.VUE_APP_BOM_PATH,
      shlkPath:process.env.VUE_APP_SHLK_PATH,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageNumber: 1,
        pageSize: 1000,
        title: "",
        status: 1,
      },
      // 待办列表
      todoList: [],
      // 最近查看列表
      lastViewList: [],
      // 消息速递
      messageList: [],
      dealDrawerShow: false,
      loading: false,
    };
  },
  watch: {

  },
  created() {
    // 查询待办列表
    this.getWorkflowToDoList()
    // 查询最近查看列表
    this.getLastViewList()
    // 查询消息速递
    this.getMessageList()
  },
  methods: {
    refresh(){
      window.location.reload(true)
    },
    handleKuaijie(name, e) {
      this.$tab.openPage(name, e);
    },
    // 查询消息速递
    getMessageList() {
      this.loading = true
      let queryParams = {
        pageNum: 1,
        pageSize: 1000,
        // 查询未读的
        msgStatus: '0'
      }
      indexSelectMessage(queryParams).then((res) => {
        this.messageList = res.data.rows;
        this.loading = false
      });
    },
    // 查询最近查看列表
    getLastViewList() {
      this.loading = true;
      this.lastViewList = [];
      indexSelectReadList({}).then((response) => {
        this.lastViewList = response.data;
        this.loading = false;
      });
    },
    // 查询待办列表
    getWorkflowToDoList() {
      this.loading = true;
      this.todoList = [];
      this.queryParams.searchType = 'original_bpmn'
      workflowToDoList(this.queryParams).then((response) => {
        this.$store.commit("SET_THINGNUMBER", response.total || 0);
        if(response.rows) {
          this.todoList = response.rows;
        }
        this.loading = false;
      });
    },
    // 流程处理事件
    handleDeal(row) {
      this.dealDrawerShow = true;
      let flowUrl = this.buildFlowUrl(row)
      // alert(flowUrl)
      //window.open(flowUrl)
      /*   */
      if (JSON.parse(sessionStorage.getItem('winOpen'))) {
        window.open(flowUrl)
      } else {
        this.$nextTick(() => {
          this.$refs.dealDrawer.init(flowUrl);
        });
      }
     //
    },
    buildFlowUrl(row) {
      let res = row.url;
      //待办=1、已办=2、办结=3
      res += "&invokeFrom=index&status="+row.status
      return res;
    },
    handleCloseChange() {
      this.dealDrawerShow = false
      this.getWorkflowToDoList();
    },
  },
};
</script>
<style lang="scss">
.vue-treeselect__control {
  height: 32px;
}
.text-name {
  width: 200px; /* 设置元素宽度 */
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 文本溢出显示省略号 */
  cursor: pointer; /* 设置鼠标样式 */
}
.text-name:hover {
  white-space: normal; /* 鼠标悬浮时换行显示全部内容 */
  overflow: visible; /* 不再隐藏文本 */
  text-overflow: clip; /* 移除省略号 */
}
@import "../../public/css/poctstyle.css";
</style>
