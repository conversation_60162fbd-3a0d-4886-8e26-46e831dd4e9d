<template>
  <div class="app-container el-card is-always-shadow">
    <div class="el-card__body">
      <el-form
        :model="queryParams"
        ref="queryParams"
        :label-width="columnLangSizeFlag ? '88px' : '68px'"
      >
        <div class="global-ser" id="add">
          <div class="ser-top">
            <div class="cell-left">
              <el-form-item :label="$t(`doc.this_dept_file_name`)">
                <el-input
                  v-model.trim="queryParams.docName"
                  :placeholder="$t(`doc.this_dept_insert`) + $t(`doc.this_dept_file_name`)"
                  clearable
                  @keyup.enter.native="handleQuery"
                >
                </el-input>
              </el-form-item>
              <el-form-item :label="$t(`doc.this_dept_file_code`)">
                <el-input
                  v-model.trim="queryParams.docId"
                  :placeholder="$t(`doc.this_dept_insert`) + $t(`doc.this_dept_file_code`)"
                  clearable
                  @keyup.enter.native="handleQuery"
                >
                </el-input>
              </el-form-item>
              <el-form-item :label="$t(`doc.this_dept_sign_dept`)">
                <el-input
                  v-model.trim="queryParams.receiveUserDept"
                  :placeholder="$t(`doc.this_dept_insert`) + $t(`doc.this_dept_sign_dept`)"
                  clearable
                  @keyup.enter.native="handleQuery"
                >
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search"  type="primary" @click="handleQuery">{{ $t(`doc.this_dept_query`) }}</el-button>
                <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t(`doc.this_dept_reset`) }}</el-button>
              </el-form-item>
            </div>
            <div class="cell-right">
              <el-button icon="el-icon-edit"  type="primary" @click="handleEvent">{{ $t(`file_handle.receive_sign_for`) }}</el-button>
            </div>
          </div>
        </div>
      </el-form>
      <el-card class="gray-card">
        <el-table
          header-align="left"
          v-loading="loading"
          :data="dataList"
          border
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" :selectable="selectable" align="left" />
          <el-table-column
            :label="$t(`doc.this_dept_file_type`)"
            align="left"
            prop="className"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            :label="$t(`doc.this_dept_file_name`)"
            align="left"
            width="300"
            prop="docName"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span class="wenjcolor">{{ scope.row.docName }}</span>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t(`doc.this_dept_file_code`)"
            align="left"
            prop="docId"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`doc.this_dept_file_versions2`)"
            align="left"
            prop="versionValue"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`doc.this_dept_sign_dept`)"
            align="left"
            prop="receiveUserDept"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`doc.this_dept_distribute_info`)"
            align="left"
            prop="code"
            :formatter="formatterCode"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`doc.distribution_time`)"
            align="left"
            prop="createTime"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`file_handle.receive_signed_status`)"
            align="left"
            prop="receive"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{scope.row.receive?$t(`file_handle.receive_have_been_signed`):$t(`file_handle.receive_unsigned`)}}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t(`doc.this_dept_sign_date`)"
            align="left"
            prop="receiveTime"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{parseTime(scope.row.receiveTime,"{y}-{m}-{d} {h}:{i}")}}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t(`doc.this_dept_operation`)"
            align="left"
            fixed="right"
            width="95"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                v-if="!scope.row.receive"
                @click="handleEvent(scope.row)"
                v-hasPermi="['fileProcessing:documentSign:sign']"
              >{{ $t(`file_handle.receive_sign_for`) }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script>
import { pageDistribute, receiveByIds } from '@/api/process/distribute'
export default {
  name: "Receive",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      // 总条数
      total: 0,
      // 表格数据
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        docName: undefined,
        receiveUserName: undefined,
        docId: undefined,
        type: 'print',
        orderByColumn: 'create_time',
        isAsc: 'desc',
      },
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  created() {
    this.getList();
  },
  mounted() {},
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      this.queryParams.receiveUserName = this.userInfo.userName
      pageDistribute(this.queryParams).then((response) => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    formatterCode(row, column, cellValue, index){
      if (row.code<10) {
        return '0'+row.code;
      }
      return row.code;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.docName = undefined
      this.queryParams.docId = undefined
      this.queryParams.receiveUserDept = undefined
      this.resetForm("queryParams");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    selectable(row, index){
      return !row.receive;
    },
    handleEvent(row) {
      let ids = row.id || this.ids
      if (!ids.length) {
        this.$message.error(this.$t(`file_handle.receive_text`))
        return;
      }
      this.$confirm(this.$t(`file_handle.receive_is_sign_select`), this.$t(`file_handle.change_tip`), {
        confirmButtonText: this.$t(`file_handle.change_confirm`),
        cancelButtonText: this.$t(`doc.this_dept_abolish`),
        type: "warning",
      })
        .then(() => {
          receiveByIds(ids).then((res) => {
            this.$message.success(this.$t(`file_handle.receive_sign_success`));
            this.getList();
          });
        })
        .catch(() => {});
    },
  },
};
</script>
