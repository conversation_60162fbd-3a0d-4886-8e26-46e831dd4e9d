<template>
  <div class="document_change_add"  v-loading="loading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">{{ $t(`home.guide_file_print`) }}</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button @click="close">{{ $t(`doc.this_dept_close`) }}</el-button>
      </div>
    </div>
    <div class="dialog-body">
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`doc.this_dept_base_msg`) }}</div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-form
            ref="elForm"
            :model="formData"
            size="medium"
            label-position="right"
            label-width="150px"
          >
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_names`)" prop="docName">
                  <span>{{formData.docName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_codes`)" prop="docId">
                  <span>{{ formData.docId }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row gutter="15">
              <el-col :span="12">
                <el-form-item :label="$t(`doc.this_dept_file_versions`)" prop="versionValue">
                  <span>{{formData.versionValue}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t(`file_handle.print_current_effec_ver`)">
                  <div class="link-box bzlink-box">
                    <span
                      style="color: #385bb4; cursor: pointer"
                      @click="handlePreview(formData.preStandardDoc.fileId)"
                    >{{ formData.preStandardDoc.fileName }}</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
      <div class="news-card">
        <div class="card-head">
          <div class="cell-title">{{ $t(`file_handle.print_process_record`) }}</div>
          <div class="cell-btn">
            <el-button
              v-hasPermi="['file:docPrint:distribution:auth']"
              @click="disPrintAuth()"
              plain
            >{{ $t(`file_handle.print_assign_authority`) }}</el-button>
          </div>
        </div>
        <el-card class="gray-card table-card no-padding">
          <el-table :data="dataList" border @selection-change="handleSelectionChange" ref="dataListTable">
            <el-table-column
              type="selection"
              width="55"
              align="left"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              :label="$t(`doc.this_dept_sign_dept`)"
              align="center"
              prop="receiveUserDept"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_signatory`)"
              align="center"
              prop="receiveNickName"
              width="200"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_has_print_data_auth`)"
              align="center"
              prop="hasPrintDataAuth"
            >
              <template slot-scope="scope">
                <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.hasPrintDataAuth"/>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_distribute_info`)"
              align="center"
              prop="code"
              :formatter="formatterCode"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_print_paper`)"
              align="center"
              prop="printPaperType"
            >
              <template slot-scope="scope">
                <dict-tag :options="dict.type.print_paper_type" :value="scope.row.printPaperType"/>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_sign_date`)"
              align="center"
              prop="receiveTime"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_recover_status`)"
              align="center"
              prop="status"
              :formatter="formatterStatus"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_recover_date`)"
              align="center"
              prop="recoveryTime"
            >
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_operation`)"
              align="center"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="handleViewPrintDetail(scope.row)"
                >{{ $t(`file_handle.print_detail`) }}</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>
    <!-- 打印明细框 -->
    <el-dialog
      :visible.sync="printDetailShow"
      modal-append-to-body
      destroy-on-close
      :title="$t(`file_handle.print_detail`)"
      width="750px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-table :data="printDetailList" border>
            <el-table-column
              :label="$t(`doc.this_dept_file_name`)"
              align="center"
              prop="fileObj.fileName" :show-overflow-tooltip="true">
            </el-table-column>
            <el-table-column
              :label="$t(`file_handle.print_generation_time`)"
              align="left"
              prop="createTime"
              :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <span>{{parseTime(scope.row.createTime,"{y}-{m}-{d} {h}:{i}")}}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_operation`)"
              align="center"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="handlePrint(scope.row)"
                >{{ $t(`doc.this_dept_printing`) }}</el-button
                >
              </template>
            </el-table-column>
          </el-table>
    </el-dialog>

    <!-- 文件预览组件 -->
    <as-pre-view :visible="viewShow" :id="viewId" ref="viewRef" @close="close"></as-pre-view>
  </div>
</template>
<script>
import {standardGetDetail} from "@/api/document_account/standard";
import {fileLocalPdfView} from "@/api/pdf_preview";
import { authFilterListDistribute } from '@/api/process/distribute'
import {signEffectiveDis} from "@/api/file_processing/fileSignature";
import {listFilePdf} from "@/api/file_processing/basicFilePdf.js";
import {addPrintDataAuth} from "@/api/file_print/printDataAuth.js"
export default {
  name: "PrintDetail",
  props: ['data'],
  dicts: ['sys_yes_no','print_paper_type'],
  data() {
    return {
      viewId: "",
      dataList: [{}],
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      viewShow: false,
      formData: {
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "01",
        docId: undefined,
        deptId: undefined,
        deptName: undefined,
        userName: undefined,
        nickName: undefined,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: undefined,
        appendixes: undefined, //附件
        standardDoc: {fileName:''}, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined,
      },
      loading: false,
      detailLoading: false,
      printDetailList: [],
      printDetailShow: false,
      selectInfoData: [],
    };
  },
  computed: {},
  watch: {
    data (val) {
      if (val) {
        this.init(val)
      }
    },
  },
  mounted() {
    if (this.data) {
      this.init(this.data)
    }
  },
  methods: {
    init(row){
      let _this = this
      _this.rest()
      _this.getDetail(row)
    },
    async getDetail(query) {
      let _this = this
      _this.loading = true
      let res = await standardGetDetail(query)
      _this.formData = res.data;
      _this.loading = false
      _this.detailLoading = true
      authFilterListDistribute({versionId:res.data.versionId,type:'print'}).then(res=>{
        if (res.data&&res.data.length>0) {
          _this.dataList = res.data
        }else{
          _this.dataList = [_this.restData()]
        }
      }).finally(()=>{
        _this.detailLoading = false
      })
    },
    rest(){
      let _this = this
      _this.formData= {
        docClass: undefined,
        changeType: undefined,
        docName: undefined,
        versionValue: "01",
        docId: undefined,
        deptId: _this.userInfo.dept.deptId,
        deptName: _this.userInfo.dept.deptName,
        userName: _this.userInfo.userName,
        nickName: _this.userInfo.nickName,
        currentVersion: undefined,
        changeReason: undefined,
        content: undefined,
        trainDept: undefined,
        applyTime: new Date().getTime(),
        appendixes: undefined, //附件
        standardDoc: {fileName:''}, //编制文件
        docLinks: undefined, //关联文件
        recordLinks: undefined, // 关联记录
        preStandardDoc: "",
        preAppendixes: "",
        preChangeCode: undefined
      }
    },
    close() {
      this.$emit("close")
    },
    formatterCode(row, column, cellValue, index){
      if (row.code<10) {
        return '0'+row.code;
      }
      return row.code;
    },
    formatterStatus(row){
      if (row.status==='lost')  {
        return this.$t(`doc.this_dept_loss`)
      }else if (row.status==='recovery') {
        return this.$t(`file_handle.print_recovered`)
      }else {
        return this.$t(`file_handle.print_unrecover`)
      }
    },
    handlePreview(id) {
      this.viewId = id;
      this.$refs.viewRef.handleOpenView(id);
      this.viewShow = true;
    },
    restData(){
      return  {
        id: undefined,
        versionId: this.formData.versionId,
        code: undefined,
        docId: this.formData.docId,
        docClass: this.formData.docClass,
        docName: this.formData.docName,
        receiveUserName: undefined,
        receiveNickName: undefined,
        receiveUserDeptId: undefined,
        receiveUserDept: undefined,
      }
    },
    // 展示打印明细框
    handleViewPrintDetail(row){
      let self = this
      let query = {
        bizId:row.id,
        pdfType:'distribute',
        status:'YES'
      }
      self.loading = true
      listFilePdf(query).then((res) => {
        if(res.rows.length == 0) {
          // 未生成过分发PDF文件
          self.$modal.alert(self.$t(`file_handle.print_text`));
          self.loading = true
          signEffectiveDis(row.id,row.code).then((res) => {
              self.loading = false
              if (res.code===200) {
                self.handleViewPrintDetail(row)
              } else {
                self.$modal.alert(res.msg);
              }
          });
        } else {
          // 已生成
          self.loading = false
          self.printDetailList = res.rows
          self.printDetailShow = true
        }
      });
    },
    // 打印行记录
    handlePrint(row){
      // fileLocalPdfView(row.pdfId)
      this.$refs.viewRef.handleOpenView(row.pdfId);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectInfoData = selection;
    },
    //分配打印权限
    disPrintAuth(){
      let self = this
      if (self.selectInfoData.length < 1) {
        self.$modal.msg(self.$t(`file_handle.print_least_one_data`));
        return
      }
      self.loading = true
      addPrintDataAuth({docDistributeVoList:self.selectInfoData}).then((res) => {
        if(res.code == 200){
          self.$modal.msgSuccess(self.$t(`file_handle.print_assign_authority_succ`))
          self.selectInfoData = []
          self.$refs.dataListTable.clearSelection();
        }else{
          self.$modal.msgWarning(self.$t(`file_handle.print_assign_authority_fail`))
        }
        self.init(self.data)
        self.loading = false
      }).catch(err => {
        self.loading = false
        console.error(err)})
    }
  },

};
</script>
