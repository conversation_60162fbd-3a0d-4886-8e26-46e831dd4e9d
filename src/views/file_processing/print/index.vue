<template>
  <div class="app-container el-card is-always-shadow">
    <div class="el-card__body">
      <el-form
        :model="queryParams"
        ref="queryParams"
        :label-width="columnLangSizeFlag ? '88px' : '68px'"
      >
        <div class="global-ser" id="add">
          <div class="ser-top">
            <div class="cell-left">
              <el-form-item :label="$t(`doc.this_dept_file_name`)">
                <el-input
                  v-model="queryParams.docName"
                  :placeholder="$t(`doc.this_dept_name_select`)"
                  clearable
                  @keyup.enter.native="handleQuery"
                >
                </el-input>
              </el-form-item>
              <el-form-item :label="$t(`doc.this_dept_file_code`)">
                <el-input
                  v-model="queryParams.docId"
                  :placeholder="$t(`file_handle.change_enter_file_number`)"
                  clearable
                  @keyup.enter.native="handleQuery"
                >
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search"  type="primary" @click="handleQuery">{{ $t(`doc.this_dept_query`) }}</el-button>
                <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t(`doc.this_dept_reset`) }}</el-button>
              </el-form-item>
            </div>
            <div class="cell-right">
            </div>
          </div>
        </div>
      </el-form>
      <el-card class="gray-card">
        <el-table border
          header-align="left"
          v-loading="loading"
          :data="dataList"
        >
          <el-table-column
            :label="$t(`doc.this_dept_file_type`)"
            align="left"
            prop="className"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            :label="$t(`doc.this_dept_file_name`)"
            align="left"
            width="300"
            prop="docName"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span class="wenjcolor">{{ scope.row.docName }}</span>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t(`doc.this_dept_file_code`)"
            align="left"
            prop="docId"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`doc.this_dept_file_versions2`)"
            align="left"
            prop="versionValue"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t(`doc.this_dept_release_time`)"
            align="left"
            prop="releaseTime"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{parseTime(scope.row.releaseTime,"{y}-{m}-{d} {h}:{i}")}}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t(`doc.this_dept_operation`)"
            align="left"
            fixed="right"
            width="95"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleDetail(scope.row)"
              >{{ $t(`doc.this_dept_detail`) }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <el-drawer
      :visible.sync="detailShow"
      direction="rtl"
      size="90%"
      :with-header="false"
      :wrapperClosable="false"
      :show-close="false"
      modal-append-to-body
      :destroy-on-close="true"
    >
      <print-detail ref="detail"  :data="data" @close="handleCloseChange"></print-detail>
    </el-drawer>
  </div>
</template>

<script>
import { authFilterPageDoc } from '@/api/process/distribute'
import PrintDetail from '@views/file_processing/print/detail/index.vue'
export default {
  name: "Print",
  components: { PrintDetail },
  data() {
    return {
      detailShow: false,
      data: undefined,
      selectInfoData: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      // 总条数
      total: 0,
      // 表格数据
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        docName: undefined,
        docId: undefined,
        type: 'print',
        orderByColumn: 'dv.release_time',
        isAsc: 'desc',
      },
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
    };
  },
  created() {
    /**
     * 记录文件打印只看记录台账的数据
     */
    if(this.$route.query.classType){
      this.queryParams.classType = this.$route.query.classType
    }
    this.getList();
  },
  mounted() {},
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      authFilterPageDoc(this.queryParams).then((response) => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.docName = undefined
      this.queryParams.docId = undefined
      this.resetForm("queryParams");
      this.handleQuery();
    },
    handleDetail(row) {
      let _this = this
      _this.data={docId:row.docId,versionId:row.versionId,flag:'0'}
      _this.detailShow = true
    },
    handleCloseChange(){
      let _this = this
      _this.detailShow = false
      _this.getList()
    }
  },
};
</script>
