<template>
  <div class="app-container el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span> {{ $t(`menus.2347`) }} </span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <div class="global-ser" id="add">
          <div class="ser-top">
            <div class="cell-left">
              <el-form-item label="" prop="docId">
                <el-input
                  :placeholder="$t(`dicts.watermark_type_file_number`)"
                  clearable
                  v-model="queryParams.docId"
                ></el-input>
              </el-form-item>
              <el-form-item label="" prop="applyBy">
                <el-input v-model="queryParams.applyNickName" readonly :placeholder="$t(`prepare.id_select_applicant`)" clearable>
                  <span slot="suffix" v-show="queryParams.applyNickName" @click="nickNameClear">
                     <i class="el-icon-close" style="margin-left: 5px;cursor: pointer;"></i>
                  </span>
                  <el-button slot="append" icon="el-icon-more" @click="handleSelect('queryParams')"></el-button>
                </el-input>
              </el-form-item>
              <el-form-item label="" prop="docClass">
                <treeselect
                  style="width: 200px"
                  v-model="queryParams.docClass"
                  :options="docClassTree"
                  :normalizer="normalizer"
                  :show-count="true"
                  :searchable="false"
                  :placeholder="$t(`prepare.id_select_file_type`)"
                />
              </el-form-item>
              <el-form-item label="" prop="createTime">
                <el-date-picker
                  v-model="createTime"
                  type="datetimerange"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :range-separator="$t(`doc.this_dept_to`)"
                  :start-placeholder="$t(`file_set.version_create_time`)"
                  :end-placeholder="$t(`file_set.version_create_time`)">
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{$t(`doc.this_dept_search`)}}</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{$t(`myItem.handle_reset`)}}</el-button>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
      <el-button
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="handleAdd"
        v-hasPermi="['setting:prepareId:add']"
      >{{$t(`menus.2314`)}}
      </el-button>
      <el-card class="gray-card">
        <el-table v-loading="loading" :data="prepareIdList" @selection-change="handleSelectionChange">
          <el-table-column :label="$t(`doc.this_dept_file_type`)" align="center" prop="docClass" :formatter="formatterDocClass"/>
<!--          <el-table-column :label="$t(`doc.this_dept_doc_code_type`)" align="center" prop="codeType">-->
<!--            <template slot-scope="scope">-->
<!--              <dict-tag :options="dict.type.doc_code_type" :value="scope.row.codeType"/>-->
<!--            </template>-->
<!--          </el-table-column>-->
          <el-table-column :label="$t(`dicts.watermark_type_file_number`)" align="center" prop="docId" v-if="true"/>
          <el-table-column :label="$t(`prepare.id_remarks`)" align="center" prop="remark"/>
          <el-table-column :label="$t(`doc.this_dept_claimant`)" align="center" prop="applyNickName"/>
          <el-table-column :label="$t(`file_set.version_create_time`)" align="center" prop="createTime" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t(`prepare.id_use_condition`)" align="center" prop="useStatus">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.doc_use_status" :value="scope.row.useStatus"/>
            </template>
          </el-table-column>
          <el-table-column :label="$t(`prepare.id_service_time`)" align="center" prop="useTime" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.useTime, '{y}-{m}-{d} {h}:{i}') }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!-- 添加或修改预制编号对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="90px">
        <el-form-item :label="$t(`doc.this_dept_claimant`)" prop="applyBy">
          <el-input v-model="form.applyNickName" readonly :placeholder="$t(`prepare.id_select_applicant`)">
            <el-button slot="append" icon="el-icon-more" @click="handleSelect('form')"></el-button>
          </el-input>
        </el-form-item>
<!--        <el-form-item label="分类" prop="dataType">-->
<!--          <el-radio-group v-model.trim="form.dataType">-->
<!--            <el-radio label="stdd">体系文件</el-radio>-->
<!--            <el-radio label="project">项目文件</el-radio>-->
<!--          </el-radio-group>-->
<!--        </el-form-item>-->
        <el-form-item :label="$t(`doc.this_dept_project`)" v-if="isProject" prop="projectId">
          <el-select value-key="id" clearable v-model="project" @change="onProjectChange" style="width:100%">
            <el-option
              v-for="item in projectList"
              :key="item.id"
              :label="item.name"
              :value="{id:item.id,name:item.name}"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t(`doc.this_dept_file_type`)" prop="docClass">
          <treeselect
            v-model="form.docClass"
            :options="classLevelOptions"
            :normalizer="normalizer"
            :show-count="true"
            :searchable="false"
            :placeholder="$t(`prepare.id_select_file_type`)"
          />
        </el-form-item>
<!--        <el-form-item :label="$t(`prepare.id_number_records`)" prop="recordCount">-->
<!--          <el-input-number v-model="form.recordCount" :min="0" :precision="0" :label="$t(`prepare.id_number_records`)"></el-input-number>-->
<!--        </el-form-item>-->
        <el-form-item :label="$t(`prepare.id_remarks`)" prop="remark">
          <el-input type="textarea" v-model="form.remark" :placeholder="$t(`prepare.id_please_remarks`)" maxlength="200"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">{{$t(`file_set.type_confim`)}}</el-button>
        <el-button @click="cancel">{{$t(`file_set.type_cancel`)}}</el-button>
      </div>
    </el-dialog>
    <user-list ref="userList" @selectHandle="handleSubmitUser"></user-list>
  </div>
</template>

<script>
import { listPrepareId, getPrepareId, delPrepareId, addPrepareId, updatePrepareId } from '@/api/setting/prepareId'
import UserList from '@views/workflowList/addWorkflow/add_import/userList.vue'
import { settingDocClassList } from '@/api/file_settings/type_settings'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { listInfo } from '@/api/system/project'

export default {
  name: 'PrepareId',
  dicts: ['doc_use_status', 'doc_code_type'],
  components: { UserList, Treeselect },
  data() {
    return {
      createTime: [],
      docClassList: [],
      docClassTree: [],
      project: {},
      projectList: [],
      classLevelOptions: [],
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 预制编号表格数据
      prepareIdList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        docId: undefined,
        codeType: undefined,
        docClass: undefined,
        applyBy: undefined,
        applyNickName: undefined,
        useStatus: undefined,
        useTime: undefined,
        createTime: undefined,
        orderByColumn: 'createTime',
        isAsc: 'desc'
      },
      isProject: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        docClass: [
          { required: true, message: this.$t(`prepare.id_type_cannot_empty`), trigger: 'blur' }
        ],
        dataType: [
          { required: true, message: this.$t(`prepare.id_type_cannot_empty`), trigger: 'blur' }
        ],
        projectId: [
          { required: true, message: this.$t(`prepare.id_item_cannot_empty`), trigger: 'blur' }
        ],
        applyBy: [
          { required: true, message: this.$t(`prepare.id_applicant_cannot_empty`), trigger: 'blur' }
        ],
        recordCount: [
          { required: true, message: this.$t(`prepare.id_records_cannot_empty`), trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    'form.dataType'(val) {
      let _this = this
      _this.isProject = val === 'project'
      _this.classLevelOptions = _this.classData.filter(item => item.dataType === val)
    }
  },
  created() {
    this.getProjectList()
    this.getSettingDocClassTreeseList()
    this.getList()
  },
  methods: {
    /** 查询预制编号列表 */
    getList() {
      this.loading = true
      if (this.createTime&&this.createTime.length>1){
        this.queryParams.startTime = this.createTime[0]
        this.queryParams.endTime =  this.createTime[1]
      }else {
        this.queryParams.startTime = undefined
        this.queryParams.endTime = undefined
      }
      listPrepareId(this.queryParams).then(response => {
        this.prepareIdList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        docId: undefined,
        codeType: undefined,
        docClass: undefined,
        recordCount: 0,
        dataType: 'stdd',
        projectId: undefined,
        projectName: undefined,
        remark: undefined,
        parentDocId: undefined,
        applyBy: undefined,
        applyNickName: undefined,
        useStatus: '0',
        useTime: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
      }
      this.project = {}
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.createTime = []
      this.nickNameClear()
      this.resetForm('queryForm')
      this.handleQuery()
    },
    nickNameClear(){
      this.queryParams.applyBy = undefined
      this.queryParams.applyNickName = undefined
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.docId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t(`prepare.id_add_prefabricated_number`)
      const userInfo = JSON.parse(sessionStorage.getItem("USER_INFO"))
      this.$set(this.form,'applyNickName',userInfo.nickName)
      this.$set(this.form,'applyBy',userInfo.userName)
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true
      this.reset()
      const docId = row.docId || this.ids
      getPrepareId(docId).then(response => {
        this.loading = false
        this.form = response.data
        this.open = true
        this.title = this.$t(`prepare.id_update_prefabricated_number`)
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.buttonLoading = true
          if (this.form.docId != null) {
            updatePrepareId(this.form).then(response => {
              this.$modal.msgSuccess(this.$t(`doc.this_dept_update_success`))
              this.open = false
              this.getList()
            }).finally(() => {
              this.buttonLoading = false
            })
          } else {
            addPrepareId(this.form).then(response => {
              this.$modal.msgSuccess(this.$t(`doc.this_dept_add_success`))
              this.open = false
              this.getList()
            }).finally(() => {
              this.buttonLoading = false
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const docIds = row.docId || this.ids
      this.$modal.confirm(this.$t(`prepare.id_delete_verify`) + docIds + this.$t(`file_set.signature_text1`)).then(() => {
        this.loading = true
        return delPrepareId(docIds)
      }).then(() => {
        this.loading = false
        this.getList()
        this.$modal.msgSuccess(this.$t(`doc.this_dept_remove_success`))
      }).finally(() => {
        this.loading = false
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('setting/prepareId/export', {
        ...this.queryParams
      }, `prepareId_${new Date().getTime()}.xlsx`)
    },
    handleSelect(source) {
      let _this = this
      _this.$nextTick(() => {
        _this.$refs.userList.init(source, null, null)
      })
    },
    handleSubmitUser(source, index, user) {
      this[source].applyBy = user.userName
      this[source].applyNickName = user.nickName
    },
    getSettingDocClassTreeseList() {
      settingDocClassList({ classStatus: '1' }).then(
        (response) => {
          this.docClassList = JSON.parse(JSON.stringify(response.rows))
          // 不展示分类：外来文件以及子类 记录文件
          let res = response.rows.filter(
            (item) => (item.id != 'DEO' && item.parentClassId != 'DEO')
          )
          this.docClassTree = this.handleTree(JSON.parse(JSON.stringify(res)), 'id', 'parentClassId')
          // 不展示分类： 记录文件
          res = res.filter(
            (item) => (item.id != 'STDD-R' && item.id != 'PROJECT-R')
          )
          this.classData = this.handleTree(res, 'id', 'parentClassId')
        }
      )
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children
      }
    },
    getProjectList() {
      let _this = this
      listInfo({ status: 'Y' }).then(res => {
        _this.projectList = res.rows
      })
    },
    onProjectChange(val) {
      let _this = this
      _this.form.projectId = val.id
      _this.form.projectName = val.name
    },
    formatterDocClass(row, column, cellValue, index) {
      let _this = this
      let item = _this.docClassList.find(item => item.id === cellValue)
      return item ? item.className : cellValue
    }
  }
}
</script>
