
	<template>
  <div id="app"  v-loading="loading">
    <el-card>
      <div class="search-box" style="margin-left: 25%">
        <el-input :placeholder="$t('home.search_input')" prefix-icon="el-icon-search" @keyup.enter.native="getSearchLinkLogList()" v-model="queryParams.keyword"> </el-input>
        <el-button type="primary" icon="el-icon-search" @click="getSearchLinkLogList()">{{ $t('home.search_btn') }}</el-button>
      </div>

      <div class="ssjg">
        <div class="fl"><i class="el-icon-info"></i><span class="tit">{{ $t('home.search_result_tip_1') }} {{total}} {{ $t('home.search_result_tip_2') }}</span></div>
        <div class="fr"><el-button type="primary" :disabled="this.checkedVersionIds.length == 0" icon="el-icon-download" @click="downloadHandle()" v-hasPermi="['system:search:download']">{{ $t('home.search_download_btn') }}</el-button></div>
      </div>

      <el-collapse v-model="activeNames" class="collapse-1" @change="changeHandle()">
        <template v-for="(searchResultItem, searchResultIndex) in searchResultList">
          <el-collapse-item :class="searchResultItem.hasChild == '0' ? '' : 'hide-arrow-right'" :name="searchResultItem.versionId" :key="searchResultIndex">
            <template slot="title">
              <div class="tit tit-1">
                 <el-checkbox v-if="searchResultItem.hasPerms || searchResultItem.isBorrow" v-model="checkedVersionIds" :label="searchResultItem.versionId" >
                  <span class="txt">{{searchResultItem.docName}}</span>
                 </el-checkbox>
                <span v-else class="txt">{{searchResultItem.docName}}</span>
                <el-link v-if="searchResultItem.hasPerms || searchResultItem.isBorrow" :underline="false" type="primary" @click.stop="handlePreview(searchResultItem, 'COMPANY')"><i class="el-icon-view el-icon--right"></i> {{ $t('home.search_view_btn') }}</el-link>
              </div>
              <div class="txt-1">
                <span><span>{{ $t('home.search_item_label_1') }}</span><span>{{searchResultItem.docId}}</span></span>
                <el-divider direction="vertical"></el-divider>
                <span><span>{{ $t('home.search_item_label_2') }}</span><span>{{searchResultItem.versionValue}}</span></span>
                <el-divider direction="vertical"></el-divider>
                <span><span>{{ $t('home.search_item_label_3') }}</span><span>{{searchResultItem.className}}</span></span>
                <el-divider direction="vertical"></el-divider>
                <span><span>{{ $t('home.search_item_label_4') }}</span><span>{{parseTime(searchResultItem.releaseTime)}}</span></span>
<!--                <el-divider direction="vertical"></el-divider>
                <span><span>{{ $t('home.search_item_label_5') }}</span><span>{{searchResultItem.deptName}}</span></span>
                <el-divider direction="vertical"></el-divider>
                <span><span>{{ $t('home.search_item_label_6') }}</span><span>{{searchResultItem.projectCode}}</span></span>
                <el-divider direction="vertical"></el-divider>
                <span><span>{{ $t('home.search_item_label_7') }}</span><span>{{searchResultItem.partNumber}}</span></span>
                <el-divider direction="vertical"></el-divider>
                <span><span>{{ $t('home.search_item_label_8') }}</span><span>{{searchResultItem.partRemark}}</span></span>
                <el-divider direction="vertical"></el-divider>
                <span><span>{{ $t('home.search_item_label_9') }}</span><span>{{searchResultItem.productVersion}}</span></span>-->
              </div>
            </template><!--title-->
            <div class="col-list">
              <template v-for="(searchDetailItem, searchDetailIndex) in searchDetailList">
                <div v-if="searchDetailItem.versionId == searchResultItem.versionId" :key="searchDetailIndex">
                  <div class="list" v-if="searchDetailItem.noteDocList.length > 0">
                    <div class="tit tit-2"> <!--<el-checkbox></el-checkbox>--> <span class="txt">{{ $t('doc.this_dept_master_file') }}</span></div>
                    <div class="txt-2">
                      <template v-for="(item, noteDocIndex) in searchDetailItem.noteDocList">
                        <p :key="noteDocIndex"> {{item.docName}}
                          <el-link v-if="searchResultItem.hasPerms" :underline="false" type="primary" @click.stop="handlePreview(item, 'COMPANY')"><i class="el-icon-view el-icon--right"></i> {{ $t('home.search_view_btn') }}</el-link>
                        </p>
                      </template>
                    </div>
                  </div><!--list-->
                  <div class="list" v-if="searchDetailItem.recordList.length > 0">
                    <div class="tit tit-2"> <!--<el-checkbox></el-checkbox>--> <span class="txt">{{ $t('home.search_item_label_10') }}</span></div>
                    <div class="txt-2">
                      <template v-for="(item, recordIndex) in searchDetailItem.recordList">
                        <p :key="recordIndex"> {{item.docName}}
                          <el-link v-if="searchResultItem.hasPerms" :underline="false" type="primary" @click.stop="handlePreview(item, 'COMPANY')"><i class="el-icon-view el-icon--right"></i> {{ $t('home.search_view_btn') }}</el-link>
                        </p>
                      </template>
                    </div>
                  </div><!--list-->
                  <div class="list" v-if="searchDetailItem.refDocList.length > 0">
                    <div class="tit tit-2"> <!--<el-checkbox></el-checkbox>--> <span class="txt">{{ $t('home.search_item_label_11') }}</span></div>
                    <div class="txt-2">
                      <template v-for="(item, refDocIndex) in searchDetailItem.refDocList">
                        <p :key="refDocIndex">
                          {{item.docName}}
                          <el-link v-if="searchResultItem.hasPerms" :underline="false" type="primary" @click.stop="handlePreview(item, 'COMPANY')"><i class="el-icon-view el-icon--right"></i> {{ $t('home.search_view_btn') }}</el-link>
                        </p>
                      </template>
                    </div>
                  </div><!--list-->
                </div>
              </template>
            </div><!--col-list-->
          </el-collapse-item><!--el-collapse-item-->
        </template>
      </el-collapse><!--el-collapse-->

    </el-card><!--------el-card--------->

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getSearchLinkLogList"
    />

    <as-pre-view
      :visible="viewShow"
      :id="viewId"
      ref="viewRef"
      @close="close"
    >
    </as-pre-view>
  </div>
  <!--app-->
</template>

<script>
import mixin from "@/layout/mixin/Commmon.js";
import { saveAs } from 'file-saver'
import {
  searchLinkLogList,
  searchLinkLogDetail, searchLinkLogDownload
} from "@/api/system/standard";
import { workflowToDoList } from "@/api/my_business/workflow";
import DealDrawer from "@/components/DealDrawer";
export default {
  name: "Index",
  components: {
    DealDrawer
  },
  mixins: [mixin],
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: ""
      },
      total: 0,
      searchResultList: [],
      searchDetailList: [],
      viewShow: false,
      showSearchDetail: true,
      dealDrawerShow: false,
      loading: false,
      checkedVersionIds: [],
      viewId: "",
      activeNames: ""
    };
  },
  watch: {

  },
  created() {
    this.getSearchLinkLogList()
  },
  methods: {
    changeHandle(){
      this.getSearchLinkLogDetail()
    },
    downloadHandle(){
      let loading = this.$loading({
        lock: true,
        text: "下载中",
        background: "rgba(0, 0, 0, 0.7)",
      });
      searchLinkLogDownload(this.checkedVersionIds).then((res) => {
        const blob = new Blob([res], { type: 'application/zip' })
        saveAs(blob, this.parseTime(Date.now(), "{y}{m}{d}{h}{i}{s}") + ".zip")
        // try {
        //   const blobUrl = window.URL.createObjectURL(res);
        //   const a = document.createElement("a");
        //   a.style.display = "none";
        //   a.download = this.parseTime(Date.now(), "{y}{m}{d}{h}{i}{s}") + ".zip";
        //   a.href = blobUrl;
        //   a.click();
        // } catch (e) {
        // }
      }).finally(()=>{
        loading.close();
      });
    },
    getSearchLinkLogList() {
      this.loading = true;
      this.checkedVersionIds = [];
      this.searchResultList = [];
      searchLinkLogList(this.queryParams).then((response) => {
        this.searchResultList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getSearchLinkLogDetail(){
      let versionIds = [];
      this.searchResultList.forEach((item) => {
        this.activeNames.forEach(str => {
          if(str !== "" && str == item["versionId"] && item["hasChild"] == "0"){
            versionIds.push(str);
          }
        })
      })
      if(versionIds.length > 0){
        searchLinkLogDetail(versionIds).then((res) => {
          this.searchDetailList = res;
          this.relead();
        });
      }
    },
    relead(){
      this.showSearchDetail = false;
      this.$nextTick(() => {
        this.showSearchDetail = true;
      })
    },
    handlePreview(row, source) {
      if (row.encryptFileId != null) {
        this.viewId = row.encryptFileId;
      } else if (row.mergeFileId != null) {
        this.viewId = row.mergeFileId;
      } else {
        this.viewId = row.fileId;
      }
      this.$refs.viewRef.handleOpenView(this.viewId, source);
      this.viewShow = true;
    }
  },
};
</script>
<style lang="scss">
.hide-arrow-right>div>.el-collapse-item__header>.el-collapse-item__arrow.el-icon-arrow-right{
  display: none !important;
}
.vue-treeselect__control {
  height: 32px;
}
.text-name {
  width: 200px; /* 设置元素宽度 */
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 文本溢出显示省略号 */
  cursor: pointer; /* 设置鼠标样式 */
}
.text-name:hover {
  white-space: normal; /* 鼠标悬浮时换行显示全部内容 */
  overflow: visible; /* 不再隐藏文本 */
  text-overflow: clip; /* 移除省略号 */
}
@import "../../public/css/poctstyle.css";
</style>
