<template>
  <el-dialog
    :title="settingValue.title"
    :visible.sync="dialogVisible"
    :close-on-click-modal = "false"
    width="60%"
    append-to-body
  >
  <div class="maincss">
    <div class="title-box">
      <el-form
        ref="formSubmit"
        class="mt20"
        label-width="130px"
      >
        <el-row :gutter="20" style="display: flex;">
          <el-col :span="12">
            <div style="height: 20px;line-height: 20px">{{settingValue.leftTitle}}</div>
            <div>
              <el-input
                v-model="queryValue"
                :placeholder="$t('doc.this_dept_insert')"
                clearable
                size="small"
                prefix-icon="el-icon-search"
              />
            </div>
            <el-tree
              ref="dataTree"
              :expand-on-click-node = "false"
              :data="dataList"
              :filter-node-method="filterNode"
              :props="defaultProps"
              @node-click="nodeClick"
              style="border: 1px solid rgb(230, 235, 245); min-height: 282px; max-height: 468px; overflow: auto"
            >
            </el-tree>
          </el-col>
          <el-col :span="12">
            <div style="height: 20px;line-height: 20px">{{settingValue.rightTitle}}</div>
            <el-tree
              :expand-on-click-node = "false"
              :data="selectedData"
              :props="defaultProps"
              default-expand-all
              style="border: 1px solid rgb(230, 235, 245); height: calc(100% - 20px); max-height: 500px;overflow: auto"
            >
               <span class="custom-tree-node" slot-scope="{ node, data }">
                <span>{{ node.label }}</span>
                <span>
                  <el-button
                    v-if="includesValueList(data[settingValue.id])"
                    v-show="!settingValue.notDel.includes(data[settingValue.id])"
                    style="padding: 10px"
                    type="text"
                    size="mini"
                    @click="() => remove(node, data)">
                    X
                  </el-button>
                </span>
              </span>
            </el-tree>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
  <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">{{ $t('doc.cancel') }}</el-button>
      <el-button type="primary" v-dbClick @click="submit">{{ $t('doc.submit') }}</el-button>
    </span>
    <el-dialog
      :title="title"
      :visible.sync="otherDialogVisible"
      :close-on-click-modal = "false"
      width="30%"
      append-to-body
    >
      <div class="maincss">
        <div class="title-box">
          <el-form
            ref="otherSubmit"
            class="mt20"
            label-width="100px"
          >
            <el-form-item :label="$t('doc.content')" required>
              <el-input  v-model="otherValue"></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
      <el-button @click="otherDialogVisible = false">{{ $t('doc.cancel') }}</el-button>
      <el-button type="primary" v-dbClick @click="otherSubmit">{{ $t('doc.submit') }}</el-button>
      </span>
    </el-dialog>
  </el-dialog>
</template>

<script>
export default {
  components: {},
  name: "SelectionBox",
  data() {
    return {
      queryValue: "",
      source:undefined,
      index:undefined,
      dataList:[],
      treeList: [],
      valueList: [],
      settingDefaultValue: {
        id: 'id',
        parentId: 'parentId',
        children: 'children',
        ancestors: 'ancestors',
        label: "label",
        other: undefined,
        title: this.$t('doc.select'),
        leftTitle: this.$t('doc.pending_selection'),
        rightTitle: this.$t('doc.selected'),
        valueId:'id',
        valueLabel:'label',
        valueModel: {},
        notDel: [],
        single: false, //是否单选
        anySelect: false, //是否可以选择任意节点，不局限于叶子节点
        FSLink: false, //是否父子联动
      },
      title: undefined,
      selectNode: undefined,
      nodeData:undefined,
      otherValue: undefined,
      otherDialogVisible: false,
      settingValue: {},
      dialogVisible: false,
      defaultProps: {
        children: "children",
        label: "label",
      },
      selectedList: [],
      selectedData: [],
    };
  },
  mounted() {},
  watch:{
    // 根据名称筛选树
    queryValue(val) {
      this.$refs.dataTree.filter(val);
    }
  },
  methods: {
    includesValueList(id){
      return this.valueList.findIndex(value=>value[this.settingValue.valueId]===id)>-1
    },
    filterNode(value, data) {
      if (!value) return true;
      return data[this.settingValue.label].toLowerCase().indexOf(value.toLowerCase()) !== -1
    },
    handlerSettings(val){
      let _this = this
      if (val) {
        for (let key in _this.settingDefaultValue){
          _this.settingValue[key] = !val[key]?_this.settingDefaultValue[key]:val[key]
        }
      }else {
        _this.settingValue = _this.settingDefaultValue
      }
    },
    //标识,标识,已选项，总数据，配置项，树形结构数据
    init(source,index,selectedList,dataList,settings,treeList){
      let _this = this
      _this.queryValue = ""
      _this.treeList = treeList
      _this.dataList = dataList
      _this.handlerSettings(settings)
      _this.source = source
      _this.index = index
      _this.defaultProps.children = this.settingValue.children
      _this.defaultProps.label = this.settingValue.label
      if (selectedList&&selectedList.length>0) {
        //是否树形结构
        if (_this.treeList) {
          _this.valueList = JSON.parse(JSON.stringify(selectedList))
          let valueList = []
          _this.valueList.forEach(item=>{
            let data = _this.treeList.find(node=>node[_this.settingValue.id]===item[_this.settingValue.valueId])
            if (data) {
              _this.queryList(valueList,data)
            }else {
              let value = JSON.parse(JSON.stringify(_this.settingValue.valueModel))
              value[_this.settingValue.id] = item[_this.settingValue.valueId]
              value[_this.settingValue.label] = item[_this.settingValue.valueLabel]
              valueList.push(value)
            }
          })
          _this.selectedList = JSON.parse(JSON.stringify(valueList))
        }else {
          let valueList = []
          _this.valueList = JSON.parse(JSON.stringify(selectedList))
          _this.valueList.forEach(item=>{
              let value = JSON.parse(JSON.stringify(_this.settingValue.valueModel))
              value[_this.settingValue.id] = item[_this.settingValue.valueId]
              value[_this.settingValue.label] = item[_this.settingValue.valueLabel]
              valueList.push(value)
          })
          _this.selectedList = JSON.parse(JSON.stringify(valueList))
        }
        _this.selectedData = _this.listToTree(_this.selectedList,_this.settingValue.id,_this.settingValue.parentId,_this.settingValue.children)
      }else {
        _this.valueList = []
        _this.selectedList = []
        _this.selectedData = []
      }
      _this.dialogVisible = true
    },
    queryList(valueList,item){
      let _this = this
      let node = valueList.find(node=>node[_this.settingValue.id]===item[_this.settingValue.id])
      if (!node) {
        valueList.push(item)
      }
      let data = _this.treeList.find(tree=>tree[_this.settingValue.id]===item[_this.settingValue.parentId])
      if (data) {
        _this.queryList(valueList,data);
      }
    },
    otherSubmit(){
      let _this = this
      if (!!_this.otherValue) {
        let label = _this.title + '（' + _this.otherValue + '）'
        let newNode= _this.selectedList.find(item=>item[_this.settingValue.label]===label&&item[_this.settingValue.parentId]===_this.nodeData[_this.settingValue.parentId])
        if (!!newNode) {
          _this.$modal.msgWarning("{{ $t('doc.content_exists') }}")
          return
        }
        _this.nodeData[_this.settingValue.label] = label
        _this.nodeData[_this.settingValue.id] = _this.$uuid.v4().split('-').join('')
        _this.nodeClick(_this.nodeData,_this.selectNode)
        _this.selectedData = _this.listToTree(_this.selectedList, _this.settingValue.id, _this.settingValue.parentId, _this.settingValue.children)
        _this.otherDialogVisible = false
      } else {
        _this.$modal.msgWarning("{{ $t('doc.content_cannot_empty') }}")
      }
    },
    submit(){
      let _this = this
      _this.$emit("selectHandle",_this.source,_this.index,JSON.parse(JSON.stringify(_this.selectedList)),JSON.parse(JSON.stringify(_this.valueList)))
      _this.dialogVisible = false
    },
    nodeClick(val,node) {
      let _this = this
      if (node.isLeaf||_this.settingValue.anySelect){
        if (_this.settingValue.other&&_this.settingValue.other.includes(val[_this.settingValue.label])) {
          _this.title = val[_this.settingValue.label]
          _this.otherDialogVisible = this
          _this.nodeData = JSON.parse(JSON.stringify(val))
          _this.otherValue = undefined
          _this.selectNode = node
          return
        }
        let data = _this.selectedList.find(item=>item[_this.settingValue.id]===val[_this.settingValue.id])
        let value = JSON.parse(JSON.stringify(_this.settingValue.valueModel))
        value[_this.settingValue.valueId] = val[_this.settingValue.id]
        value[_this.settingValue.valueLabel] = val[_this.settingValue.label]
        if (!data) {
          val.isLeaf = true
          if (_this.settingValue.single) {
            _this.valueList=[value]
            _this.selectedList = [val]
          }else {
            _this.valueList.push(value)
            _this.selectedList.push(val)
          }
          let parentNodeList = []
          _this.getParentNodeList(parentNodeList,node)
          if (parentNodeList.length>0) {
            _this.selectedList.push(...parentNodeList)
          }
          _this.selectedData = _this.listToTree(_this.selectedList,_this.settingValue.id,_this.settingValue.parentId,_this.settingValue.children)
          if (_this.settingValue.FSLink) {
            let parentList = []
            _this.getParentList(parentList,val)
            _this.valueList= _this.valueList.filter(item=>parentList.findIndex(delItem=>delItem[_this.settingValue.id]===item[_this.settingValue.valueId])<0)
          }
        }else{
          if (!_this.settingValue.single&&!_this.includesValueList(value[_this.settingValue.valueId])) {
            _this.valueList.push(value)
            if (_this.settingValue.FSLink) {
              let childrenList = []
              _this.getChildrenList(childrenList,val)
              _this.selectedList = _this.selectedList.filter(item=>childrenList.findIndex(delItem=>delItem[_this.settingValue.id]===item[_this.settingValue.id])<0)
              _this.selectedData = _this.listToTree(_this.selectedList,_this.settingValue.id,_this.settingValue.parentId,_this.settingValue.children)
              _this.valueList= _this.valueList.filter(item=>childrenList.findIndex(delItem=>delItem[_this.settingValue.id]===item[_this.settingValue.valueId])<0)
            }
          }
        }
      }
    },
    getParentNodeList(parentList,node){
      let _this = this
      if (node.level>1) {
        let parent = JSON.parse(JSON.stringify(node.parent.data))
        let data = _this.selectedList.find(item=>item[_this.settingValue.id]===parent[_this.settingValue.id])
        if (!data) {
          delete parent[_this.settingValue.children]
          parentList.push(parent)
          _this.getParentNodeList(parentList,node.parent);
        }
      }
    },
    getParentList(parentList,val){
      let _this = this
      let parent = _this.selectedList.find(item=>item[_this.settingValue.id]===val[_this.settingValue.parentId])
      if (parent) {
        parentList.push(parent)
        _this.getParentList(parentList,parent)
      }
    },
    getChildrenList(childrenList,val){
      let _this = this
      let children = _this.selectedList.filter(item=>item[_this.settingValue.parentId]===val[_this.settingValue.id])
      children.forEach(item=>{
        childrenList.push(item)
        _this.getChildrenList(childrenList,item)
      })
    },
    remove(node, data) {
      let _this = this
      let deleteList = []
      if (_this.selectedList.findIndex(d => d[_this.settingValue.parentId] === data[_this.settingValue.id])===-1) {
        _this.getDeleteList(deleteList,node)
        deleteList.push(data)
      }
      deleteList.forEach(d=>{
        const index = _this.selectedList.findIndex(s => s[_this.settingValue.id] === d[_this.settingValue.id]);
        _this.selectedList.splice(index, 1);
      })
      const index = _this.valueList.findIndex(s => s[_this.settingValue.valueId] === data[_this.settingValue.id]);
      _this.valueList.splice(index, 1);
    },
    getDeleteList(deleteList,node){
      let _this = this
      let parent = node.parent;
      if (parent.parent&&parent.data.children.length===1&&!_this.includesValueList(parent.data[_this.settingValue.id])) {
         deleteList.push(parent.data)
        _this.getDeleteList(deleteList, parent)
      }else {
        const children = parent.data.children || parent.data;
        const index = children.findIndex(d => d[_this.settingValue.id] === node.data[_this.settingValue.id]);
        children.splice(index, 1);
      }
    },

    /**
     * 构造树型结构数据
     * @param {*} list 数据源
     * @param {*} id id字段 默认 'id'
     * @param {*} parentId 父节点字段 默认 'parentId'
     * @param {*} children 孩子节点字段 默认 'children'
     */
    listToTree(list, id, parentId, children) {
      let data = JSON.parse(JSON.stringify(list))
      let config = {
        id: id || 'id',
        parentId: parentId || 'parentId',
        childrenList: children || 'children'
      };

      var childrenListMap = {};
      var nodeIds = {};
      var tree = [];

      for (let d of data) {
        let parentId = d[config.parentId];
        if (childrenListMap[parentId] == null) {
          childrenListMap[parentId] = [];
        }
        nodeIds[d[config.id]] = d;
        childrenListMap[parentId].push(d);
      }

      for (let d of data) {
        let parentId = d[config.parentId];
        if (nodeIds[parentId] == null) {
          tree.push(d);
        }
      }

      for (let t of tree) {
        adaptToChildrenList(t);
      }

      function adaptToChildrenList(o) {
        if (childrenListMap[o[config.id]] !== null) {
          o[config.childrenList] = childrenListMap[o[config.id]];
        }
        if (o[config.childrenList]) {
          for (let c of o[config.childrenList]) {
            adaptToChildrenList(c);
          }
        }
      }
      return tree;
    },
  },
};
</script>
<style scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
</style>
