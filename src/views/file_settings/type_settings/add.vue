<template>
  <div>
    <el-form
      ref="elForm"
      :model="formData"
      :rules="rules"
      size="medium"
      label-width="100px"
    >
      <el-form-item label="上级分类:">
        <treeselect
          v-model.trim="formData.parentClassId"
          :options="classLevelOptions"
          :normalizer="normalizer"
          :show-count="true"
          :searchable="false"
          @select="handleSelectNode"
          placeholder="选择上级分类"
        />
      </el-form-item>
      <el-form-item label="分类名称:" prop="className">
        <el-input
          v-model="formData.className"
          placeholder="请输入分类名称:"
          clearable
          :style="{ width: '100%' }"
          @blur="handeNameBlur"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="分类编码:" prop="classCode">
        <el-input
          v-model.trim="formData.classCode"
          placeholder="请输入分类编码:"
          clearable
          :style="{ width: '100%' }"
          @blur="handeBlur"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="序号:" prop="sort">
        <el-input
          v-model.trim="formData.sort"
          placeholder="请输入序号:"
          clearable
          type="number"
          :style="{ width: '100%' }"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="文件模板:">
        <el-upload
          ref="field1041"
          :file-list="fileIdfileList"
          :http-request="appendixesUpload"
          :on-remove="handleRemoveAttachment"
        >
          <el-button size="small" type="primary" icon="el-icon-upload"
            >点击上传
          </el-button>
        </el-upload>
      </el-form-item>
      <el-form-item label="分类所属类型:" prop="classType">
        <el-select
          v-model.trim="formData.classType"
        >
          <el-option
            v-for="dict in dict.type.class_type"
            :key="dict.value"
            :label="dictLanguage(dict)"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="分类状态:" prop="classStatus">
        <el-radio-group v-model.trim="formData.classStatus" size="medium">
          <el-radio
            v-for="(item, index) in dict.type.class_status"
            :key="index"
            :label="item.value"
            :disabled="item.disabled"
            >{{ item.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="文件类型:" prop="isFileType">
        <el-radio-group v-model.trim="formData.isFileType" size="medium">
          <el-radio
            v-for="(item, index) in dict.type.file_type"
            :key="index"
            :label="item.value"
          >{{ item.label }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <!-- <el-form-item label="编号规则:" prop="codeId">
        <el-select
          v-model.trim="formData.codeId"
          placeholder="请选择编号规则："
          clearable
          :style="{ width: '100%' }"
        >
          <el-option
            v-for="(item, index) in codeIdptions"
            :key="index"
            :label="item.ruleName"
            :value="item.id"
            :disabled="item.disabled"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item size="large">
        <el-button type="primary" @click="submitForm" v-dbClick>提交</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import {
  settingDocClass,
  settingDocClassIsExistByCode,
  settingDocClassIsExistByName,
  settingDocClassId,
  settingDocClassList,
  settingDocClassUpdata,
} from "@/api/file_settings/type_settings";
import { listCodeRule } from "@/api/setting/codeRule";
import { processFileLocalUpload } from "@/api/commmon/file";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { parseTime } from "../../../utils/ruoyi";

export default {
  dicts: ["class_status","class_type", "file_type"],
  components: {
    Treeselect,
  },
  props: {
    id: {
      type: String,
      default: "",
    },
    dataType: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      classLevelOptions: [],
      formData: {
        classCode: undefined,
        menuId: undefined,
        classLevel: 1,
        classType: 'DOC',
        className: undefined,
        id: undefined,
        parentClassId: 0,
        classStatus: "1",
        fileId: undefined,
        codeId: undefined,
        dataType:  undefined,
        isFileType: "N"
      },
      classLevelHandle: [],
      rules: {
        // className: [
        //   {
        //     required: true,
        //     message: "请输入分类名称",
        //     trigger: "blur",
        //   },
        //   {
        //     min: 1,
        //     max: 20,
        //     message: "长度在 1 到 20 个字符",
        //     trigger: "blur",
        //   },
        // ],
        id: [
          {
            required: true,
            message: "请输入分类代码",
            trigger: "blur",
          },
          {
            min: 1,
            max: 20,
            message: "长度在 1 到 20 个字符",
            trigger: "blur",
          },
        ],
        classStatus: [
          {
            required: true,
            message: "分类状态不能为空",
            trigger: "change",
          },
        ],
        codeId: [
          {
            required: true,
            message: "请选择编号规则",
            trigger: "change",
          },
        ],
        isFileType:  [
          {
            required: true,
            message: "是否为临时文件不能为空",
            trigger: "change",
          },
        ],
      },

      fileIdfileList: [],
      field101Options: [],
      codeIdptions: [],

      field101Props: {
        multiple: false,
      },
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getSettingDocClassTreeseList();
  },
  mounted() {
    //console.log(this.id);
    if (this.id != "") {
      settingDocClassId(this.id).then((response) => {
        // console.log(response.data);
        if (response.data.fileList != null) {
          response.data.fileList.forEach((element) => {
            this.fileIdfileList.push({
              name: element.fileName,
              url: element.id,
            });
          });
        }
        this.formData = response.data;
      });
    }
    listCodeRule({ pageNum: 1, pageSize: 100 }).then((response) => {
      this.codeIdptions = response.rows;
    });
  },
  methods: {
    handleSelectNode(node, instanceId) {
      if (node.id === 0) {
        this.formData.classLevel = 1;
      } else {
        this.formData.classLevel = parseInt(node.classLevel) + 1;
      }
    },
    getSettingDocClassTreeseList() {
      settingDocClassList({
        classStatus: "1", dataType:this.dataType
      }).then((response) => {
        this.classLevelOptions = [];
        const classLevel = { id: 0, className: "主类目", children: [] };
        //console.log(response.rows);
        response.rows.forEach((element) => {
          element["children"] = [];
        });
        //console.log(response.rows);
        classLevel.children = this.handleTree(
          response.rows,
          "id",
          "parentClassId"
        );

        this.classLevelOptions.push(classLevel);
      });
    },
    submitForm() {
      // 设置数据类型
      this.formData.dataType = this.dataType;
      this.$refs["elForm"].validate((valid) => {
        if (valid) {
          if (this.fileIdfileList != "") {
            this.formData.fileId = this.fileIdfileList[0].url;
          }else{
            this.formData.fileList = []
            this.formData.fileId = ""
          }
          if (this.id != "") {
            this.formData.createTime = parseTime(this.formData.createTime);
            this.formData.updateTime = parseTime(this.formData.updateTime);
            settingDocClassUpdata(this.formData).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.$emit("chinldClose");
            });
          } else {
            settingDocClass(this.formData).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.$emit("chinldClose");
            });
          }
        }
      });
    },
    resetForm() {
      this.$refs["elForm"].resetFields();
    },
    getField101Options() {
      // TODO 发起请求获取数据
      this.field101Options;
    },
    appendixesUpload(params) {
      this.fileIdfileList = [];
      let fd = new FormData();
      fd.append("file", params.file); //传文件
      // fd.append('srid',this.aqForm.srid);//传其他参数
      processFileLocalUpload(fd).then((res) => {
        this.fileIdfileList.push({
          name: res.data.fileName,
          url: res.data.fileId,
        });
      });
    },
    // 删除附件
    handleRemoveAttachment(file, fileList) {
      this.fileIdfileList = this.fileIdfileList.filter(
        (item) => item.url !== file.url
      );
    },
    handleChange(value) {
      console.log(value);

      this.formData.classLevel = value.slice(-1).toString();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    handeNameBlur() {
      // settingDocClassIsExistByName(this.formData.className).then((res) => {
      //   if (res.data >= 1) {
      //     this.$modal.msgWarning("当前输入的分类名称已经存在");
      //     this.formData.className = "";
      //   }
      // });
    },
    handeBlur() {
      // settingDocClassIsExistByCode(this.formData.id).then((response) => {
      //   console.log(response);
      //   if (response.data == 1) {
      //     this.$modal.msgWarning("当前输入的分类代码已经存在");
      //     this.formData.id = "";
      //   }
      // });
    },
  },
};
</script>
<style>
.el-upload__tip {
  line-height: 1.2;
}
</style>
