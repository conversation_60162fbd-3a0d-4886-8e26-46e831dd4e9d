<template>
    <div class="news-card" v-loading="loading">
      <el-card class="gray-card table-card no-padding">
        <el-form
          ref="elForm"
          :model="formData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="是否启用:" prop="openFlag">
                <el-switch
                  v-model.trim="formData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="文件有效期（月）:" prop="ruleId">
                <el-input v-model.number.trim="formData.ruleId" size="medium" style="width: 20%" placeholder="请输入文件有效期"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item
                label="是否应用到子文件类型:"
                prop="applyFlag"
              >
                <el-radio-group
                  v-model.trim="formData.applyFlag"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in dict.type.sys_yes_no"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-form
          ref="elForm1"
          :model="formData1"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="是否启用:" prop="openFlag">
                <el-switch
                  v-model.trim="formData1.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="文件复审周期（月）:" prop="ruleId">
                <el-input v-model.number.trim="formData1.ruleId" size="medium" style="width: 20%" placeholder="请输入文件复审周期"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item
                label="是否应用到子文件类型:"
                prop="applyFlag"
              >
                <el-radio-group
                  v-model.trim="formData1.applyFlag"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in dict.type.sys_yes_no"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>
</template>

<script>
import { getInfoBy, updateDocClassSetting } from '@/api/setting/docClassSetting'
import { listDistributeGroup } from '../../../../api/setting/distributeGroup'

export default {
  name: 'ExpirationSetting',
  components: {},
  props: ['id'],
  dicts: ['sys_yes_no'],
  data() {
    return {
      loading: false,
      printGroupOptions: [],
      purviewGroupOptions: [],
      formData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        settingId: undefined,
        openFlag: 'N',
        applyFlag: 'Y'
      },
      formData1:{
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        settingId: undefined,
        openFlag: 'N',
        applyFlag: 'Y'
      },
      rules: {
        ruleId: [
          {
            required: true,
            message: this.$t(`doc.this_dept_insert`),
            trigger: "blur",
          },
        ],
      },
    }
  },

  mounted() {
    this.getByDocClass()
  },
  methods: {
    reset() {
      this.formData = {
        id: undefined,
        type: 'expiration',
        docClass: this.id,
        ruleId: undefined,
        settingId: undefined,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.formData1 = {
        id: undefined,
        type: 'reviewCycle',
        docClass: this.id,
        ruleId: undefined,
        settingId: undefined,
        openFlag: 'N',
        applyFlag: 'Y',
      }
    },
    getByDocClass() {
      let _this = this
      _this.reset()
      getInfoBy({type:'expiration',docClass:this.id}).then( res => {
        if (res.data) {
          _this.formData = res.data
        }
      })
      getInfoBy({type:'reviewCycle',docClass:this.id}).then( res => {
        if (res.data) {
          _this.formData1 = res.data
        }
      })
    },
    savaDocClassSetting(){
       let _this  = this
      _this.$refs["elForm"].validate((valid) => {
        if (valid) {
          let formData = JSON.parse(JSON.stringify(_this.formData))
          updateDocClassSetting(formData).then(res => {
            _this.formData.id = res.data
            _this.$modal.msgSuccess("设置成功");
          })
        }
      })
      _this.$refs["elForm1"].validate((valid) => {
        if (valid) {
          let formData = JSON.parse(JSON.stringify(_this.formData1))
          updateDocClassSetting(formData).then(res => {
            _this.formData1.id = res.data
            _this.$modal.msgSuccess("设置成功");
          })
        }
      })
    },
  }
}
</script>
