<template>
    <div class="news-card" v-loading="loading">
      <el-card class="gray-card table-card no-padding">
        <el-form
          ref="elForm"
          :model="formData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="是否启用:" prop="openFlag">
                <el-switch
                  v-model.trim="formData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item
                label="是否应用到子文件类型:"
                prop="applyFlag"
              >
                <el-radio-group
                  v-model.trim="formData.applyFlag"
                  size="medium"
                >
                  <el-radio
                    v-for="(item, index) in dict.type.sys_yes_no"
                    :key="index"
                    :label="item.value"
                  >{{ item.label }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="分发人员:" prop="ruleId">
                <el-select
                  v-model.trim="formData.ruleId"
                  placeholder="请选择分组设置"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <el-option
                    v-for="(item, index) in printGroupOptions"
                    :key="index"
                    :label="item.groupName"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="预览人员:" prop="settingId">
                <el-select
                  v-model.trim="formData.settingId"
                  placeholder="请选择分组设置"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <el-option
                    v-for="(item, index) in purviewGroupOptions"
                    :key="index"
                    :label="item.groupName"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>
</template>

<script>
import { getInfoBy, updateDocClassSetting } from '@/api/setting/docClassSetting'
import { listDistributeGroup } from '../../../../api/setting/distributeGroup'

export default {
  name: 'GroupSetting',
  components: {},
  props: ['id'],
  dicts: ['sys_yes_no'],
  data() {
    return {
      loading: false,
      printGroupOptions: [],
      purviewGroupOptions: [],
      formData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        settingId: undefined,
        openFlag: 'N',
        applyFlag: 'Y'
      },
      rules: {
        ruleId: [
          {
            required: true,
            message: this.$t(`doc.this_dept_pls_select`)+this.$t(`menus.2423`),
            trigger: "blur",
          },
        ],
        settingId: [
          {
            required: true,
            message: this.$t(`doc.this_dept_pls_select`)+this.$t(`menus.2423`),
            trigger: "blur",
          },
        ],
      },
    }
  },
  created() {
    this.getRuleList()
  },
  mounted() {
    this.getByDocClass()
  },
  methods: {
    getRuleList(){
      listDistributeGroup({type:'print'}).then(res=>{
        this.printGroupOptions=res.rows
      })
      listDistributeGroup({type:'purview'}).then(res=>{
        this.purviewGroupOptions=res.rows
      })
    },
    reset() {
      this.formData = {
        id: undefined,
        type: 'distribute',
        docClass: this.id,
        ruleId: undefined,
        settingId: undefined,
        openFlag: 'N',
        applyFlag: 'Y',
      }
    },
    getByDocClass() {
      let _this = this
      _this.loading = true
      _this.reset()
      getInfoBy({type:'distribute',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.formData = res.data
        }
        _this.loading = false
      })
    },
    savaDocClassSetting(){
       let _this  = this
      _this.$refs["elForm"].validate((valid) => {
        if (valid) {
          let formData = JSON.parse(JSON.stringify(_this.formData))
          updateDocClassSetting(formData).then(res => {
            _this.formData.id = res.data
            _this.$modal.msgSuccess("设置成功");
          })
        }
      })
    },
  }
}
</script>
