<template>
    <div class="news-card" v-loading="loading">
      <el-card class="gray-card table-card no-padding">
        <el-form
          ref="elForm"
          :model="formData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="工厂、物料编码、物料描述展示:" prop="openFlag">
                <el-switch
                  v-model.trim="formData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>


        <el-form
          ref="elForm"
          :model="formCustomerData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="客户编码展示:" prop="openFlag">
                <el-switch
                  v-model.trim="formCustomerData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>


        <el-form
          ref="elForm"
          :model="formDeviceData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="设备编码、设备名称展示:" prop="openFlag">
                <el-switch
                  v-model.trim="formDeviceData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form
          ref="elForm"
          :model="formProductVersionData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="产品版本展示:" prop="openFlag">
                <el-switch
                  v-model.trim="formProductVersionData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form
          ref="elForm"
          :model="shelfLifeData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="保存期限展示:" prop="openFlag">
                <el-switch
                  v-model.trim="shelfLifeData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form
          ref="elForm"
          :model="codeAndTypeData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="名称拼接（物料编码+文件类型）:" prop="openFlag">
                <el-switch
                  v-model.trim="codeAndTypeData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form
          ref="elForm"
          :model="projectCodeData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="项目编号展示:" prop="openFlag">
                <el-switch
                  v-model.trim="projectCodeData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form
          ref="elForm"
          :model="systemClauseData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="体系条款展示:" prop="openFlag">
                <el-switch
                  v-model.trim="systemClauseData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-form
          ref="elForm"
          :model="internalDocIdData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="内部文件编号展示:" prop="openFlag">
                <el-switch
                  v-model.trim="internalDocIdData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-form
          ref="elForm"
          :model="ecnCodeData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="ECN编号展示:" prop="openFlag">
                <el-switch
                  v-model.trim="ecnCodeData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-form
          ref="elForm"
          :model="versionIsUpdateData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="文件版本可修改:" prop="openFlag">
                <el-switch
                  v-model.trim="versionIsUpdateData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-form
          ref="elForm"
          :model="projectNameSecurityKeywordByteData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="项目名称、密级、关键词、字节数展示:" prop="openFlag">
                <el-switch
                  v-model.trim="projectNameSecurityKeywordByteData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-form
          ref="elForm"
          :model="complianceData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="合规性:" prop="openFlag">
                <el-switch
                  v-model.trim="complianceData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form
          ref="elForm"
          :model="filePurposeData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="文件用途:" prop="openFlag">
                <el-switch
                  v-model.trim="filePurposeData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form
          ref="elForm"
          :model="regulationStatusData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="法规标准状态:" prop="openFlag">
                <el-switch
                  v-model.trim="regulationStatusData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form
          ref="elForm"
          :model="regulationPublishDateData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="法规/标准发布日期:" prop="openFlag">
                <el-switch
                  v-model.trim="regulationPublishDateData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form
          ref="elForm"
          :model="regulationImplementDateData"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="法规/标准实施日期:" prop="openFlag">
                <el-switch
                  v-model.trim="regulationImplementDateData.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-form
          ref="elForm"
          :model="programVersionId"
          :rules="rules"
          size="medium"
          label-width="200px"
        >
          <el-row gutter="15">
            <el-col :span="24">
              <el-form-item label="归属上级文件:" prop="openFlag">
                <el-switch
                  v-model.trim="programVersionId.openFlag"
                  active-color="#13ce66"
                  active-value="Y"
                  inactive-value="N"
                  size="medium"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>
</template>

<script>
import { listVersionRule } from '@/api/setting/versionRule'
import { getInfoBy, updateDocClassSetting } from '@/api/setting/docClassSetting'

export default {
  name: 'DocFormSetting',
  components: {},
  props: ['id'],
  dicts: ['sys_yes_no'],
  data() {
    return {
      loading: false,
      listCodeRuleOptions: [],
      formData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      formCustomerData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      formDeviceData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      formProductVersionData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      shelfLifeData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      codeAndTypeData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      projectCodeData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      systemClauseData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      internalDocIdData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      ecnCodeData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      versionIsUpdateData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      projectNameSecurityKeywordByteData: {
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      complianceData:{
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      filePurposeData:{
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      regulationStatusData:{
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      regulationPublishDateData:{
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      regulationImplementDateData:{
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      programVersionId:{
        docClass: undefined,
        type: undefined,
        ruleId: undefined,
        openFlag: null,
        applyFlag: 'Y'
      },
      rules: {},
    }
  },
  created() {
  },
  mounted() {
    this.getByDocClass()
  },
  methods: {
    reset() {
      this.formData = {
        id: undefined,
        type: 'formShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.formCustomerData = {
        id: undefined,
        type: 'formCustomerShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.formDeviceData = {
        id: undefined,
        type: 'formDeviceShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.formProductVersionData = {
        id: undefined,
        type: 'formProductVersionShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.shelfLifeData = {
        id: undefined,
        type: 'shelfLifeShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.codeAndTypeData = {
        id: undefined,
        type: 'codeAndTypeShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.projectCodeData = {
        id: undefined,
        type: 'projectCodeShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.systemClauseData = {
        id: undefined,
        type: 'systemClauseShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.internalDocIdData = {
        id: undefined,
        type: 'internalDocIdShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.ecnCodeData = {
        id: undefined,
        type: 'ecnCodeShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.versionIsUpdateData = {
        id: undefined,
        type: 'versionIsUpdateShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.projectNameSecurityKeywordByteData = {
        id: undefined,
        type: 'projectNameSecurityKeywordByteShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.complianceData = {
        id: undefined,
        type: 'complianceShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.filePurposeData = {
        id: undefined,
        type: 'filePurposeShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.regulationStatusData = {
        id: undefined,
        type: 'regulationStatusShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.regulationPublishDateData = {
        id: undefined,
        type: 'regulationPublishDateShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.regulationImplementDateData = {
        id: undefined,
        type: 'regulationImplementDateShow',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
      this.programVersionId = {
        id: undefined,
        type: 'programVersionId',
        docClass: this.id,
        openFlag: 'N',
        applyFlag: 'Y',
      }
    },
    getByDocClass() {
      let _this = this
      _this.loading = true
      _this.reset()
      getInfoBy({type:'formShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.formData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'formCustomerShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.formCustomerData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'formDeviceShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.formDeviceData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'formProductVersionShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.formProductVersionData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'shelfLifeShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.shelfLifeData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'codeAndTypeShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.codeAndTypeData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'projectCodeShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.projectCodeData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'systemClauseShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.systemClauseData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'internalDocIdShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.internalDocIdData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'ecnCodeShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.ecnCodeData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'versionIsUpdateShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.versionIsUpdateData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'projectNameSecurityKeywordByteShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.projectNameSecurityKeywordByteData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'complianceShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.complianceData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'filePurposeShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.filePurposeData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'regulationStatusShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.regulationStatusData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'regulationPublishDateShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.regulationPublishDateData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'regulationImplementDateShow',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.regulationImplementDateData = res.data
        }
        _this.loading = false
      })
      getInfoBy({type:'programVersionId',docClass:this.id}).then(async res => {
        if (res.data) {
          _this.programVersionId = res.data
        }
        _this.loading = false
      })
    },
    savaDocFormSetting(){
       let _this  = this
      _this.loading = true
       let formData = JSON.parse(JSON.stringify(_this.formData))
        updateDocClassSetting(formData).then(res=>{
          _this.formData.id = res.data
          this.$modal.msgSuccess("设置成功");
          this.getByDocClass()
        })
        let formCustomerData = JSON.parse(JSON.stringify(_this.formCustomerData))
        updateDocClassSetting(formCustomerData).then(res=>{
          _this.formCustomerData.id = res.data
        })
        let formDeviceData = JSON.parse(JSON.stringify(_this.formDeviceData))
        updateDocClassSetting(formDeviceData).then(res=>{
          _this.formDeviceData.id = res.data
        })
        let formProductVersionData = JSON.parse(JSON.stringify(_this.formProductVersionData))
        updateDocClassSetting(formProductVersionData).then(res=>{
          _this.formProductVersionData.id = res.data
        })
        let shelfLifeData = JSON.parse(JSON.stringify(_this.shelfLifeData))
        updateDocClassSetting(shelfLifeData).then(res=>{
          _this.shelfLifeData.id = res.data
        })
        let codeAndTypeData = JSON.parse(JSON.stringify(_this.codeAndTypeData))
        updateDocClassSetting(codeAndTypeData).then(res=>{
          _this.codeAndTypeData.id = res.data
        })
        let projectCodeData = JSON.parse(JSON.stringify(_this.projectCodeData))
        updateDocClassSetting(projectCodeData).then(res=>{
          _this.projectCodeData.id = res.data
        })
        let systemClauseData = JSON.parse(JSON.stringify(_this.systemClauseData))
        updateDocClassSetting(systemClauseData).then(res=>{
        })
        let internalDocIdData = JSON.parse(JSON.stringify(_this.internalDocIdData))
        updateDocClassSetting(internalDocIdData).then(res=>{
        })
        let ecnCodeData = JSON.parse(JSON.stringify(_this.ecnCodeData))
        updateDocClassSetting(ecnCodeData).then(res=>{
        })
        let versionIsUpdateData = JSON.parse(JSON.stringify(_this.versionIsUpdateData))
        updateDocClassSetting(versionIsUpdateData).then(res=>{
        })
        let projectNameSecurityKeywordByteData = JSON.parse(JSON.stringify(_this.projectNameSecurityKeywordByteData))
        updateDocClassSetting(projectNameSecurityKeywordByteData).then(res=>{
        })
        let complianceData = JSON.parse(JSON.stringify(_this.complianceData))
        updateDocClassSetting(complianceData).then(res=>{
        })
        let filePurposeData = JSON.parse(JSON.stringify(_this.filePurposeData))
        updateDocClassSetting(filePurposeData).then(res=>{
        })
        let regulationStatusData = JSON.parse(JSON.stringify(_this.regulationStatusData))
        updateDocClassSetting(regulationStatusData).then(res=>{
        })
        let regulationPublishDateData = JSON.parse(JSON.stringify(_this.regulationPublishDateData))
        updateDocClassSetting(regulationPublishDateData).then(res=>{
        })
        let regulationImplementDateData = JSON.parse(JSON.stringify(_this.regulationImplementDateData))
        updateDocClassSetting(regulationImplementDateData).then(res=>{
        })
        let programVersionId = JSON.parse(JSON.stringify(_this.programVersionId))
        updateDocClassSetting(programVersionId).then(res=>{
        })
       setTimeout(() => {
        _this.loading = false;
       }, 1000);
    }
  }
}
</script>

<style scoped>

</style>
