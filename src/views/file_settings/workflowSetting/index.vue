<template>
  <div class="app-container el-card is-always-shadow">
    <div class="el-card__header">
      <div slot="header" class="clearfix">
        <span>流程设置</span>
      </div>
    </div>
    <div class="el-card__body">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        label-width="68px"
      >
        <div class="global-ser" :class="!boxClass ? '' : 'open'" id="add">
          <div class="ser-top">
            <div class="cell-left">
              <el-input
                v-model.trim="queryParams.className"
                placeholder="输入分类名称搜索"
                clearable
                @keyup.enter.native="handleQuery"
                class="input-with-select"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="handleQuery"
                ></el-button>
              </el-input>
              <el-button
                class="button"
                @click="activeSearchBox"
                v-if="!boxClass"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button
                v-else
                class="button"
                @click="activeSearchBox"
                style="background: #013288; border-color: #013288; color: #fff"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery"
                >重置
              </el-button>
            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleAdd()">新增</el-button>
            </div>
          </div>
          <div class="ser-bottom">
            <div class="cell-left">
              <el-form-item label="分类层级">
                <el-input
                  placeholder="分类层级"
                  clearable
                  v-model.trim="queryParams.classLevel"
                ></el-input>
              </el-form-item>
              <el-form-item label="分类代码">
                <el-input
                  placeholder="分类代码"
                  clearable
                  v-model.trim="queryParams.id"
                ></el-input>
              </el-form-item>
              <el-form-item label="分类状态:" prop="classStatus">
              <el-select
                placeholder="分类状态"
                v-model.trim="queryParams.classStatus"
                clearable
              >
                <el-option
                  v-for="dict in dict.type.class_status"
                  :key="dict.value"
                  :label="dictLanguage(dict)"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
              </el-form-item>
              <el-form-item label="所属类型:" prop="classType">
                <el-select
                  v-model.trim="queryParams.classType"
                  clearable
                >
                  <el-option
                    v-for="dict in dict.type.class_type"
                    :key="dict.value"
                    :label="dictLanguage(dict)"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
              <el-button @click="boxClass = false">取消</el-button>
            </div>
          </div>
        </div>
      </el-form>
      <el-card class="gray-card">
        <el-table
          v-loading="loading"
          ref="dragTable"
          :key="tableKey"
          :data="postList"
          @selection-change="handleSelectionChange"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          row-key="id"
        >
          >
          <el-table-column
            type="selection"
            width="55"
            align="left"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="id"
            align="left"
            prop="id"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="流程Key"
            align="left"
            prop="flowKey"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="流程名称"
            align="left"
            prop="flowKey"
            :formatter="formatterFlowKey"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            label="分类状态"
            align="left"
            prop="openFlag"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.openFlag == 'Y' ? "启用" : "禁用" }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="left"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleUpdata(scope.row)"
                >编辑
              </el-button>
              <el-button
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
                >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <el-drawer
        :visible.sync="drawer"
        direction="rtl"
        size="80%"
        modal-append-to-body
        :with-header="false"
        :show-close="false"
        :destroy-on-close="true"
      >
        <add-or-update :id="id" @close="closeDrawer"></add-or-update>
      </el-drawer>

    </div>
  </div>
</template>

<script>
import {
  settingDocClassList,
  settingDocClassIds,
} from "@/api/file_settings/type_settings";
import AddOrUpdate from '@views/file_settings/workflowSetting/addOrUpdate.vue'
import { listDocClassFlow, queryFlowList } from '@/api/setting/docClassFlow'

export default {
  name: "Post",
  dicts: ["sys_normal_disable", "class_status","class_type"],
  components: {
    AddOrUpdate
    // rzDrawer,
  },
  data() {
    return {
      tableKey: undefined,
      boxClass: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        id: undefined,
        noClass: true
      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: {
        postName: [{ required: true, message: "不能为空", trigger: "blur" }],
        postCode: [{ required: true, message: "不能为空", trigger: "blur" }],
        postSort: [{ required: true, message: "不能为空", trigger: "blur" }],
      },
      taskData: [], // 任务数据
      drawer: false,
      drawerSettings: false,
      taskFormData: {},
      varChangeColor1: true,
      varChangeColor2: false,
      pButton: "add",
      id: "",
      flowList: [],
    };
  },
  created() {
    this.getFlowList()
    this.getList();
  },
  methods: {
    /** 查询列表 */
    async getList() {
      this.loading = true;
      const { rows } = await listDocClassFlow(this.queryParams);
      this.$set(this, "postList", this.handleTree(rows, "id", "parentClassId"));
      this.tableKey = new Date().getTime();
      this.$nextTick(() => {
        this.loading = false;
      });
    },
    getFlowList(){
      queryFlowList({}).then(res=>{
        this.flowList = res.data
      })
    },
    formatterFlowKey(row, column, cellValue, index){
      if (this.flowList&&this.flowList.length>0) {
       let item = this.flowList.find(item=>item.dictValue === cellValue)
        return item?item.dictLabel:""
      }
      return ""
    },
    // 取消按钮
    cancel() {
      this.open = false;
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,

        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.classStatus = "";
      this.queryParams.className = "";
      this.queryParams.classLevel = "";
      this.queryParams.id = "";
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.postId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    activeSearchBox() {
      this.boxClass = !this.boxClass;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.drawer = true;
      this.id = undefined;
    },
    handleUpdata(e) {
      //console.log(e);
      this.id = e.id;
      this.drawer = true;
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.id || this.ids;
      console.log(postIds);
      this.$modal
        .confirm('是否确认删除分类名称为"' + row.className + '"的数据项？')
        .then(function () {
          return settingDocClassIds(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    closeDrawer(v) {
      this.drawer = false;
      this.getList();
    },
  },
};
</script>
