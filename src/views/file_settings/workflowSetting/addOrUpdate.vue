<template>
  <div class="document_change_add" v-loading="loading">
    <div class="drawer-head">
      <div class="cell-title">
        <div>
          <p class="title">流程设置</p>
        </div>
      </div>
      <div class="cell-btn">
        <el-button type="primary" @click="saveDocClassFlow">提交</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </div>
      <el-card class="gray-card">
        <el-form
          ref="elForm"
          :model="formData"
          :rules="rules"
          label-position="left"
          label-width="160px"
        >
          <el-form-item
            label="选择流程模板:"
            prop="flowKey">
            <el-select
              placeholder="流程模板"
              v-model.trim="formData.flowKey"
              @change="onChangeFlowKey"
            >
              <el-option
                v-for="dict in template"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              >
              </el-option>
            </el-select>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-refresh"
              @click="handleRefresh()"
            >同步最新节点</el-button>
          </el-form-item>
          <el-form-item label="是否启用:" prop="openFlag">
            <el-switch
              v-model.trim="formData.openFlag"
              active-color="#13ce66"
              active-value="Y"
              inactive-value="N"
              size="medium"
              inactive-color="#ff4949"
            >
            </el-switch>
          </el-form-item>
          <el-alert
            v-if="alertTitle"
            :title="alertTitle"
            style="margin-bottom: 5px"
            type="error">
          </el-alert>
          <span>待办按钮控制:</span>
          <el-table :data="formData.nodeList" border>
            <el-table-column label="序号" type="index" width="50"></el-table-column>
            <el-table-column label="环节节点" align="left" prop="nodeName" width="200"/>
            <el-table-column label="按钮权限" align="left" prop="nodeDetailList">
              <template slot-scope="scope">
                <el-tag
                  v-for="tag in scope.row.nodeDetailList"
                  closable
                  @close="()=>handleClose(tag,scope.row)"
                  :key="tag.code"
                  :type="tag.code">
                  {{tag.name}}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="left" width="100">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="handleSelect(scope.row)"
                >重新选择</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </el-card>
      <el-dialog title="选择按钮权限" append-to-body :visible.sync="visible" width="800px"  :close-on-click-modal="false">
        <el-transfer
          v-model="value"
          :titles="['待选按钮（多选）', '已选按钮']"
          :data="dict.type.flow_node_fun_list"
          :props="{key: 'value',label:'label'}"
        >
        </el-transfer>
        <div slot="footer" class="dialog-footer">
          <el-button @click="visible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </el-dialog>
    </div>
</template>

<script>
    import {getByFlowKey,compareFlowPlatNodeList,syncFlowPlatNodeList} from "@/api/setting/docClassFlowNode";
    import {
      queryFlowList,
      getByDocClass,
      saveDocClassFlow,
      addDocClassFlow,
      getDocClassFlow
    } from '@/api/setting/docClassFlow'
    export default {
        name: "AddOrUpdate",
        props: ['id'],
        dicts: ['sys_yes_no','flow_node_fun_list'],
        data() {
            return {
                loading: false,
                alertTitle: '',
                data: [],
                row: {},
                value: [],
                template: [],
                activeIndex: 'ADD',
                visible: false,
                formData: {
                    openFlag: 'N',
                    applyFlag: 'N',
                    flowKey: undefined,
                    bizType: 'ADD',
                    nodeList: []
                },
                rules:{

                }
            }
        },
        mounted(){
            this.getByDocClass()
        },
        methods: {
            reset(){
                this.formData= {
                    docClass: this.id,
                    openFlag:'N',
                    applyFlag: 'N',
                    flowKey: undefined,
                    bizType: undefined,
                    nodeList: []
                }
            },
            getByDocClass(){
                let _this = this
                _this.changeTemplate()
                if (this.id) {
                  _this.loading = true
                  getDocClassFlow(this.id).then(res=>{
                    this.formData = res.data
                    _this.loading = false
                  })
                }
                _this.reset()
            },
            handleClose(tag,row){
                row.nodeDetailList.splice(row.nodeDetailList.findIndex(node=>node.code===tag.code), 1);
            },
            onChangeFlowKey(flowKey) {
                let _this = this
                getByFlowKey(_this.id,_this.formData.bizType,flowKey).then(res=>{
                    _this.formData.nodeList=res.data
                })
            },
            changeTemplate(){
                let _this = this
                _this.loading = true
                _this.formData = {}
                // 获取流程模板
                queryFlowList({flowKey:undefined}).then(res=>{
                  _this.template = res.data
                  _this.loading = false
                })
            },
            handleSelect(row){
                let _this = this
                _this.visible=true
                _this.row = row
                if(row.nodeDetailList){
                    _this.value = row.nodeDetailList.map(node=>node.code)
                }else {
                    _this.value = []
                }

            },
            submitForm(){
                let _this = this
                _this.visible=false
                let nodeDetailList = []
                _this.value.forEach(code=>{
                    let node =_this.dict.type.flow_node_fun_list.find(node=>node.value===code)
                    nodeDetailList.push({
                        code: node.value,
                        name: node.label,
                        remark: _this.row.nodeName,
                        type: node.raw.remark,
                        sort: node.raw.dictSort
                    })
                })
                _this.row.nodeDetailList = nodeDetailList
            },
            saveDocClassFlow(){
              let _this = this
              if (!_this.formData.id) {
                addDocClassFlow(_this.formData).then((res)=>{
                  this.$modal.msgSuccess("设置成功");
                  this.close()
                })
              }else {

              }
            },
            handleRefresh() { // 对接流程平台，同步最新的节点
              let _this = this
              syncFlowPlatNodeList(_this.id,_this.formData.bizType,_this.formData.flowKey,_this.formData.id === undefined ? "null" : _this.formData.id).then(res=>{
                this.$modal.msgSuccess("同步成功");
                _this.formData.nodeList=res.data
                _this.alertTitle = ''
              })
            },
          close(){
              this.$emit("close")
          }
        }
    }
</script>

<style scoped>

</style>
