<template>
  <div class="app-container el-card is-always-shadow">
    <div class="el-card__body">
      <el-tabs v-model.trim="activeName" class="out-tabs" @tab-click="state">
        <el-tab-pane
          v-for="(item, i) in linkTypeTab"
          :key="i"
          :class="{ switch: varChangeColor == item.id }"
          :name="i"
          @click="state(item.id)"
        >
          <span slot="label">
            {{ item.className }}
          </span>
        </el-tab-pane>
      </el-tabs>
      <el-form :model="queryParams" v-show="showSearch" label-width="68px">
        <div class="global-ser" :class="!boxClass ? '' : 'open'" id="add">
          <div class="ser-top">
            <div class="cell-left">
              <el-date-picker
                v-model="value2"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                unlink-panels
                align="right"
                size="small"
                style="float: left; height: 34px; margin-right: 5px"
              >
              </el-date-picker>
              <el-input
                v-model.trim="queryParams.docName"
                placeholder="输入文件名称搜索"
                @keyup.enter.native="handleQuery"
                class="input-with-select"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="handleQuery"
                ></el-button>
              </el-input>
              <el-button
                class="button"
                @click="activeSearchBox"
                v-if="!boxClass"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button
                v-else
                class="button"
                @click="activeSearchBox"
                style="background: #013288; border-color: #013288; color: #fff"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery"
                >重置
              </el-button>
            </div>
            <div class="cell-right">
              <el-button
                plain
                @click="handleRemove()"
                :disabled="multiple"
                v-hasPermi="['system:versionFavorites:remove']"
                >删除</el-button
              >
            </div>
          </div>
          <div class="ser-bottom">
            <div class="cell-left">
              <el-form-item label="文件类型">
                <treeselect
                  v-model.trim="queryParams.docClass"
                  :options="classLevelOptions"
                  :normalizer="normalizerFile"
                  :searchable="false"
                  :show-count="true"
                  placeholder="选择文件类型"
                />
              </el-form-item>
              <el-form-item label="文件编号">
                <el-input
                  placeholder="文件编号"
                  v-model.trim="queryParams.docId"
                ></el-input>
              </el-form-item>
            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
              <el-button @click="boxClass = false">取消</el-button>
            </div>
          </div>
        </div>
      </el-form>
      <el-card class="gray-card">
        <el-table
          v-loading="loading"
          :data="postList"
          @sort-change="sortChange"
          @selection-change="handleSelectionChange"
          header-align="left"
        >
          <el-table-column
            type="selection"
            width="55"
            align="left"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="文件名称"
            sortable="custom"
            align="left"
            width="300"
            prop="docName"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span v-show="scope.row.receiveStatus == '1'" class="wenjcolor"
                ><a @click="handlePreview(scope.row, 'THIS_DEPT')">{{
                  scope.row.docName
                }}</a></span
              >
              <span v-show="scope.row.receiveStatus != '1'">{{
                scope.row.docName
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="文件编号"
            align="left"
            sortable="custom"
            :prop="queryParams.linkType == 'REC'?'recordDocId':'docId'"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span
                v-if="
                  queryParams.linkType == 'REC' && scope.row.recordDocId != 0
                "
                >{{ scope.row.recordDocId }}</span
              >
              <span v-else>{{ scope.row.docId }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="文件版本"
            align="left"
            prop="versionValue"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="文件类型"
            align="left"
            prop="docClass"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ handleFileType(scope.row.docClass) }}</span>
            </template>
          </el-table-column>

          <!-- <el-table-column
            label="编制部门"
            align="left"
            prop="deptName"
            :show-overflow-tooltip="true"
          /> -->
          <el-table-column
            label="生效日期"
            align="left"
            sortable="custom"
            prop="startDate"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.startDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="失效日期"
            align="left"
            sortable="custom"
            prop="endDate"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.endDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="复审日期"
            align="left"
            sortable="custom"
            prop="endDate"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.reviewTime">{{
                parseTime(scope.row.reviewTime, "{y}-{m}-{d}")
              }}</span>
              <span
                v-if="
                  scope.row.reviewTime == null || scope.row.reviewTime == ''
                "
                ></span
              >
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="left"
            width="95"
            class-name="small-padding fixed-width"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleDetails(scope.row)"
                v-if="
                  scope.row.receiveStatus === '1' &&
                  checkPermi(['documentAccount:thisDepartment:detail'])
                "
                >详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <el-drawer
        :visible.sync="drawerShow"
        direction="rtl"
        size="90%"
        :with-header="false"
        :wrapperClosable="false"
        :show-close="false"
        modal-append-to-body
        :destroy-on-close="true"
      >
        <main-component ref="mainComponent" :code="path+code"  :data="data"  :dataType="queryParams.dataType" @close="handleCloseChange"></main-component>
      </el-drawer>
      <as-pre-view
        :visible="viewShow"
        :id="viewId"
        ref="viewRef"
        @close="close"
      >
      </as-pre-view>
    </div>
  </div>
</template>

<script>
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {queryPageFavorites,queryPageFavoritesRecordFile,delVersionFavorites} from "@/api/document_account/versionFavorites";
import {
  settingDocClassList,
} from "@/api/file_settings/type_settings";
import rzDrawer from "../../components/rz-drawer";
import { checkPermi, checkRole } from "@/utils/permission";
import mainComponent from '@/components/mainComponent/index.vue' // 权限判断函数
export default {
  name: "Post",
  dicts: ["sys_normal_disable"],
  components: {
    mainComponent,
    rzDrawer,
    Treeselect
  },
  data() {
    return {
      drawerShow: false,
      path: 'views/workflowList/addWorkflow/',
      code: '',
      data: undefined,
      viewShow: false,
      viewId: "",
      boxClass: false,
      // 文档id
      id: "",
      fileTypeList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      drawer: false,
      drawerDetails: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderByColumn: undefined,
        isAsc: undefined,
        // receiveStatus: '1',
        postName: undefined,
        status: 1,
        docClass: "DEO",
        searchValue: "",

        docName: undefined,

        docId: null,
        deptId: null,
        // inside: JSON.parse(sessionStorage.getItem("USER_INFO")).dept.deptId,
        // outside: "",
        params: {
          startTime: "",
          endTime: "",
        },
        dataType: undefined,
      },
      srcList: [
        "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
      ],
      varChangeColor: "DEO",
      linkTypeTab: [],
      pListData: {},
      classLevelOptions: [],
      activeName: 0,
      value2: "",
    };
  },
  created() {
    /*
    by xfc 20230626
    系统分别建立 体系文件类型设置和项目文件类型设置菜单，路由参数不一样
    dataType参数区分：体系文件=stdd、项目文件=project
    */
    this.queryParams.dataType = this.$route.query.dataType
    this.getSettingDocClassTreeseList();
  },
  watch: {
    value2(val) {
      if (val != null) {
        this.queryParams.params.startTime = val[0];
        this.queryParams.params.endTime = val[1];
      } else {
        this.queryParams.params.startTime = "";
        this.queryParams.params.endTime = "";
      }
    },
  },
  mounted() {
    settingDocClassList({
      pageNum: 1,
      pageSize: 100,
      classStatus: "1",
      dataType: this.queryParams.dataType,
    }).then((response) => {
      this.fileTypeList = response.rows;
      this.handleTree(response.rows, "id", "parentClassId").forEach(
        (element) => {
          this.linkTypeTab.push({
            className: element.className,
            id: element.id,
          });
        }
      );
      this.queryParams.docClass = this.linkTypeTab[0].id;
      this.varChangeColor = this.linkTypeTab[0].id;
      this.getList();
    });
  },
  methods: {
    checkPermi,
    checkRole,
    getSettingDocClassTreeseList() {
      settingDocClassList({ classStatus: "1",dataType: this.queryParams.dataType}).then(
        (response) => {
          this.classLevelOptions = [];
          response.rows.forEach((element, index) => {
            response.rows[index].children = [];
          });
          this.classLevelOptions = this.handleTree(
            response.rows,
            "id",
            "parentClassId"
          );
        }
      );
    },
    normalizerFile(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    handlePreview(row, source) {
      if (row.encryptFileId != null) {
        this.viewId = row.encryptFileId;
      } else if (row.mergeFileId != null) {
        this.viewId = row.mergeFileId;
      } else {
        this.viewId = row.fileId;
      }
      this.$refs.viewRef.handleOpenView(this.viewId, source);
      this.viewShow = true;
    },
    close() {
      this.viewShow = false;
    },
    activeSearchBox() {
      this.boxClass = !this.boxClass;
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      if (this.queryParams.docClass == "REC") {
        // this.queryParams.inside = JSON.parse(
        //   sessionStorage.getItem("USER_INFO")
        // ).dept.deptId;
        queryPageFavoritesRecordFile(this.queryParams).then((response) => {
          this.postList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      } else {
        this.queryParams.inside = "";
        queryPageFavorites(this.queryParams).then((response) => {
          this.postList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      }
    },
    handleFileType(obj) {
      if (obj && this.fileTypeList) {
        let arr = this.fileTypeList.filter((x) => x.id === obj)[0];
        if (arr) {
          return arr.className;
        }
        return obj;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.docName = null;
      this.queryParams.docId = null;
      this.queryParams.docClass = this.varChangeColor;
      this.handleQuery();
    },
    sortChange({ column, prop, order }){
        if (order==='ascending') {
            this.queryParams.orderByColumn = prop
            this.queryParams.isAsc = 'asc'
        }else if(order==='descending') {
            this.queryParams.orderByColumn = prop
            this.queryParams.isAsc = 'desc'
        }else {
            this.queryParams.orderByColumn = undefined
            this.queryParams.isAsc = undefined
        }
        this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.versionId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 详情按钮操作 */
    handleDetails(row) {
      let _this = this
      _this.handleDetail({type:'detail/index',docId:row.docId,versionId:row.versionId,flag:'0',status:this.queryParams.status})
    },
    handleDetail(row){
      let _this = this
      _this.code = row.type
      _this.data = row
      _this.drawerShow = true
    },
    chinldClose() {
        this.drawerDetails = false;
        this.getList();
    },
    closeDrawerDetails() {
      this.drawerDetails = false;
    },
    state(tab) {
      let t = tab.index;
      let index = this.linkTypeTab[t].id;
      this.varChangeColor = index;
      this.queryParams.docClass = index;
      this.getList();
    },
    handleRemove() {
      this.$modal
          .confirm('是否确认将选中文件移出我的收藏？')
          .then(() => {
              return delVersionFavorites(this.ids);
          })
          .then(() => {
              this.getList();
              this.$modal.msgSuccess("删除成功");
          })
          .catch(() => {});
    },
  },
  handleCloseChange(){
    this.distributePrintShow = false
    this.distributeRangeShow = false
    this.drawerShow = false
    this.getList();
  },
};
</script>
