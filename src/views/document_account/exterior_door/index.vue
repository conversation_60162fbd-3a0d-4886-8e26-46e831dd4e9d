<template>
  <div class="app-container companyindex el-card is-always-shadow">
    <div class="el-card__body">
      <el-tabs v-model.trim="activeName" class="out-tabs" @tab-click="state">
        <el-tab-pane
          v-for="(item, i) in linkTypeTab"
          :key="i"
          :class="{ switch: varChangeColor == item.id }"
          :name="i"
          @click="state(item.id)"
        >
          <span slot="label">
            {{ item.className }}
          </span>
        </el-tab-pane>
      </el-tabs>
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        :label-width=" columnLangSizeFlag? '88px' : '68px'"
      >
        <div
          class="global-ser"
          id="add"
          :class="!boxClass ? '' : 'open'"
        >
          <div class="ser-top">
            <div class="cell-left">
              <el-date-picker
                v-model="value2"
                type="datetimerange"
                :picker-options="pickerOptions"
                :range-separator="$t(`doc.this_dept_to`)"
                :start-placeholder="$t(`doc.this_dept_start_date`)"
                :end-placeholder="$t(`doc.this_dept_end_date`)"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                unlink-panels
                align="right"
                size="small"
                style="float: left; height: 34px; margin-right: 5px"
              >
              </el-date-picker>
              <el-input
                v-model="queryParams.docName"
                :placeholder="$t(`doc.this_dept_name_select`)"
                @keyup.enter.native="handleQuery"
                class="input-with-select"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="handleQuery"
                ></el-button>
              </el-input>
              <el-button
                class="button"
                @click="activeSearchBox"
                v-if="!boxClass"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button
                v-else
                class="button"
                @click="activeSearchBox"
                style="background: #013288; border-color: #013288; color: #fff"
              >
                <i class="icon iconfont icon-zongheshaixuan-"></i>
              </el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery"
              >{{$t(`doc.this_dept_reset`)}}
              </el-button>
            </div>
            <div class="cell-right">
              <el-button v-if="queryParams.status==1"
                         v-hasPermi="['process:wbm:standard:export']"
                         @click="handleExport()">{{ $t(`doc.exterior_dept_export`) }}</el-button>
<!--              <el-button-->
<!--                v-if="queryParams.status==1"-->
<!--                @click="handleReissue()"-->
<!--              >补发申请</el-button>-->
<!--              <el-button-->
<!--                v-if="queryParams.status==1"-->
<!--                @click="handleAddReissue()"-->
<!--              >增发申请</el-button>-->
            </div>
          </div>
          <div class="ser-bottom">
            <div class="cell-left">
              <el-form-item :label="$t(`doc.this_dept_file_code`)" prop="docId">
                <el-input
                  :placeholder="$t(`doc.this_dept_file_code`)"
                  v-model="queryParams.docId"
                  @keyup.enter.native="handleQuery"
                ></el-input>
              </el-form-item>
              <el-form-item :label="$t(`doc.this_dept_staffing_dept`)" :label-width=" columnLangSizeFlag? '178px' : '88px'" prop="outside">
                <treeselect
                  v-model.trim="queryParams.outside"
                  :options="deptOptions"
                  :normalizer="normalizer"
                  :placeholder="$t(`doc.this_dept_select_dept`)"
                  style="width: 200px"
                  :searchable="false"
                  @keyup.enter.native="handleQuery"
                  size="mini"
                />
              </el-form-item>
              <el-form-item :label="$t(`doc.this_dept_file_type`)" prop="docClass">
                <treeselect
                  v-model.trim="queryParams.docClass"
                  :options="docClassTree.filter(item=>item.id===varChangeColor)"
                  @select="handleSelectNode"
                  @keyup.enter.native="handleQuery"
                  :normalizer="normalizerFile"
                  :placeholder="$t(`doc.this_dept_select_type`)"
                  style="width: 200px"
                  :searchable="false"
                  :clearable="false"
                  size="mini"
                />
              </el-form-item>
              <template v-if="isDeviceShow">
                <el-form-item :label="$t(`doc.this_dept_unit_code`)" prop="deviceCode">
                  <el-input
                    :placeholder="$t(`doc.this_dept_unit_code_select`)"
                    v-model="queryParams.deviceCode"
                    @keyup.enter.native="handleQuery"
                  ></el-input>
                </el-form-item>
              </template>
              <template v-if="internalDocIdShow">
                <el-form-item :label="$t(`doc.this_dept_internal_file_number`)" prop="internalDocId">
                  <el-input
                    :placeholder="$t(`doc.this_dept_internal_file_number`)"
                    v-model="queryParams.internalDocId"
                    @keyup.enter.native="handleQuery"
                  ></el-input>
                </el-form-item>
              </template>
              <template v-if="isCustomerShow">
                <el-form-item :label="$t(`doc.this_dept_client_code`)" prop="customerCode">
                  <el-input
                    :placeholder="$t(`doc.this_dept_client_code_select`)"
                    v-model="queryParams.customerCode"
                    @keyup.enter.native="handleQuery"
                  ></el-input>
                </el-form-item>
              </template>
              <template v-if="isShowPart">
                <el-form-item :label="$t(`doc.this_dept_product_code`)" prop="partNumber">
                  <el-input
                    :placeholder="$t(`doc.this_dept_product_code_select`)"
                    v-model="queryParams.partNumber"
                    @keyup.enter.native="handleQuery"
                  ></el-input>
                </el-form-item>
                <el-form-item :label="$t(`doc.this_dept_product_summary`)" prop="partRemark">
                  <el-input
                    :placeholder="$t(`doc.this_dept_product_summary_select`)"
                    v-model="queryParams.partRemark"
                    @keyup.enter.native="handleQuery"
                  ></el-input>
                </el-form-item>
                <el-form-item :label="$t(`doc.this_dept_factory`)" prop="factorys">
                  <el-select
                    :placeholder="$t(`doc.this_dept_factory_select`)"
                    v-model="queryParams.factorys"
                    @keyup.enter.native="handleQuery"
                  >
                    <el-option
                      v-for="dict in dict.type.tenant_list"
                      :key="dict.value"
                      :label="dictLanguage(dict)"
                      :value="dict.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </template>

              <template v-if="isProductVersionShow">
                <el-form-item :label="$t(`doc.this_dept_product_version`)" prop="productVersion">
                  <el-input
                    :placeholder="$t(`doc.this_dept_product_version_select`)"
                    v-model="queryParams.productVersion"
                    @keyup.enter.native="handleQuery"
                  ></el-input>
                </el-form-item>
              </template>
              <template v-if="projectCodeShow">
                <el-form-item :label="$t(`doc.this_dept_project_code`)" prop="projectCode">
                  <el-select
                    filterable
                    clearable
                    :placeholder="$t(`doc.this_dept_project_code`)"
                    v-model="queryParams.projectCode"
                    @keyup.enter.native="handleQuery"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="dict in dict.type.project_code_list"
                      :key="dict.value"
                      :label="dictLanguage(dict)"
                      :value="dict.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </template>
              <template>
                <el-form-item :label="$t(`doc.this_dept_staffs`)" prop="nickName">
                  <el-input
                    :placeholder="$t(`doc.this_input_dept_staffs`)"
                    v-model="queryParams.nickName"
                    @keyup.enter.native="handleQuery"
                  ></el-input>
                </el-form-item>
              </template>
            </div>
            <div class="cell-right">
              <el-button type="primary" @click="handleQuery">{{ $t(`doc.this_dept_query`) }}</el-button>
              <el-button @click="resetQuery">{{ $t(`doc.this_dept_reset`) }}</el-button>
              <el-button @click="boxClass = false">{{ $t(`doc.this_dept_abolish`) }}</el-button>
            </div>
          </div>
        </div>
      </el-form>
      <el-card class="gray-card">
        <el-table
          border
          v-loading="loading"
          :data="postList"
          @sort-change="sortChange"
          @selection-change="handleSelectionChange"
          :row-class-name="tableRowClassName"
          ref="multipleTable"
          header-align="left"
        >
          <el-table-column
            type="selection"
            width="55"
            align="left"
            :show-overflow-tooltip="true"
          />
          <template>
            <el-table-column
              :label="$t(`doc.this_dept_file_code`)"
              sortable="custom"
              align="left"
              width="120"
              prop="docId"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.docId }}</span>
              </template>
            </el-table-column>

            <el-table-column
              :label="$t(`doc.this_dept_file_name`)"
              align="left"
              sortable="custom"
              prop="docName"
              width="150"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span class="wenjcolor" ><a @click="handlePreview(scope.row, 'COMPANY')">{{scope.row.docName}}</a></span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_file_version`)"
              align="left"
              prop="versionValue"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              :label="$t(`doc.this_dept_Initial_release_date`)"
              align="left"
              sortable="custom"
              prop="releaseTime"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.releaseTime, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_effective_date`)"
              align="left"
              sortable="custom"
              prop="startDate"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.startDate, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>

            <template v-if="isCustomerShow">
              <el-table-column
                :label="$t(`doc.this_dept_client_code`)"
                align="left"
                prop="customerCode"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.customerCode }}</span>
                </template>
              </el-table-column>
            </template>

            <template v-if="isShowPart">
              <el-table-column
                :label="$t(`doc.this_dept_product_code`)"
                align="left"
                prop="partNumber"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                :label="$t(`doc.this_dept_product_summary`)"
                align="left"
                prop="partRemark"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.partRemark }}</span>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t(`doc.this_dept_factory`)"
                align="left"
                prop="factorys"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="scope">
                  <span>{{ getDictLabel(scope.row.factorys) }}</span>
                </template>
              </el-table-column>
            </template>

            <template v-if="isProductVersionShow">
              <el-table-column
                :label="$t(`doc.this_dept_product_version`)"
                align="left"
                prop="productVersion"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.productVersion }}</span>
                </template>
              </el-table-column>
            </template>

            <template v-if="projectCodeShow">
              <el-table-column
                :label="$t(`doc.this_dept_project_code`)"
                align="left"
                prop="projectCode"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
            </template>

<!--            <el-table-column-->
<!--              :label="$t(`doc.this_dept_revise_date`)"-->
<!--              align="left"-->
<!--              sortable="custom"-->
<!--              prop="revisionDate"-->
<!--              :show-overflow-tooltip="true"-->
<!--            >-->
<!--              <template slot-scope="scope">-->
<!--                <span>{{ parseTime(scope.row.revisionDate, "{y}-{m}-{d}") }}</span>-->
<!--              </template>-->
<!--            </el-table-column>-->

            <el-table-column
              :label="$t(`doc.this_dept_change_content`)"
              align="left"
              prop="content"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              :label="$t(`doc.this_document_review_date`)"
              align="left"
              sortable="custom"
              prop="reviewTime"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.reviewTime, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>

            <el-table-column
              :label="$t(`doc.this_dept_file_status`)"
              align="left"
              prop="status"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <dict-tag :options="dict.type.standard_status" :value="scope.row.status"/>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(`dict.expired_status`)"
              align="left"
              prop="expiredStatus"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <dict-tag :options="dict.type.expired_status" :value="scope.row.expiredStatus"/>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(`doc.this_dept_staffing_dept`)"
              align="left"
              prop="deptName"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.deptName }}</span>
              </template>
            </el-table-column>

            <el-table-column
              :label="$t(`doc.this_dept_staffing`)"
              align="left"
              prop="nickName"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.nickName }}</span>
              </template>
            </el-table-column>
          </template>

          <el-table-column
            :label="$t(`doc.this_dept_operation`)"
            align="left"
            width="95"
            fixed="right"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleDetails(scope.row)"
              >{{$t(`doc.this_dept_detail`)}}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <as-pre-view
        :visible="viewShow"
        :id="viewId"
        ref="viewRef"
        @close="close"
      >
      </as-pre-view>
      <el-drawer
        :visible.sync="drawerShow"
        direction="rtl"
        size="90%"
        :with-header="false"
        :wrapperClosable="false"
        :show-close="false"
        modal-append-to-body
        :destroy-on-close="true"
      >
        <main-component ref="mainComponent" :code="path+code"  :data="data"  :dataType="queryParams.dataType" @close="handleCloseChange"></main-component>
      </el-drawer>
      <el-drawer
        :visible.sync="distributePrintShow"
        direction="rtl"
        size="90%"
        :with-header="false"
        :wrapperClosable="false"
        :show-close="false"
        modal-append-to-body
        :destroy-on-close="true"
      >
        <distribute-print ref="distributePrint"  :data="data" @close="handleCloseChange"></distribute-print>
      </el-drawer>
      <el-drawer
        :visible.sync="distributeRangeShow"
        direction="rtl"
        size="90%"
        :with-header="false"
        :wrapperClosable="false"
        :show-close="false"
        modal-append-to-body
        :destroy-on-close="true"
      >
        <distribute-range ref="distributeRange"  :data="data" @close="handleCloseChange"></distribute-range>
      </el-drawer>
      <el-drawer
        :visible.sync="importShow"
        direction="rtl"
        size="90%"
        :with-header="false"
        :wrapperClosable="false"
        :show-close="true"
        modal-append-to-body
        :destroy-on-close="true"
      >
        <div class="drawer-head">
          <div class="cell-title">
            <p class="title">{{$t(`doc.this_dept_import_history_data`)}}</p>
          </div>
          <div class="cell-btn">
            <el-button @click="importShow=false">{{ $t(`doc.this_dept_close`) }}</el-button>
          </div>
        </div>
        <div class="drawer-body">
          <initHistory dataType="stdd" :docClass="varChangeColor"></initHistory>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listVersion,
  distributeLogcheckSign, listOtherDept
} from '@/api/document_account/version'
import { settingDocClassList } from "@/api/file_settings/type_settings";
import mainComponent from "@/components/mainComponent/index.vue";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { listDept } from "@/api/system/dept";
import mixin from "@/layout/mixin/Commmon.js";
// 引入文件新增页面（同时适应文件修订、文件作废发起页面）
import { selectStatusByDocId } from "@/api/my_business/workflowApplyLog";
import initHistory from "@/views/system/dispose/initHistory";
import DistributePrint from '@views/document_account/distribute/print.vue'
import DistributeRange from '@views/document_account/distribute/range.vue'
import { getInfoBy } from '@/api/setting/docClassSetting'

export default {
  name: "ExteriorDoor",
  dicts: ["standard_status","tenant_list",'project_code_list',"expired_status"],
  components: {
    DistributeRange,
    DistributePrint,
    Treeselect,
    mainComponent,
    initHistory
  },
  props: ["pButton", "menuitem"],
  mixins: [mixin],
  data() {
    return {
      docClassList:[],
      distributePrintShow: false,
      distributeRangeShow: false,
      disable: false,
      activeName: undefined,
      drawerShow: false,
      path: 'views/workflowList/addWorkflow/',
      code: '',
      deptOptions:[],
      data: undefined,
      dateTime: new Date().getTime(),
      viewShow: false,
      viewId: "",
      boxClass: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        postCode: undefined,
        postName: undefined,
        searchValue: "",
        docId: null,
        docClass: null,
        docNames: null,
        docName:null,
        outside: null,
        params: {
          startTime: "",
          endTime: "",
        },
        dataType: undefined,
        // 有效版本文件 1有效、2失效
        status: '1',
        partNumber: null,
        partRemark: null,
        factorys: null,
        productVersion: null,
        projectCode: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        postName: [{ required: true, message: this.$t(`doc.this_dept_not_null`), trigger: "blur" }],
        postCode: [{ required: true, message: this.$t(`doc.this_dept_not_null`), trigger: "blur" }],
        postSort: [{ required: true, message: this.$t(`doc.this_dept_not_null`), trigger: "blur" }],
      },
      taskData: [], // 任务数据
      borrowOpen: false,
      addBorrow: false,
      drawerDetails: false,
      taskFormData: {},
      varChangeColor: "",
      companySelection: [],
      selectDoc: "",
      linkTypeTab: [],
      addDrawer: false,
      selectInfoData: [],
      pListData: {},
      classLevelOptions: [],
      userInfo: JSON.parse(sessionStorage.getItem("USER_INFO")),
      value2: "",
      selectionData: [],
      importShow:false,
      docClassTree: [],
      changeClassType: '',
      isShowPart:false,
      isCustomerShow:false,
      isDeviceShow:false,
      internalDocIdShow:false,
      columnLangSizeFlag: sessionStorage.getItem('language') == 'en' ? true : false,
      isProductVersionShow:false,
      projectCodeShow: false,
    };
  },
  created() {
    /*
    by xfc 20230626
    系统分别建立 体系文件类型设置和项目文件类型设置菜单，菜单配置路由参数不一样
    dataType参数区分：体系文件=stdd、项目文件=project
    status参数区分：有效文件=1、失效文件=2
    */
    this.queryParams.dataType = this.$route.query.dataType
    this.queryParams.status = this.$route.query.status
    let openFunc= this.$route.query.openFunc
    if(openFunc==='add'){
      this.handleAdd()
    }
    this.getSettingDocClassTreeseList();
    listDept({ status: 0}).then((response) => {
      this.deptOptions = this.handleTree(response.data, "deptId")
    });
  },
  watch: {
    value2(val) {
      if (val != null) {
        this.queryParams.params.startTime = val[0];
        this.queryParams.params.endTime = val[1];
      } else {
        this.queryParams.params.startTime = "";
        this.queryParams.params.endTime = "";
      }
    },
  },
  mounted() {
    // 加载体系文件的-文件类型页签
    settingDocClassList({classStatus: "1",dataType:this.queryParams.dataType,neClassType:'foreign',openPurview:true }).then(
      (response) => {
        this.docClassList = JSON.parse(JSON.stringify(response.rows))
        this.docClassTree = this.handleTree(response.rows.filter(item=>item.purview), "id", "parentClassId")
        this.docClassTree.forEach(
          (element) => {
            this.linkTypeTab.push({
              className: element.className,
              id: element.id,
            });
          }
        );
        if (this.linkTypeTab.length>0) {
          this.queryParams.docClass = this.linkTypeTab[0].id;
          this.varChangeColor = this.linkTypeTab[0].id;
          const docClass = this.docClassTree.find(item=>item.id===this.varChangeColor)
          this.changeClassType = docClass.classType
          this.handleSelectNode(docClass)

          this.getByDocClass(this.queryParams.docClass)
        }
        this.getList();
      }
    );
  },
  methods: {
    handleCloseChange(){
      this.distributePrintShow = false
      this.distributeRangeShow = false
      this.drawerShow = false
      this.getList();
    },
    closeAdd() {
      this.drawerAdd = false
    },
    getSettingDocClassTreeseList() {
      settingDocClassList({ classStatus: "1",dataType:this.queryParams.dataType }).then(
        (response) => {
          this.classLevelOptions = [];
          response.rows.forEach((element, index) => {
            response.rows[index].children = [];
          });
          this.classLevelOptions = this.handleTree(
            response.rows,
            "id",
            "parentClassId"
          );
        }
      );
    },
    normalizerFile(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.className,
        children: node.children,
      };
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    activeSearchBox() {
      this.boxClass = !this.boxClass;
    },
    handleDistribute() {
      let _this = this
      if (this.selectInfoData.length != 1) {
        this.$modal.msg(_this.$t(`doc.this_dept_select_data`));
        //this.$refs.this_department.clearSelection();
      } else {
        let row = this.selectInfoData[0]
        this.data={docId:row.docId,versionId:row.versionId,flag:'0'}
        this.distributePrintShow = true
      }
    },
    handleDistributeRange() {
      let _this = this
      if (this.selectInfoData.length != 1) {
        this.$modal.msg(_this.$t(`doc.this_dept_select_data`));
        //this.$refs.this_department.clearSelection();
      } else {
        let row = this.selectInfoData[0]
        this.data={docId:row.docId,versionId:row.versionId,flag:'0'}
        this.distributeRangeShow = true
      }
    },
    handleReview(){
      let _this = this
      _this.handleDetail({type:'review_doc_apply'})
    },
    handleChange(){
      let _this = this
      _this.handleDetail({type:'change_doc_apply'})
    },
    handleAdd() { // 新增
      let _this = this
      _this.handleDetail({type:'add_doc'})
    },
    handleDetail(row){
      let _this = this
      _this.code = row.type
      _this.data = row
      _this.drawerShow = true
    },
    handleUpdata() { // 修订
      let _this = this
      if (this.selectInfoData.length != 1) {
        this.$modal.msg(_this.$t(`doc.this_dept_select_data`));
        //this.$refs.this_department.clearSelection();
      } else {
        let row = this.selectInfoData[0]
        _this.getStatusByDocId({type:'update_doc',docId:row.docId,versionId:row.versionId,flag:'1'})
      }

    },
    handleDisuse() { // 作废
      let _this = this
      if (this.selectInfoData.length != 1) {
        this.$modal.msg(_this.$t(`doc.this_dept_select_data`));
        //this.$refs.this_department.clearSelection();
      } else {
        let row = this.selectInfoData[0]
        _this.getStatusByDocId({type:'disuse_doc',docId:row.docId,versionId:row.versionId,flag:'1'})
      }
    },
    getStatusByDocId(query){
      let _this = this
      selectStatusByDocId(query).then(res=>{
        if (res.data!==1) {
          _this.handleDetail(query)
        }else {
          _this.$modal.msg(res.msg);
        }
      })
    },
    /** 查询列表 */
    getList() {
      if (this.queryParams.docClass) {
        this.loading = true;
        listOtherDept(this.queryParams).then((response) => {
          this.postList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.value2=[]
      this.$refs.queryForm.resetFields();
      this.queryParams.params.startTime = null;
      this.queryParams.params.endTime = null;
      this.queryParams.searchValue = "";
      this.queryParams.docClass = this.varChangeColor;
      this.queryParams.docName = null;
      this.queryParams.nickName = null;
      this.handleSelectNode(this.docClassTree.find(item=>item.id===this.varChangeColor))
      this.handleQuery();
    },
    sortChange({ column, prop, order }){
      if (order==='ascending') {
        this.queryParams.orderByColumn = prop
        this.queryParams.isAsc = 'asc'
      }else if(order==='descending') {
        this.queryParams.orderByColumn = prop
        this.queryParams.isAsc = 'desc'
      }else {
        this.queryParams.orderByColumn = undefined
        this.queryParams.isAsc = undefined
      }
      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.companySelection = selection;
      this.selectInfoData = selection;
    },
    handlePreview(row, source) {
      // console.log('row====>', row)
      if (row.encryptFileId != null) {
        this.viewId = row.encryptFileId;
      } else if (row.mergeFileId != null) {
        this.viewId = row.mergeFileId;
      } else {
        this.viewId = row.fileId;
      }
      this.$refs.viewRef.handleOpenView(this.viewId, source);
      this.viewShow = true;
    },
    close() {
      this.viewShow = false;
    },
    /** 详情按钮操作 */
    handleDetails(row) {
      let _this = this
      if (JSON.parse(sessionStorage.getItem('winOpen'))) {
        window.open(process.env.VUE_APP_CONTEXT_PATH + '/#/workflow?type=detail/index'+'&docId='+row.docId+'&versionId='+row.versionId+'&flag=0&status='+this.queryParams.status)
      }else {
        _this.handleDetail({type:'detail/index',docId:row.docId,versionId:row.versionId,flag:'0',status:this.queryParams.status})
      }
    },
    /** 借阅按钮操作 */
    handleBorrow(row, type) {
      let _this = this
      // const postId = row.postId || this.ids;
      _this.title = _this.$t(`doc.this_dept_borrow`);
      if (type === 2) {
        _this.companySelection = [row];
      }
      if (_this.companySelection.length <= 0) {
        _this.$message.warning(_this.$t(`doc.this_dept_select_file`));
        return;
      }

      let array = _this.companySelection;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        if (element.status == 2) {
          _this.$message.warning(_this.$t(`doc.this_dept_file_lost_efficacy`));
          return;
        }
      }

      _this.companySelection.forEach((element) => {
        if (
          element.deptId != _this.userInfo.dept.deptId &&
          element.trainDept.indexOf(_this.userInfo.dept.deptId) < 0
        ) {
          _this.borrowOpen = true;
        } else {
          _this.$message.warning(_this.$t(`doc.this_dept_authority_text`));
          return;
        }
      });
    },

    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.postId != undefined) {
            updatePost(this.form).then((response) => {
              this.$modal.msgSuccess(this.$t(`doc.this_dept_update_success`));
              this.open = false;
              this.getList();
            });
          } else {
            addPost(this.form).then((response) => {
              this.$modal.msgSuccess(this.$t(`doc.this_dept_add_success`));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm(this.$t(`doc.this_dept_is_remove_code`) + postIds + this.$t(`file_set.signature_text1`))
        .then(function () {
          return delPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t(`doc.this_dept_remove_success`));
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$set(this.queryParams,'exportName',"导出外本部门文件")
      this.download(
        "/process/version/export/other/dept",
        {
          ...this.queryParams,
        },
        this.linkTypeTab[this.activeName].className + `_${new Date().getTime()}.xlsx`
      );
    },
    closeDrawerDetails() {
      this.drawerDetails = false;
    },
    changeDrawer(v) {
      this.drawer = v;
    },
    handleCloseBorrow() {
      this.borrowOpen = false;
      this.getList();
    },
    state(tab) {
      let t = tab.index;
      let index = this.linkTypeTab[t].id;
      this.varChangeColor = index;
      const docClass = this.docClassTree.find(item=>item.id===index)
      this.changeClassType = docClass.classType
      this.handleSelectNode(docClass)
      this.queryParams.docClass = index;
      this.getByDocClass(this.queryParams.docClass)
      this.getList();
    },
    handleSelectNode(node){
      let docClassList = []
      this.getChildrenList(docClassList,node,'id',)
      this.queryParams.docClassList = docClassList
    },
    getChildrenList(docClassList,node,key){
      if (node.children && node.children.length) {
        node.children.forEach(item=>{
          this.getChildrenList(docClassList,item,key);
        })
      }else {
        docClassList.push(node[key])
      }
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.p2 == this.$t(`doc.this_dept_lost_efficacy`)) {
        //console.log(row.p2);
        return "yidu";
      }
      return "";
    },
    /** 增发按钮操作 */
    handleAddReissue(e) {
      if (this.companySelection.length <= 0) {
        this.$message.warning(this.$t(`doc.this_dept_select_extra_file`));
        return;
      }
      let array = this.companySelection;

      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        if (element.status == 2) {
          this.$message.warning(this.$t(`doc.this_dept_file_lost_efficacy`));
          return;
        }
      }

      let length = 0;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        distributeLogcheckSign({
          docId: element.docId,
          versionId: element.id,
        }).then((res) => {
          if (res.data != true) {
            this.$message.warning(res.msg);
          } else {
            length = length + 1;
            //debugger;
            if (length == array.length) {
              this.addDrawer = true;
              this.title = this.$t(`doc.this_dept_additional_issuance`);
            }
          }
        });
      }
    },
    chinldClose() {
      this.addDrawer = false;
      this.drawerDetails = false;
      this.getList();
    },
    formatterDocClass(row, column, cellValue, index){
      let _this = this
      let item = _this.docClassList.find(item=>item.id===cellValue)
      return item?item.className:cellValue
    },
    getByDocClass(docClass) {
      let _this = this
      _this.loading = true
      getInfoBy({type:'formShow',docClass:docClass}).then(async res => {
        _this.isShowPart = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            _this.isShowPart = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'formCustomerShow',docClass:docClass}).then(async res => {
        _this.isCustomerShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            _this.isCustomerShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'formDeviceShow',docClass:docClass}).then(async res => {
        _this.isDeviceShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            _this.isDeviceShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'internalDocIdShow',docClass:docClass}).then(async res => {
        _this.internalDocIdShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            _this.internalDocIdShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'formProductVersionShow',docClass:docClass}).then(async res => {
        _this.isProductVersionShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            _this.isProductVersionShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })

      getInfoBy({type:'projectCodeShow',docClass:docClass}).then(async res => {
        _this.projectCodeShow = false
        if (res.data) {
          if(res.data.openFlag== 'Y'){
            _this.projectCodeShow = true
          }
        }
        _this.loading = false
      }).catch(err => {
        console.log(err)
        _this.loading = false
      })
    },
    getDictLabel(dictValue) {
      const dictItem = this.dict.type.tenant_list.find(item => item.value === dictValue);
      return dictItem ? this.dictLanguage(dictItem) : dictValue;
    },
  },
};
</script>
