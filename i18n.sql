INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)VALUES (UUID(), 'dicts.regulation_standard_status_status_current', '现行', 'zh', 'front', 'CAM', 'admin','2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)VALUES (UUID(), 'dicts.regulation_standard_status_status_current', 'Current', 'en', 'front', 'CAM', 'admin','2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)VALUES (UUID(), 'dicts.regulation_standard_status_status_upcoming', '即将实施', 'zh', 'front', 'CAM', 'admin','2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)VALUES (UUID(), 'dicts.regulation_standard_status_status_upcoming', 'To be Implemented', 'en', 'front', 'CAM', 'admin','2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'doc.places_select', 'Please select', 'en', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'doc.places_select', '请选择', 'zh', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dict.file_purpose_type', 'File Purpose', 'en', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dict.file_purpose_type', '文件用途', 'zh', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dicts.file_purpose_type_mass_production_test', '产品量产测试', 'zh', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dicts.file_purpose_type_mass_production_test', 'Mass Production Test', 'en', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dicts.file_purpose_type_product_sampling', 'Product Sampling', 'en', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dicts.file_purpose_type_product_sampling', '产品打样', 'zh', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dicts.file_purpose_type_product_trial_mold', '产品试模', 'zh', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dicts.file_purpose_type_product_trial_mold', 'Product Trial Mold', 'en', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dicts.file_purpose_type_trial_production_test', '产品试产测试', 'zh', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time) VALUES (UUID(), 'dicts.file_purpose_type_trial_production_test', 'Trial Production Test', 'en', 'front', 'CAM', 'luob', '2025-07-18 16:17:52', 'luob', '2025-07-18 16:17:52');
-- 字典类型国际化 - 法规/标准状态
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
    (UUID(), 'dict.regulation_standard_status', '法规/标准状态', 'zh', 'front', 'CAM', 'admin', '2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50'),
    (UUID(), 'dict.regulation_standard_status', 'Regulation/Standard Status', 'en', 'front', 'CAM', 'admin', '2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');
-- 字典类型国际化 - 过期状态
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'dict.expired_status', '过期状态', 'zh', 'front', 'CAM', 'adminmh', '2025-07-25 09:15:05', 'adminmh', '2025-07-25 09:15:05'),
  (UUID(), 'dict.expired_status', 'Expiry Status', 'en', 'front', 'CAM', 'adminmh', '2025-07-25 09:15:05', 'adminmh', '2025-07-25 09:15:05');

-- 字典数据国际化 - 过期状态选项
-- 正常状态
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'dicts.expired_status_normal', '正常', 'zh', 'front', 'CAM', 'adminmh', '2025-07-25 09:15:36', 'adminmh', '2025-07-25 09:16:06'),
  (UUID(), 'dicts.expired_status_normal', 'Normal', 'en', 'front', 'CAM', 'adminmh', '2025-07-25 09:15:36', 'adminmh', '2025-07-25 09:16:06');

-- 失效状态
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'dicts.expired_status_failure', '失效', 'zh', 'front', 'CAM', 'adminmh', '2025-07-25 09:15:58', 'adminmh', '2025-07-25 09:15:58'),
  (UUID(), 'dicts.expired_status_failure', 'Expired', 'en', 'front', 'CAM', 'adminmh', '2025-07-25 09:15:58', 'adminmh', '2025-07-25 09:15:58');
-- 字段标签国际化 - 法规/标准发布日期
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
    (UUID(), 'field.regulation_publish_date', '法规/标准发布日期', 'zh', 'front', 'CAM', 'admin', '2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50'),
    (UUID(), 'field.regulation_publish_date', 'Regulation/Standard Publish Date', 'en', 'front', 'CAM', 'admin', '2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');

-- 字段标签国际化 - 法规/标准实施日期
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
    (UUID(), 'field.regulation_implement_date', '法规/标准实施日期', 'zh', 'front', 'CAM', 'admin', '2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50'),
    (UUID(), 'field.regulation_implement_date', 'Regulation/Standard Implementation Date', 'en', 'front', 'CAM', 'admin', '2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');

-- workflow.process_node 流程环节
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'workflow.process_node', '流程环节', 'zh', 'front', 'CAM', 'admin','2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'workflow.process_node', 'Process Node', 'en', 'front', 'CAM', 'admin','2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');

-- workflow.executor 执行人员
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'workflow.executor', '执行人员', 'zh', 'front', 'CAM', 'admin','2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'workflow.executor', 'Executor', 'en', 'front', 'CAM', 'admin','2025-07-21 15:02:50', 'admin', '2025-07-21 15:02:50');


-- 为 doc.review_record 添加国际化支持
-- 中文版本
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'doc.review_record', '评审记录', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 英文版本
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'doc.review_record', 'Review Record', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());



-- 为 doc.download_resume 添加国际化支持
-- 中文版本
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'doc.download_resume', '下载履历', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 英文版本
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'doc.download_resume', 'Download History', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());


-- 为 downloadResume 方法添加国际化支持

-- doc.this_dept_no_apply_id (没有申请ID)
-- 中文版本
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'doc.this_dept_no_apply_id', '没有申请ID', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 英文版本
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'doc.this_dept_no_apply_id', 'No Application ID', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- doc.this_dept_operation_succ (操作成功)
-- 中文版本
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'doc.this_dept_operation_succ', '操作成功', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 英文版本
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'doc.this_dept_operation_succ', 'Operation Successful', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- doc.this_dept_operation_fail (操作失败)
-- 中文版本
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'doc.this_dept_operation_fail', '操作失败', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 英文版本
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'doc.this_dept_operation_fail', 'Operation Failed', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());


-- 为 doc.download_source_file 添加国际化支持
-- 中文版本
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'doc.download_source_file', '源文件下载', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 英文版本
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES (UUID(), 'doc.download_source_file', 'Download Source File', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());



-- 字典类型国际化 - 文件类型
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'dict.file_type', '文件类型', 'zh', 'front', 'CAM', 'adminmh', '2025-07-25 09:15:05', 'adminmh', '2025-07-25 09:15:05'),
  (UUID(), 'dict.file_type', 'File Type', 'en', 'front', 'CAM', 'adminmh', '2025-07-25 09:15:05', 'adminmh', '2025-07-25 09:15:05');

-- 字典数据国际化 - 文件类型选项
-- 工程样板
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'dicts.file_type_G', '工程样板', 'zh', 'front', 'CAM', 'adminmh', '2025-07-25 09:15:36', 'adminmh', '2025-07-25 09:16:06'),
  (UUID(), 'dicts.file_type_G', 'Engineering Template', 'en', 'front', 'CAM', 'adminmh', '2025-07-25 09:15:36', 'adminmh', '2025-07-25 09:16:06');

-- 临时文件
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'dicts.file_type_Y', '临时文件', 'zh', 'front', 'CAM', 'adminmh', '2025-07-25 09:15:58', 'adminmh', '2025-07-25 09:15:58'),
  (UUID(), 'dicts.file_type_Y', 'Temporary File', 'en', 'front', 'CAM', 'adminmh', '2025-07-25 09:15:58', 'adminmh', '2025-07-25 09:15:58');

-- 无
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'dicts.file_type_N', '无', 'zh', 'front', 'CAM', 'adminmh', '2025-07-25 09:15:58', 'adminmh', '2025-07-25 09:15:58'),
  (UUID(), 'dicts.file_type_N', 'None', 'en', 'front', 'CAM', 'adminmh', '2025-07-25 09:15:58', 'adminmh', '2025-07-25 09:15:58');


INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'watermark_type.shelf_life', '过期日期', 'zh', 'front', 'CAM', 'adminmh', '2025-07-25 09:15:58', 'adminmh', '2025-07-25 09:15:58'),
  (UUID(), 'watermark_type.shelf_life', 'Expiration date', 'en', 'front', 'CAM', 'adminmh', '2025-07-25 09:15:58', 'adminmh', '2025-07-25 09:15:58');

-- 项目人员配置模块国际化SQL
-- 页面标题和基础标签
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.title', '项目人员配置', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.title', 'Project Personnel Configuration', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 字段标签
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.project_code', '项目编码', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.project_code', 'Project Code', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.users', '人员配置', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.users', 'Personnel Configuration', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.create_by', '创建者', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.create_by', 'Created By', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.create_time', '创建时间', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.create_time', 'Create Time', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.update_by', '更新者', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.update_by', 'Updated By', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.update_time', '更新时间', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.update_time', 'Update Time', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 操作按钮
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.add', '新增', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.add', 'Add', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.edit', '修改', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.edit', 'Edit', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.delete', '删除', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.delete', 'Delete', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.export', '导出', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.export', 'Export', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 对话框标题
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.add_title', '添加项目人员配置', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.add_title', 'Add Project Personnel Configuration', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.edit_title', '修改项目人员配置', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.edit_title', 'Edit Project Personnel Configuration', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 占位符文本
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.project_code_placeholder', '请选择项目编码', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.project_code_placeholder', 'Please select project code', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.users_placeholder', '请输入人员配置信息', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.users_placeholder', 'Please enter personnel configuration information', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 验证消息
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.project_code_required', '项目编码不能为空', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.project_code_required', 'Project code cannot be empty', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.users_required', '人员配置不能为空', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.users_required', 'Personnel configuration cannot be empty', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 操作成功消息
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.add_success', '新增成功', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.add_success', 'Added successfully', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.update_success', '修改成功', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.update_success', 'Updated successfully', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.delete_success', '删除成功', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.delete_success', 'Deleted successfully', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 确认消息
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES
  (UUID(), 'project_person.delete_confirm', '是否确认删除选中的项目人员配置数据项？', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'project_person.delete_confirm', 'Are you sure you want to delete the selected project personnel configuration data?', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());
