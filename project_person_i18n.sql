-- 项目人员配置模块国际化SQL
-- 页面标题和基础标签
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.title', '项目人员配置', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.title', 'Project Personnel Configuration', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 字段标签
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.project_code', '项目编码', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.project_code', 'Project Code', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.users', '人员配置', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.users', 'Personnel Configuration', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.create_by', '创建者', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.create_by', 'Created By', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.create_time', '创建时间', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.create_time', 'Create Time', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.update_by', '更新者', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.update_by', 'Updated By', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.update_time', '更新时间', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.update_time', 'Update Time', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 操作按钮
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.add', '新增', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.add', 'Add', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.edit', '修改', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.edit', 'Edit', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.delete', '删除', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.delete', 'Delete', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.export', '导出', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.export', 'Export', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 对话框标题
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.add_title', '添加项目人员配置', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.add_title', 'Add Project Personnel Configuration', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.edit_title', '修改项目人员配置', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.edit_title', 'Edit Project Personnel Configuration', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 占位符文本
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.project_code_placeholder', '请选择项目编码', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.project_code_placeholder', 'Please select project code', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.users_placeholder', '请输入人员配置信息', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.users_placeholder', 'Please enter personnel configuration information', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 验证消息
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.project_code_required', '项目编码不能为空', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.project_code_required', 'Project code cannot be empty', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.users_required', '人员配置不能为空', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.users_required', 'Personnel configuration cannot be empty', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 操作成功消息
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.add_success', '新增成功', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.add_success', 'Added successfully', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.update_success', '修改成功', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.update_success', 'Updated successfully', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.delete_success', '删除成功', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.delete_success', 'Deleted successfully', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());

-- 确认消息
INSERT INTO t_international (id, code, name, lang, type, tenant_id, create_by, create_time, update_by, update_time)
VALUES 
  (UUID(), 'setting.project_person.delete_confirm', '是否确认删除选中的项目人员配置数据项？', 'zh', 'front', 'CAM', 'admin', NOW(), 'admin', NOW()),
  (UUID(), 'setting.project_person.delete_confirm', 'Are you sure you want to delete the selected project personnel configuration data?', 'en', 'front', 'CAM', 'admin', NOW(), 'admin', NOW());
